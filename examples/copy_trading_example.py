#!/usr/bin/env python3
"""
👥 Exemple d'utilisation du bot de copy trading
Démontre l'analyse de wallets et la réplication de trades
"""

import asyncio
import json
import time
from pathlib import Path
import sys

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from bots.copy_trading.copy_trading_bot import CopyTradingBot, CopyTradingBotConfig
from bots.copy_trading.wallet_analyzer import WalletAnalyzer, WalletPerformance
from bots.copy_trading.signal_detector import SignalDetector, SignalConfig
from bots.copy_trading.copy_executor import CopyExecutor, CopyTradeConfig
from logging_system.central_logger import central_logger, LogCategory

def create_example_config() -> dict:
    """Crée une configuration d'exemple"""
    return {
        # Wallets à suivre (exemples fictifs)
        "tracked_wallets": [
            {
                "address": "******************************************",
                "label": "Whale Trader Pro",
                "min_trade_size": 5000.0
            },
            {
                "address": "******************************************",
                "label": "DeFi Expert",
                "min_trade_size": 2000.0
            },
            {
                "address": "******************************************",
                "label": "Smart Money",
                "min_trade_size": 3000.0
            }
        ],
        
        # Configuration de détection
        "min_signal_confidence": 0.75,
        "required_urgency_levels": ["high", "medium"],
        
        # Configuration d'exécution
        "default_copy_percentage": 1.5,
        "max_copy_percentage": 3.0,
        "max_concurrent_copies": 5,
        
        # Gestion des risques
        "enable_stop_loss": True,
        "default_stop_loss_percentage": 8.0,
        "enable_take_profit": True,
        "default_take_profit_percentage": 15.0,
        
        # Limites
        "max_daily_copy_amount": 20000.0,
        "max_copies_per_wallet": 2,
        
        # Monitoring
        "analysis_interval_minutes": 30,
        "enable_notifications": True,
        "notification_min_profit": 50.0
    }

async def test_wallet_analyzer():
    """Teste l'analyseur de wallets"""
    print("\n🔍 Test de l'analyseur de wallets")
    print("="*50)
    
    try:
        analyzer = WalletAnalyzer()
        
        # Ajouter des wallets de test
        test_wallets = [
            ("******************************************", "Whale Trader"),
            ("******************************************", "DeFi Expert"),
            ("******************************************", "Smart Money")
        ]
        
        print("📊 Ajout des wallets de test...")
        for address, label in test_wallets:
            success = analyzer.add_wallet_to_track(address, label, min_trade_size=1000.0)
            print(f"   {'✅' if success else '❌'} {label}: {address[:10]}...")
        
        # Analyser les wallets
        print("\n🔍 Analyse des performances...")
        performances = []
        
        for address, label in test_wallets:
            print(f"\n📈 Analyse de {label}:")
            performance = await analyzer.analyze_wallet_transactions(address, days_back=30)
            
            if performance:
                performances.append(performance)
                print(f"   📊 Total trades: {performance.total_trades}")
                print(f"   🎯 Win rate: {performance.win_rate:.1f}%")
                print(f"   💰 Total P&L: ${performance.total_pnl:.2f}")
                print(f"   📏 Trade moyen: ${performance.average_trade_size:.0f}")
                print(f"   🎲 Score confiance: {performance.consistency_score:.2f}")
                print(f"   ⚠️ Score risque: {performance.risk_score:.2f}")
                print(f"   🏆 Tokens favoris: {list(performance.favorite_tokens.keys())[:3]}")
            else:
                print(f"   ❌ Impossible d'analyser {label}")
        
        # Top performers
        print(f"\n🏆 Top performers:")
        top_performers = analyzer.get_top_performers(limit=3)
        
        for i, perf in enumerate(top_performers, 1):
            print(f"   {i}. {perf.wallet_address[:10]}... - P&L: ${perf.total_pnl:.2f} - Win: {perf.win_rate:.1f}%")
        
        # Statistiques de l'analyseur
        stats = analyzer.get_analyzer_statistics()
        print(f"\n📈 Statistiques analyseur:")
        print(f"   Wallets suivis: {stats['total_wallets_tracked']}")
        print(f"   Wallets analysés: {stats['total_wallets_analyzed']}")
        print(f"   Performances calculées: {stats['wallets_with_performance']}")
        
        print("\n✅ Test analyseur réussi!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur test analyseur: {e}")
        return False

async def test_signal_detector():
    """Teste le détecteur de signaux"""
    print("\n📡 Test du détecteur de signaux")
    print("="*50)
    
    try:
        # Créer l'analyseur et le détecteur
        analyzer = WalletAnalyzer()
        config = SignalConfig(
            min_trade_size_usd=500.0,
            min_wallet_performance_score=0.6
        )
        detector = SignalDetector(analyzer, config)
        
        # Ajouter et analyser des wallets
        test_wallets = [
            "******************************************",
            "******************************************"
        ]
        
        print("🔍 Préparation des wallets...")
        for address in test_wallets:
            analyzer.add_wallet_to_track(address, f"Wallet {address[:6]}")
            await analyzer.analyze_wallet_transactions(address)
        
        # Simuler le monitoring pendant quelques cycles
        print("📡 Simulation du monitoring de signaux...")
        
        # Créer une tâche de monitoring
        monitor_task = asyncio.create_task(detector.monitor_wallets())
        
        # Laisser tourner pendant 10 secondes
        await asyncio.sleep(10)
        
        # Arrêter le monitoring
        monitor_task.cancel()
        
        try:
            await monitor_task
        except asyncio.CancelledError:
            pass
        
        # Vérifier les signaux détectés
        active_signals = detector.get_active_signals()
        print(f"\n📊 Signaux détectés: {len(active_signals)}")
        
        for i, signal in enumerate(active_signals[:3], 1):
            print(f"\n💡 Signal #{i}:")
            print(f"   🪙 Token: {signal.token_symbol}")
            print(f"   📈 Action: {signal.action}")
            print(f"   💰 Montant: ${signal.amount_usd:.0f}")
            print(f"   🎯 Confiance: {signal.confidence_score:.2f}")
            print(f"   ⚡ Urgence: {signal.urgency}")
            print(f"   👤 Wallet: {signal.wallet_address[:10]}...")
            print(f"   💡 Raisons: {', '.join(signal.reasoning[:2])}")
        
        # Statistiques du détecteur
        stats = detector.get_signal_statistics()
        print(f"\n📈 Statistiques détecteur:")
        print(f"   Total signaux: {stats['total_signals_detected']}")
        print(f"   Signaux actifs: {stats['active_signals_count']}")
        
        if stats['top_signal_tokens']:
            print(f"   Top tokens: {[t[0] for t in stats['top_signal_tokens'][:3]]}")
        
        print("\n✅ Test détecteur réussi!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur test détecteur: {e}")
        return False

async def test_copy_executor():
    """Teste l'exécuteur de copy trading"""
    print("\n⚡ Test de l'exécuteur de copy trading")
    print("="*50)
    
    try:
        # Créer les composants
        analyzer = WalletAnalyzer()
        signal_detector = SignalDetector(analyzer)
        
        config = CopyTradeConfig(
            default_copy_percentage=1.0,
            max_concurrent_copies=3,
            min_signal_confidence=0.6
        )
        executor = CopyExecutor(signal_detector, config)
        
        # Préparer un wallet avec de bonnes performances
        test_wallet = "******************************************"
        analyzer.add_wallet_to_track(test_wallet, "Test Wallet")
        performance = await analyzer.analyze_wallet_transactions(test_wallet)
        
        if not performance:
            print("❌ Impossible de créer les performances de test")
            return False
        
        print(f"📊 Wallet de test préparé:")
        print(f"   Win rate: {performance.win_rate:.1f}%")
        print(f"   P&L total: ${performance.total_pnl:.2f}")
        
        # Créer un signal de test manuellement
        from bots.copy_trading.signal_detector import WalletSignal
        from datetime import datetime
        
        test_signal = WalletSignal(
            wallet_address=test_wallet,
            timestamp=datetime.now(),
            action="buy",
            token_symbol="WETH",
            token_address="******************************************",
            amount_usd=2000.0,
            confidence_score=0.85,
            urgency="high",
            reasoning=["Wallet très performant", "Trade important"],
            wallet_performance=performance,
            recent_trades=[],
            market_context={}
        )
        
        print(f"\n💡 Signal de test créé:")
        print(f"   Action: {test_signal.action}")
        print(f"   Token: {test_signal.token_symbol}")
        print(f"   Montant: ${test_signal.amount_usd}")
        print(f"   Confiance: {test_signal.confidence_score}")
        
        # Tester l'exécution
        print(f"\n⚡ Test d'exécution...")
        copy_trade = await executor._execute_copy_trade(test_signal)
        
        if copy_trade:
            print(f"✅ Copy trade exécuté!")
            print(f"   ID: {copy_trade.id}")
            print(f"   Statut: {copy_trade.status.value}")
            print(f"   Montant cible: ${copy_trade.target_amount_usd:.2f}")
            print(f"   Montant réel: ${copy_trade.actual_amount_usd:.2f}")
            print(f"   Prix d'entrée: ${copy_trade.entry_price:.2f}")
            print(f"   Slippage: {copy_trade.slippage_percentage:.3f}%")
            print(f"   Frais gas: ${copy_trade.gas_fees_usd:.2f}")
            
            if copy_trade.stop_loss_price:
                print(f"   Stop loss: ${copy_trade.stop_loss_price:.2f}")
            if copy_trade.take_profit_price:
                print(f"   Take profit: ${copy_trade.take_profit_price:.2f}")
        else:
            print("❌ Échec de l'exécution")
            return False
        
        # Simuler la gestion de position
        print(f"\n📊 Simulation de gestion de position...")
        await executor._update_position_pnl(copy_trade)
        await executor._check_exit_conditions(copy_trade)
        
        print(f"   Prix actuel: ${copy_trade.current_price:.2f}")
        print(f"   P&L non réalisé: ${copy_trade.unrealized_pnl:.2f}")
        
        # Statistiques de l'exécuteur
        stats = executor.get_performance_statistics()
        print(f"\n📈 Statistiques exécuteur:")
        print(f"   Total copies: {stats['total_copies_executed']}")
        print(f"   Taux de réussite: {stats['success_rate']:.1f}%")
        print(f"   Volume total: ${stats['total_copy_volume']:.2f}")
        print(f"   P&L total: ${stats['total_realized_pnl']:.2f}")
        
        print("\n✅ Test exécuteur réussi!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur test exécuteur: {e}")
        return False

async def test_full_bot():
    """Teste le bot complet"""
    print("\n🤖 Test du bot complet")
    print("="*50)
    
    try:
        # Configuration du bot
        config = CopyTradingBotConfig(
            tracked_wallets=[
                {
                    "address": "******************************************",
                    "label": "Test Whale",
                    "min_trade_size": 1000.0
                }
            ],
            default_copy_percentage=1.0,
            max_concurrent_copies=2,
            enable_notifications=False  # Désactiver pour le test
        )
        
        # Créer le bot
        bot = CopyTradingBot(config)
        
        print("🚀 Bot créé avec succès")
        print(f"📊 Configuration:")
        print(f"   Wallets suivis: {len(config.tracked_wallets)}")
        print(f"   Copy %: {config.default_copy_percentage}%")
        print(f"   Copies max: {config.max_concurrent_copies}")
        print(f"   Stop loss: {config.enable_stop_loss}")
        
        # Tester le statut
        status = bot.get_status()
        print(f"\n📋 Statut initial:")
        print(f"   En cours: {status['is_running']}")
        print(f"   Wallets: {status['tracked_wallets_count']}")
        print(f"   Copies quotidiennes: {status['daily_copies']}")
        
        # Tester l'ajout d'un wallet
        print(f"\n➕ Test ajout de wallet...")
        success = await bot.add_wallet_to_follow(
            "******************************************",
            "Nouveau Trader",
            2000.0
        )
        print(f"   {'✅' if success else '❌'} Wallet ajouté")
        
        # Tester les performances des wallets
        print(f"\n📊 Performances des wallets suivis:")
        performances = bot.get_tracked_wallets_performance()
        
        for i, perf in enumerate(performances, 1):
            print(f"   {i}. {perf.get('label', 'Unknown')}:")
            print(f"      Win rate: {perf.get('win_rate', 0):.1f}%")
            print(f"      P&L: ${perf.get('total_pnl', 0):.2f}")
            print(f"      Trades: {perf.get('total_trades', 0)}")
        
        # Simulation courte du bot
        print(f"\n⏳ Simulation du bot (15 secondes)...")
        
        # Créer une tâche pour le bot (sans le démarrer complètement)
        # On teste juste l'initialisation et quelques cycles
        await bot._initialize_tracked_wallets()
        
        # Simuler quelques cycles de monitoring
        for i in range(3):
            await bot._periodic_wallet_analysis()
            await bot._monitor_performance()
            await asyncio.sleep(2)
            print(f"   Cycle {i+1}/3 terminé")
        
        # Rapport final
        final_status = bot.get_status()
        print(f"\n📊 Résultats de la simulation:")
        print(f"   Wallets suivis: {final_status['tracked_wallets_count']}")
        print(f"   Positions actives: {final_status['active_positions']}")
        print(f"   Erreurs: {final_status['consecutive_errors']}")
        
        print("\n✅ Test du bot complet réussi!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur test bot: {e}")
        return False

async def demo_wallet_comparison():
    """Démontre la comparaison de wallets"""
    print("\n📊 Démonstration de comparaison de wallets")
    print("="*50)
    
    try:
        analyzer = WalletAnalyzer()
        
        # Créer plusieurs wallets avec des profils différents
        wallets = [
            ("******************************************", "Conservative Trader"),
            ("******************************************", "Aggressive Trader"),
            ("******************************************", "Balanced Trader")
        ]
        
        print("📈 Analyse comparative des wallets:")
        print("-" * 60)
        
        performances = []
        for address, label in wallets:
            analyzer.add_wallet_to_track(address, label)
            perf = await analyzer.analyze_wallet_transactions(address)
            if perf:
                performances.append((label, perf))
        
        # Afficher la comparaison
        print(f"{'Wallet':<20} {'Win Rate':<10} {'P&L':<12} {'Risk':<8} {'Consistency':<12}")
        print("-" * 60)
        
        for label, perf in performances:
            print(f"{label:<20} {perf.win_rate:>7.1f}% "
                  f"${perf.total_pnl:>9.2f} {perf.risk_score:>6.2f} "
                  f"{perf.consistency_score:>10.2f}")
        
        # Recommandations
        print(f"\n💡 Recommandations:")
        best_performer = max(performances, key=lambda x: x[1].total_pnl)
        most_consistent = max(performances, key=lambda x: x[1].consistency_score)
        lowest_risk = min(performances, key=lambda x: x[1].risk_score)
        
        print(f"   🏆 Meilleur P&L: {best_performer[0]}")
        print(f"   🎯 Plus consistant: {most_consistent[0]}")
        print(f"   🛡️ Moins risqué: {lowest_risk[0]}")
        
        print("\n✅ Démonstration terminée!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur démonstration: {e}")
        return False

async def main():
    """Fonction principale des exemples"""
    print("👥 EXEMPLES BOT DE COPY TRADING")
    print("="*80)
    
    try:
        print("🧪 Démarrage des tests...")
        
        # Test 1: Analyseur de wallets
        if not await test_wallet_analyzer():
            print("❌ Test analyseur échoué")
            return
        
        # Test 2: Détecteur de signaux
        if not await test_signal_detector():
            print("❌ Test détecteur échoué")
            return
        
        # Test 3: Exécuteur de copy trading
        if not await test_copy_executor():
            print("❌ Test exécuteur échoué")
            return
        
        # Test 4: Bot complet
        if not await test_full_bot():
            print("❌ Test bot échoué")
            return
        
        # Démonstration 5: Comparaison de wallets
        if not await demo_wallet_comparison():
            print("❌ Démonstration échouée")
            return
        
        print("\n" + "="*80)
        print("✅ Tous les tests ont réussi!")
        print("💡 Le bot de copy trading est prêt à être utilisé")
        
        # Afficher la configuration recommandée
        print("\n📋 Configuration recommandée pour la production:")
        config = create_example_config()
        print(json.dumps(config, indent=2))
        
        print("\n⚠️ Rappels importants:")
        print("• Analysez toujours les wallets avant de les suivre")
        print("• Commencez avec de petits pourcentages de copy")
        print("• Utilisez des stop-loss pour limiter les pertes")
        print("• Surveillez régulièrement les performances")
        print("• Diversifiez les wallets suivis")
        print("• Vérifiez la légitimité des wallets (pas de wash trading)")
        
    except KeyboardInterrupt:
        print("\n🛑 Tests interrompus par l'utilisateur")
    except Exception as e:
        print(f"\n❌ Erreur inattendue: {e}")
        central_logger.log(
            level="ERROR",
            message=f"Erreur dans les exemples: {e}",
            category=LogCategory.ERROR
        )

if __name__ == "__main__":
    asyncio.run(main())
