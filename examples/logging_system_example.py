#!/usr/bin/env python3
"""
📝 Exemple d'utilisation du système de logging avancé
Démontre toutes les fonctionnalités de logging, audit et analytics
"""

import sys
import time
import random
from datetime import datetime, timedelta
from pathlib import Path

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from logging_system.central_logger import central_logger
from logging_system.advanced_logger import LogLevel, LogCategory
from logging_system.audit_trail import AuditAction, AuditSeverity
from logging_system.performance_analytics import performance_analytics

def example_basic_logging():
    """Exemple de logging basique"""
    print("📝 EXEMPLE 1: Logging basique")
    print("="*60)
    
    # Logs de différents niveaux
    central_logger.log(LogLevel.INFO, "Démarrage de l'exemple", LogCategory.SYSTEM)
    central_logger.log(LogLevel.DEBUG, "Information de debug", LogCategory.SYSTEM)
    central_logger.log(LogLevel.WARNING, "Avertissement important", LogCategory.SYSTEM)
    central_logger.log(LogLevel.ERROR, "Erreur simulée", LogCategory.ERROR)
    
    # Logs avec données supplémentaires
    central_logger.log(
        LogLevel.INFO, 
        "Connexion utilisateur", 
        LogCategory.AUDIT,
        user_id="user123",
        ip_address="*************",
        user_agent="Mozilla/5.0"
    )
    
    print("✅ Logs basiques créés")

def example_trading_logs():
    """Exemple de logs de trading avec audit automatique"""
    print("\n💹 EXEMPLE 2: Logs de trading")
    print("="*60)
    
    # Simulation d'un trade
    symbol = "BTC/USDT"
    side = "buy"
    quantity = 0.001
    price = 50000
    trade_id = "trade_123456"
    
    # Log d'exécution de trade (avec audit automatique)
    central_logger.trade_executed(
        message=f"Trade exécuté: {side} {quantity} {symbol} @ {price}",
        symbol=symbol,
        side=side,
        quantity=quantity,
        price=price,
        trade_id=trade_id,
        exchange="binance",
        strategy="grid_trading"
    )
    
    # Ouverture de position
    central_logger.position_opened(
        message=f"Position ouverte: {symbol}",
        symbol=symbol,
        position_type="LONG",
        quantity=quantity,
        entry_price=price,
        stop_loss=47500,
        take_profit=55000
    )
    
    # Simulation d'une fermeture profitable
    exit_price = 52000
    pnl = (exit_price - price) * quantity
    
    central_logger.position_closed(
        message=f"Position fermée: {symbol} avec profit",
        symbol=symbol,
        quantity=quantity,
        exit_price=exit_price,
        pnl=pnl,
        duration_minutes=45
    )
    
    print(f"✅ Logs de trading créés (P&L: {pnl:.2f})")

def example_strategy_logs():
    """Exemple de logs de stratégie"""
    print("\n🎯 EXEMPLE 3: Logs de stratégie")
    print("="*60)
    
    strategy_name = "Grid Trading v2.0"
    
    # Démarrage de stratégie
    central_logger.strategy_action(
        message=f"Démarrage de la stratégie {strategy_name}",
        strategy_name=strategy_name,
        action="start",
        symbol="ETH/USDT",
        grid_size=10,
        grid_spacing=0.5,
        capital_allocated=1000
    )
    
    # Logs d'activité de la stratégie
    for i in range(3):
        central_logger.log(
            LogLevel.INFO,
            f"Signal généré par {strategy_name}: {'BUY' if i % 2 == 0 else 'SELL'}",
            LogCategory.STRATEGY,
            strategy_name=strategy_name,
            signal_type="BUY" if i % 2 == 0 else "SELL",
            confidence=random.uniform(0.7, 0.95),
            price_level=3000 + random.uniform(-50, 50)
        )
        time.sleep(0.1)
    
    # Arrêt de stratégie
    central_logger.strategy_action(
        message=f"Arrêt de la stratégie {strategy_name}",
        strategy_name=strategy_name,
        action="stop",
        reason="manual_stop",
        total_trades=15,
        total_pnl=125.50
    )
    
    print("✅ Logs de stratégie créés")

def example_performance_monitoring():
    """Exemple de monitoring de performance"""
    print("\n📊 EXEMPLE 4: Monitoring de performance")
    print("="*60)
    
    # Enregistrer des métriques custom
    central_logger.performance_metric(
        metric_name="api_response_time",
        value=0.125,
        unit="seconds",
        message="Temps de réponse API Binance"
    )
    
    central_logger.performance_metric(
        metric_name="order_execution_time",
        value=0.045,
        unit="seconds",
        endpoint="/api/v3/order"
    )
    
    # Utiliser le timer de performance
    with central_logger.timed_operation("complex_calculation", LogCategory.PERFORMANCE):
        # Simulation d'une opération complexe
        time.sleep(0.2)
        result = sum(i**2 for i in range(1000))
    
    # Enregistrer des métriques business
    central_logger.performance_metric(
        metric_name="portfolio_value",
        value=10500.75,
        unit="USDT",
        message="Valeur actuelle du portefeuille"
    )
    
    central_logger.performance_metric(
        metric_name="daily_return",
        value=2.5,
        unit="percent",
        message="Rendement quotidien"
    )
    
    print("✅ Métriques de performance enregistrées")

def example_risk_and_alerts():
    """Exemple de logs de risque et alertes"""
    print("\n⚠️ EXEMPLE 5: Logs de risque et alertes")
    print("="*60)
    
    # Alerte de risque
    central_logger.risk_alert(
        message="Corrélation élevée détectée entre BTC et ETH",
        risk_type="correlation",
        risk_level="HIGH",
        symbol="BTC/USDT",
        correlation_value=0.92,
        threshold=0.85
    )
    
    # Changement de configuration critique
    central_logger.config_changed(
        message="Modification des limites de risque",
        config_key="max_position_size",
        old_value=0.1,
        new_value=0.05,
        reason="risk_reduction",
        user_id="admin"
    )
    
    # Simulation d'un arrêt d'urgence
    central_logger.emergency_stop(
        message="ARRÊT D'URGENCE: Volatilité excessive détectée",
        reason="high_volatility",
        volatility_level=15.2,
        threshold=10.0,
        affected_symbols=["BTC/USDT", "ETH/USDT"],
        positions_closed=3
    )
    
    print("✅ Logs de risque et alertes créés")

def example_audit_trail():
    """Exemple d'utilisation de l'audit trail"""
    print("\n🔍 EXEMPLE 6: Audit trail")
    print("="*60)
    
    # Recherche dans l'audit trail
    recent_audits = central_logger.search_audit(
        start_date=datetime.now() - timedelta(minutes=5),
        limit=10
    )
    
    print(f"📋 {len(recent_audits)} entrées d'audit récentes:")
    for audit in recent_audits[:3]:  # Afficher les 3 premières
        print(f"   • {audit['action']}: {audit['timestamp']}")
    
    # Recherche par action spécifique
    trade_audits = central_logger.search_audit(
        action=AuditAction.TRADE_EXECUTED,
        limit=5
    )
    
    print(f"💹 {len(trade_audits)} audits de trades trouvés")
    
    print("✅ Recherches d'audit effectuées")

def example_analytics_and_reports():
    """Exemple d'analytics et rapports"""
    print("\n📈 EXEMPLE 7: Analytics et rapports")
    print("="*60)
    
    # Statistiques de métriques
    api_stats = performance_analytics.get_metric_statistics("api_response_time", hours=1)
    if api_stats:
        print(f"📊 Stats API response time:")
        print(f"   Moyenne: {api_stats['avg']:.3f}s")
        print(f"   Min/Max: {api_stats['min']:.3f}s / {api_stats['max']:.3f}s")
        print(f"   P95: {api_stats['p95']:.3f}s")
    
    # Score de santé système
    health_score = performance_analytics.get_system_health_score()
    print(f"🏥 Score de santé système: {health_score:.1f}/100")
    
    # Rapport complet
    report = central_logger.get_comprehensive_report(hours=1)
    print(f"📋 Rapport complet généré:")
    print(f"   Total logs: {report['logging']['stats']['total_logs']}")
    print(f"   Entrées audit: {report['logging']['stats']['audit_entries']}")
    print(f"   Métriques: {report['logging']['stats']['metrics_recorded']}")
    print(f"   Uptime: {report['performance']['uptime_seconds']:.0f}s")
    
    print("✅ Analytics et rapports générés")

def example_search_and_export():
    """Exemple de recherche et export"""
    print("\n🔍 EXEMPLE 8: Recherche et export")
    print("="*60)
    
    # Recherche dans les logs
    search_results = central_logger.search_logs(
        query="trade",
        category=LogCategory.TRADING,
        max_results=5
    )
    
    print(f"🔍 {len(search_results)} résultats de recherche 'trade'")
    
    # Export des données (simulation)
    print("📤 Simulation d'export des données...")
    
    # Dans un vrai cas, on ferait :
    # exports = central_logger.export_all_data(
    #     start_date=datetime.now() - timedelta(hours=1),
    #     end_date=datetime.now()
    # )
    
    print("✅ Recherche et export simulés")

def example_context_logging():
    """Exemple de logging contextuel"""
    print("\n🎯 EXEMPLE 9: Logging contextuel")
    print("="*60)
    
    # Utiliser le context manager pour une opération complexe
    with central_logger.timed_operation(
        "portfolio_rebalancing", 
        LogCategory.TRADING,
        audit_action=AuditAction.CONFIG_CHANGED
    ):
        # Simulation d'un rééquilibrage de portefeuille
        central_logger.log(
            LogLevel.INFO,
            "Analyse des positions actuelles",
            LogCategory.TRADING,
            positions_count=5,
            total_value=10500
        )
        
        time.sleep(0.1)  # Simulation du traitement
        
        central_logger.log(
            LogLevel.INFO,
            "Calcul des nouveaux poids",
            LogCategory.TRADING,
            algorithm="mean_reversion",
            target_weights={"BTC": 0.4, "ETH": 0.3, "ADA": 0.3}
        )
        
        time.sleep(0.1)  # Simulation du traitement
        
        central_logger.log(
            LogLevel.INFO,
            "Exécution des ordres de rééquilibrage",
            LogCategory.TRADING,
            orders_placed=3,
            total_volume=500
        )
    
    print("✅ Logging contextuel avec timing automatique")

def show_final_statistics():
    """Affiche les statistiques finales"""
    print("\n📊 STATISTIQUES FINALES")
    print("="*60)
    
    # Statistiques du logger central
    stats = central_logger.log_stats
    print(f"📝 Logs totaux: {stats['total_logs']}")
    print(f"🔍 Entrées audit: {stats['audit_entries']}")
    print(f"📊 Métriques: {stats['metrics_recorded']}")
    
    print(f"\n📋 Logs par niveau:")
    for level, count in stats['logs_by_level'].items():
        print(f"   {level}: {count}")
    
    print(f"\n📂 Logs par catégorie:")
    for category, count in stats['logs_by_category'].items():
        print(f"   {category}: {count}")
    
    # Score de santé final
    health_score = performance_analytics.get_system_health_score()
    print(f"\n🏥 Score de santé final: {health_score:.1f}/100")

def main():
    """Fonction principale des exemples"""
    print("📝 EXEMPLES DU SYSTÈME DE LOGGING AVANCÉ")
    print("="*80)
    
    try:
        # Exemples de base
        example_basic_logging()
        example_trading_logs()
        example_strategy_logs()
        
        # Monitoring et performance
        example_performance_monitoring()
        example_risk_and_alerts()
        
        # Audit et analytics
        example_audit_trail()
        example_analytics_and_reports()
        
        # Fonctionnalités avancées
        example_search_and_export()
        example_context_logging()
        
        # Attendre un peu pour que les métriques se collectent
        print("\n⏳ Attente de la collecte des métriques...")
        time.sleep(3)
        
        # Statistiques finales
        show_final_statistics()
        
        print("\n✅ Tous les exemples ont été exécutés avec succès!")
        print("💡 Le système de logging est maintenant opérationnel")
        print("📁 Consultez le répertoire 'logs/' pour voir les fichiers générés")
        
    except KeyboardInterrupt:
        print("\n🛑 Exemples interrompus par l'utilisateur")
    except Exception as e:
        print(f"\n❌ Erreur inattendue: {e}")
        central_logger.log(
            LogLevel.ERROR,
            f"Erreur dans les exemples: {e}",
            LogCategory.ERROR
        )
    finally:
        # Arrêt propre
        central_logger.shutdown()

if __name__ == "__main__":
    main()
