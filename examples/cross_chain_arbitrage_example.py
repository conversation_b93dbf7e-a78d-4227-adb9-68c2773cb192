#!/usr/bin/env python3
"""
🌉 Exemple d'utilisation du bot d'arbitrage cross-chain
Démontre la détection et l'exécution d'opportunités d'arbitrage entre chaînes
"""

import asyncio
import json
import time
from pathlib import Path
import sys

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from bots.cross_chain_arbitrage.cross_chain_arbitrage_bot import CrossChainArbitrageBot, BotConfig
from bots.cross_chain_arbitrage.chain_connector import ChainConnector
from bots.cross_chain_arbitrage.arbitrage_detector import ArbitrageDetector, ArbitrageConfig
from bots.cross_chain_arbitrage.arbitrage_executor import ArbitrageExecutor, ExecutionConfig
from logging_system.central_logger import central_logger, LogCategory

def create_example_config() -> dict:
    """Crée une configuration d'exemple"""
    return {
        # Paramètres de détection
        "min_profit_usd": 15.0,
        "min_roi_percentage": 0.8,
        
        # Paramètres d'exécution
        "max_concurrent_trades": 2,
        "max_capital_per_trade": 3000.0,
        
        # Chaînes et tokens
        "monitored_chains": ["ethereum", "bsc", "polygon", "arbitrum"],
        "monitored_tokens": ["USDT", "USDC", "DAI"],
        
        # Timing
        "scan_interval_seconds": 20,
        "opportunity_timeout_seconds": 180,
        
        # Notifications
        "enable_notifications": True,
        "notification_min_profit": 30.0,
        
        # Sécurité
        "emergency_stop_loss_percentage": 3.0,
        "max_daily_loss": 500.0
    }

async def test_chain_connector():
    """Teste le connecteur multi-chaînes"""
    print("\n🔗 Test du connecteur multi-chaînes")
    print("="*50)
    
    try:
        connector = ChainConnector()
        
        # Tester les chaînes supportées
        chains = connector.get_supported_chains()
        print(f"✅ Chaînes supportées: {', '.join(chains)}")
        
        # Tester la santé des chaînes
        for chain in chains[:3]:  # Tester les 3 premières
            health = await connector.check_chain_health(chain)
            status = "✅" if health.get('healthy', False) else "❌"
            print(f"{status} {chain}: {health.get('chain_name', 'Unknown')}")
        
        # Tester la récupération de prix
        print(f"\n📊 Test de récupération de prix:")
        all_prices = await connector.get_all_stablecoin_prices()
        
        for chain, chain_prices in list(all_prices.items())[:2]:  # 2 premières chaînes
            print(f"\n🔗 {chain.upper()}:")
            for token, price_data in chain_prices.items():
                print(f"   💰 {token}: ${price_data.price_usd:.6f} (Liquidité: ${price_data.liquidity_usd:,.0f})")
        
        # Tester une cotation de bridge
        print(f"\n🌉 Test de cotation bridge:")
        bridge_quote = await connector.get_bridge_quote(
            from_chain="ethereum",
            to_chain="polygon",
            token_symbol="USDT",
            amount=1000
        )
        
        if bridge_quote:
            print(f"✅ Bridge USDT Ethereum → Polygon:")
            print(f"   💵 Montant: ${bridge_quote['input_amount']}")
            print(f"   💰 Reçu: ${bridge_quote['output_amount']:.2f}")
            print(f"   💸 Frais: ${bridge_quote['total_fee_usd']:.2f}")
            print(f"   ⏱️ Temps: {bridge_quote['estimated_time_minutes']} min")
        else:
            print("❌ Impossible d'obtenir une cotation de bridge")
        
        print("\n✅ Test du connecteur réussi!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur test connecteur: {e}")
        return False

async def test_arbitrage_detector():
    """Teste le détecteur d'opportunités"""
    print("\n🔍 Test du détecteur d'opportunités")
    print("="*50)
    
    try:
        connector = ChainConnector()
        config = ArbitrageConfig(
            min_profit_usd=10.0,
            min_roi_percentage=0.5,
            monitored_tokens=['USDT', 'USDC'],
            monitored_chains=['ethereum', 'bsc', 'polygon']
        )
        
        detector = ArbitrageDetector(connector, config)
        
        # Scanner les opportunités
        print("🔍 Scan des opportunités en cours...")
        opportunities = await detector.scan_opportunities()
        
        print(f"📊 {len(opportunities)} opportunités trouvées")
        
        # Afficher les meilleures opportunités
        for i, opp in enumerate(opportunities[:3], 1):
            print(f"\n💰 Opportunité #{i}:")
            print(f"   🪙 Token: {opp.token_symbol}")
            print(f"   🔗 Route: {opp.buy_chain} → {opp.sell_chain}")
            print(f"   💵 Achat: ${opp.buy_price:.6f}")
            print(f"   💰 Vente: ${opp.sell_price:.6f}")
            print(f"   📈 Écart: {opp.price_difference_pct:.3f}%")
            print(f"   💎 Montant optimal: ${opp.optimal_amount:.0f}")
            print(f"   🎯 Profit net: ${opp.net_profit:.2f}")
            print(f"   ⚡ ROI: {opp.roi_percentage:.2f}%")
            print(f"   🎲 Confiance: {opp.confidence_score:.2f}")
            print(f"   ⚠️ Risque: {opp.risk_level}")
        
        # Statistiques du détecteur
        stats = detector.get_statistics()
        print(f"\n📈 Statistiques:")
        print(f"   Total trouvées: {stats['total_opportunities_found']}")
        print(f"   ROI moyen: {stats['average_roi_percentage']:.2f}%")
        print(f"   Profit moyen: ${stats['average_profit_usd']:.2f}")
        
        print("\n✅ Test du détecteur réussi!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur test détecteur: {e}")
        return False

async def test_arbitrage_executor():
    """Teste l'exécuteur d'arbitrage"""
    print("\n⚡ Test de l'exécuteur d'arbitrage")
    print("="*50)
    
    try:
        connector = ChainConnector()
        detector_config = ArbitrageConfig(min_profit_usd=5.0, min_roi_percentage=0.3)
        detector = ArbitrageDetector(connector, detector_config)
        
        executor_config = ExecutionConfig(
            max_concurrent_executions=1,
            max_capital_per_trade=1000.0
        )
        executor = ArbitrageExecutor(connector, detector, executor_config)
        
        # Trouver une opportunité
        opportunities = await detector.scan_opportunities()
        
        if not opportunities:
            print("❌ Aucune opportunité trouvée pour le test")
            return False
        
        # Prendre la première opportunité
        opportunity = opportunities[0]
        print(f"🎯 Test avec opportunité: {opportunity.token_symbol} {opportunity.buy_chain}→{opportunity.sell_chain}")
        print(f"💰 Profit estimé: ${opportunity.net_profit:.2f}")
        
        # Exécuter l'arbitrage (simulation)
        print("⚡ Exécution en cours...")
        execution = await executor.execute_opportunity(opportunity, amount=500.0)
        
        if execution:
            print(f"✅ Exécution terminée!")
            print(f"   📊 ID: {execution.id}")
            print(f"   📈 Statut: {execution.status.value}")
            print(f"   💰 Profit réalisé: ${execution.realized_profit:.2f}")
            print(f"   ⚡ ROI réalisé: {execution.realized_roi:.2f}%")
            print(f"   ⏱️ Temps d'exécution: {execution.execution_time_seconds:.1f}s")
            
            # Afficher les étapes
            print(f"   📋 Étapes:")
            for step in execution.steps:
                status_icon = "✅" if step.status.value == "COMPLETED" else "❌"
                print(f"      {status_icon} {step.step_name}: {step.status.value}")
        else:
            print("❌ Échec de l'exécution")
            return False
        
        # Statistiques de l'exécuteur
        stats = executor.get_performance_stats()
        print(f"\n📊 Statistiques exécuteur:")
        print(f"   Total exécutions: {stats['total_executions']}")
        print(f"   Taux de réussite: {stats['success_rate']:.1f}%")
        print(f"   Profit total: ${stats['net_profit']:.2f}")
        
        print("\n✅ Test de l'exécuteur réussi!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur test exécuteur: {e}")
        return False

async def test_full_bot():
    """Teste le bot complet"""
    print("\n🤖 Test du bot complet")
    print("="*50)
    
    try:
        # Configuration du bot
        config = BotConfig(
            min_profit_usd=10.0,
            min_roi_percentage=0.5,
            max_concurrent_trades=1,
            max_capital_per_trade=1000.0,
            scan_interval_seconds=10,
            enable_notifications=False  # Désactiver pour le test
        )
        
        # Créer le bot
        bot = CrossChainArbitrageBot(config)
        
        print("🚀 Bot créé avec succès")
        print(f"📊 Configuration:")
        print(f"   Profit min: ${config.min_profit_usd}")
        print(f"   ROI min: {config.min_roi_percentage}%")
        print(f"   Trades max: {config.max_concurrent_trades}")
        print(f"   Capital max: ${config.max_capital_per_trade}")
        
        # Tester le statut
        status = bot.get_status()
        print(f"\n📋 Statut initial:")
        print(f"   En cours: {status['is_running']}")
        print(f"   Chaînes: {', '.join(status['monitored_chains'])}")
        print(f"   Tokens: {', '.join(status['monitored_tokens'])}")
        
        # Tester la récupération d'opportunités
        print(f"\n🔍 Test de scan d'opportunités...")
        opportunities = await bot.get_current_opportunities()
        
        print(f"📊 {len(opportunities)} opportunités actuelles:")
        for i, opp in enumerate(opportunities[:2], 1):
            print(f"   {i}. {opp['token']} {opp['buy_chain']}→{opp['sell_chain']}: "
                  f"${opp['net_profit']:.2f} ({opp['roi_percentage']:.2f}%)")
        
        # Simulation courte du bot
        print(f"\n⏳ Simulation du bot (15 secondes)...")
        
        # Créer une tâche pour le bot
        bot_task = asyncio.create_task(bot.start())
        
        # Attendre 15 secondes
        await asyncio.sleep(15)
        
        # Arrêter le bot
        await bot.stop()
        
        # Attendre que la tâche se termine
        try:
            await asyncio.wait_for(bot_task, timeout=5)
        except asyncio.TimeoutError:
            bot_task.cancel()
        
        # Rapport final
        final_status = bot.get_status()
        print(f"\n📊 Résultats de la simulation:")
        print(f"   Trades quotidiens: {final_status['daily_trades']}")
        print(f"   Profit quotidien: ${final_status['daily_profit']:.2f}")
        print(f"   Erreurs: {final_status['consecutive_errors']}")
        
        print("\n✅ Test du bot complet réussi!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur test bot: {e}")
        return False

async def demo_opportunity_analysis():
    """Démontre l'analyse d'opportunités"""
    print("\n📈 Démonstration d'analyse d'opportunités")
    print("="*50)
    
    try:
        connector = ChainConnector()
        
        # Récupérer les prix sur toutes les chaînes
        all_prices = await connector.get_all_stablecoin_prices()
        
        print("💰 Analyse des écarts de prix USDT:")
        print("-" * 40)
        
        # Analyser les écarts pour USDT
        usdt_prices = {}
        for chain, chain_prices in all_prices.items():
            if 'USDT' in chain_prices:
                usdt_prices[chain] = chain_prices['USDT'].price_usd
        
        # Trouver les écarts
        chains = list(usdt_prices.keys())
        for i, chain1 in enumerate(chains):
            for chain2 in chains[i+1:]:
                price1 = usdt_prices[chain1]
                price2 = usdt_prices[chain2]
                
                diff = abs(price2 - price1)
                diff_pct = (diff / price1) * 100
                
                if price1 < price2:
                    direction = f"{chain1} → {chain2}"
                    profit_direction = "Acheter sur " + chain1 + ", vendre sur " + chain2
                else:
                    direction = f"{chain2} → {chain1}"
                    profit_direction = "Acheter sur " + chain2 + ", vendre sur " + chain1
                
                print(f"🔗 {direction}:")
                print(f"   📊 Écart: {diff_pct:.4f}%")
                print(f"   💡 Stratégie: {profit_direction}")
                print(f"   💰 Prix 1: ${price1:.6f}")
                print(f"   💰 Prix 2: ${price2:.6f}")
                print()
        
        print("✅ Analyse terminée!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur analyse: {e}")
        return False

async def main():
    """Fonction principale des exemples"""
    print("🌉 EXEMPLES BOT D'ARBITRAGE CROSS-CHAIN")
    print("="*80)
    
    try:
        print("🧪 Démarrage des tests...")
        
        # Test 1: Connecteur multi-chaînes
        if not await test_chain_connector():
            print("❌ Test connecteur échoué")
            return
        
        # Test 2: Détecteur d'opportunités
        if not await test_arbitrage_detector():
            print("❌ Test détecteur échoué")
            return
        
        # Test 3: Exécuteur d'arbitrage
        if not await test_arbitrage_executor():
            print("❌ Test exécuteur échoué")
            return
        
        # Test 4: Bot complet
        if not await test_full_bot():
            print("❌ Test bot échoué")
            return
        
        # Démonstration 5: Analyse d'opportunités
        if not await demo_opportunity_analysis():
            print("❌ Démonstration échouée")
            return
        
        print("\n" + "="*80)
        print("✅ Tous les tests ont réussi!")
        print("💡 Le bot d'arbitrage cross-chain est prêt à être utilisé")
        
        # Afficher la configuration recommandée
        print("\n📋 Configuration recommandée pour la production:")
        config = create_example_config()
        print(json.dumps(config, indent=2))
        
        print("\n⚠️ Rappels importants:")
        print("• Testez toujours avec de petits montants d'abord")
        print("• Surveillez les frais de gas et de bridge")
        print("• Vérifiez la liquidité avant d'exécuter")
        print("• Utilisez des alertes pour les gros profits")
        print("• Gardez des réserves pour les frais de transaction")
        
    except KeyboardInterrupt:
        print("\n🛑 Tests interrompus par l'utilisateur")
    except Exception as e:
        print(f"\n❌ Erreur inattendue: {e}")
        central_logger.log(
            level="ERROR",
            message=f"Erreur dans les exemples: {e}",
            category=LogCategory.ERROR
        )

if __name__ == "__main__":
    asyncio.run(main())
