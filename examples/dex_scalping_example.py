#!/usr/bin/env python3
"""
⚡ Exemple d'utilisation du bot de scalping DEX
Démontre la configuration et l'utilisation du bot Uniswap v3
"""

import asyncio
import json
import os
from pathlib import Path
import sys

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from bots.dex_scalping.dex_scalping_bot import DEXScalpingBot, BotConfig
from bots.dex_scalping.uniswap_v3_connector import UniswapV3Connector
from bots.dex_scalping.scalping_engine import ScalpingConfig
from bots.dex_scalping.mev_optimizer import MEVOptimizer
from logging_system.central_logger import central_logger, LogCategory

def load_config_from_file(config_path: str) -> dict:
    """Charge la configuration depuis un fichier JSON"""
    try:
        with open(config_path, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"⚠️ Fichier de configuration non trouvé: {config_path}")
        return create_example_config()
    except json.JSONDecodeError as e:
        print(f"❌ Erreur de parsing JSON: {e}")
        return {}

def create_example_config() -> dict:
    """Crée une configuration d'exemple"""
    return {
        # Configuration blockchain (À MODIFIER avec vos vraies valeurs)
        "web3_provider": "https://mainnet.infura.io/v3/YOUR_PROJECT_ID",
        "private_key": "YOUR_PRIVATE_KEY_HERE",  # ⚠️ JAMAIS en production !
        "network": "goerli",  # Utiliser testnet pour les tests
        
        # Configuration de trading
        "trading_pair": "ETH/WBTC",
        "pool_fee": 3000,  # 0.3%
        
        # Paramètres de scalping
        "min_profit_threshold": 0.002,  # 0.2% minimum
        "max_position_size": 0.02,      # 2% du capital max
        "max_daily_trades": 50,
        
        # Gestion des risques
        "max_daily_loss": 0.01,         # 1% de perte max par jour
        "emergency_stop_loss": 0.03,    # 3% de perte d'urgence
        
        # Configuration MEV
        "use_mev_protection": True,
        "max_gas_price_gwei": 150,
        
        # Monitoring
        "enable_notifications": True,
        "performance_report_interval": 1800  # 30 minutes
    }

def save_example_config(config_path: str):
    """Sauvegarde une configuration d'exemple"""
    config = create_example_config()
    
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"✅ Configuration d'exemple sauvegardée: {config_path}")
    print("⚠️ IMPORTANT: Modifiez les valeurs web3_provider et private_key avant utilisation!")

async def test_uniswap_connection(config: dict):
    """Teste la connexion à Uniswap v3"""
    print("\n🔗 Test de connexion Uniswap v3")
    print("="*50)
    
    try:
        # Créer le connecteur
        connector = UniswapV3Connector(
            web3_provider=config['web3_provider'],
            private_key=config['private_key'],
            network=config['network']
        )
        
        # Tester la récupération d'informations de pool
        pool_info = await connector.get_pool_info(
            connector.tokens['WETH'],
            connector.tokens['WBTC'],
            config['pool_fee']
        )
        
        if pool_info:
            print(f"✅ Pool trouvée: {pool_info.address}")
            print(f"📊 Liquidité: {pool_info.liquidity:,}")
            print(f"💰 Prix actuel: {connector._sqrt_price_to_price(pool_info.sqrt_price_x96):.6f}")
        else:
            print("❌ Impossible de récupérer les informations de la pool")
            return False
        
        # Tester une cotation
        quote = await connector.get_quote(
            connector.tokens['WETH'],
            connector.tokens['WBTC'],
            10**16,  # 0.01 ETH
            config['pool_fee']
        )
        
        if quote:
            print(f"💱 Cotation pour 0.01 ETH: {quote.amount_out} WBTC")
            print(f"⛽ Gas estimé: {quote.gas_estimate:,}")
        else:
            print("❌ Impossible d'obtenir une cotation")
            return False
        
        print("✅ Connexion Uniswap v3 réussie!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur de connexion: {e}")
        return False

async def test_mev_optimizer(config: dict):
    """Teste l'optimiseur MEV"""
    print("\n🛡️ Test de l'optimiseur MEV")
    print("="*50)
    
    try:
        # Créer l'optimiseur
        optimizer = MEVOptimizer(
            web3_provider=config['web3_provider'],
            network=config['network']
        )
        
        # Simuler une transaction
        test_transaction = {
            'function_name': 'exactInputSingle',
            'amount_usd': 1000,
            'pool_liquidity': 5000000,
            'volatility': 0.03,
            'slippage_tolerance': 0.005,
            'deadline': 300
        }
        
        # Optimiser la transaction
        optimized_tx = await optimizer.optimize_transaction(
            test_transaction,
            urgency="normal"
        )
        
        print(f"📊 Transaction optimisée:")
        print(f"   MEV Protection: {optimized_tx.get('mev_protection', {}).get('enabled', False)}")
        print(f"   Gas Strategy: {optimized_tx.get('optimization', {}).get('gas_strategy', 'N/A')}")
        print(f"   Confidence: {optimized_tx.get('optimization_confidence', 0):.2f}")
        
        # Statistiques MEV
        stats = optimizer.get_mev_statistics()
        print(f"📈 Statistiques MEV:")
        print(f"   Transactions protégées: {stats['transactions_protected']}")
        print(f"   Attaques détectées: {stats['mev_attacks_detected']}")
        
        print("✅ Optimiseur MEV fonctionnel!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur optimiseur MEV: {e}")
        return False

async def run_bot_simulation(config: dict, duration_seconds: int = 60):
    """Lance une simulation du bot"""
    print(f"\n🚀 Simulation du bot ({duration_seconds}s)")
    print("="*50)
    
    try:
        # Créer la configuration du bot
        bot_config = BotConfig(**config)
        
        # Créer le bot
        bot = DEXScalpingBot(bot_config)
        
        # Initialiser
        await bot.initialize()
        
        print("✅ Bot initialisé avec succès")
        print(f"📊 Paire de trading: {config['trading_pair']}")
        print(f"🌐 Réseau: {config['network']}")
        print(f"⚡ Protection MEV: {'✅' if config['use_mev_protection'] else '❌'}")
        
        # Démarrer la simulation
        print(f"\n⏳ Démarrage de la simulation...")
        
        # Créer une tâche pour le bot
        bot_task = asyncio.create_task(bot.start())
        
        # Attendre la durée spécifiée
        await asyncio.sleep(duration_seconds)
        
        # Arrêter le bot
        await bot.stop()
        
        # Attendre que la tâche se termine
        try:
            await asyncio.wait_for(bot_task, timeout=10)
        except asyncio.TimeoutError:
            bot_task.cancel()
        
        # Afficher les résultats
        status = bot.get_status()
        print(f"\n📊 Résultats de la simulation:")
        print(f"   Trades quotidiens: {status['daily_trades']}")
        print(f"   P&L quotidien: {status['daily_pnl']:.6f}")
        print(f"   Erreurs consécutives: {status['consecutive_errors']}")
        
        print("✅ Simulation terminée avec succès!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur simulation: {e}")
        return False

async def run_performance_analysis():
    """Analyse les performances du système"""
    print("\n📈 Analyse de performance")
    print("="*50)
    
    try:
        # Simuler différentes conditions de marché
        test_scenarios = [
            {"volatility": 0.01, "liquidity": 1000000, "gas_price": 50},
            {"volatility": 0.03, "liquidity": 5000000, "gas_price": 100},
            {"volatility": 0.05, "liquidity": 10000000, "gas_price": 200}
        ]
        
        for i, scenario in enumerate(test_scenarios, 1):
            print(f"\n📊 Scénario {i}:")
            print(f"   Volatilité: {scenario['volatility']*100:.1f}%")
            print(f"   Liquidité: ${scenario['liquidity']:,}")
            print(f"   Prix gas: {scenario['gas_price']} gwei")
            
            # Simuler l'analyse d'opportunité
            profit_potential = scenario['volatility'] * 100 - (scenario['gas_price'] / 1000)
            mev_risk = min(0.9, scenario['volatility'] * 10 + (scenario['gas_price'] / 200))
            
            print(f"   💰 Potentiel de profit: {profit_potential:.2f}%")
            print(f"   ⚠️ Risque MEV: {mev_risk:.2f}")
            
            if profit_potential > 0.1 and mev_risk < 0.7:
                print(f"   ✅ Scénario favorable")
            else:
                print(f"   ❌ Scénario défavorable")
        
        print("\n✅ Analyse de performance terminée!")
        return True
        
    except Exception as e:
        print(f"❌ Erreur analyse: {e}")
        return False

def show_configuration_guide():
    """Affiche un guide de configuration"""
    print("\n📋 Guide de configuration")
    print("="*50)
    
    print("""
🔧 Configuration requise:

1. Web3 Provider:
   - Infura: https://infura.io/
   - Alchemy: https://www.alchemy.com/
   - Ankr: https://www.ankr.com/

2. Private Key:
   ⚠️ ATTENTION: Utilisez un wallet dédié au trading
   ⚠️ Ne jamais partager votre clé privée
   ⚠️ Utilisez des variables d'environnement en production

3. Réseau:
   - "mainnet" pour Ethereum principal
   - "goerli" pour les tests

4. Paramètres de trading:
   - min_profit_threshold: Profit minimum par trade (0.1-1%)
   - max_position_size: Taille max de position (1-10% du capital)
   - max_daily_trades: Limite quotidienne (10-100 trades)

5. Gestion des risques:
   - max_daily_loss: Perte max quotidienne (1-5%)
   - emergency_stop_loss: Seuil d'arrêt d'urgence (3-10%)

6. MEV Protection:
   - use_mev_protection: Toujours True en production
   - max_gas_price_gwei: Limite de prix gas (100-300 gwei)

💡 Conseils:
- Commencez avec des montants faibles
- Testez d'abord sur testnet
- Surveillez les performances régulièrement
- Ajustez les paramètres selon les conditions de marché
""")

async def main():
    """Fonction principale des exemples"""
    print("⚡ EXEMPLES BOT DE SCALPING DEX UNISWAP V3")
    print("="*80)
    
    # Chemin du fichier de configuration
    config_path = "config/dex_scalping_config.json"
    
    # Créer le répertoire config s'il n'existe pas
    Path("config").mkdir(exist_ok=True)
    
    # Vérifier si le fichier de configuration existe
    if not Path(config_path).exists():
        print("📁 Création du fichier de configuration d'exemple...")
        save_example_config(config_path)
        print("\n⚠️ Veuillez modifier le fichier de configuration avant de continuer.")
        show_configuration_guide()
        return
    
    # Charger la configuration
    config = load_config_from_file(config_path)
    
    if not config:
        print("❌ Impossible de charger la configuration")
        return
    
    # Vérifier les valeurs critiques
    if "YOUR_PROJECT_ID" in config.get('web3_provider', ''):
        print("⚠️ Veuillez configurer votre web3_provider dans le fichier de configuration")
        show_configuration_guide()
        return
    
    if "YOUR_PRIVATE_KEY" in config.get('private_key', ''):
        print("⚠️ Veuillez configurer votre private_key dans le fichier de configuration")
        show_configuration_guide()
        return
    
    try:
        print("🧪 Démarrage des tests...")
        
        # Test 1: Connexion Uniswap
        if not await test_uniswap_connection(config):
            print("❌ Test de connexion échoué")
            return
        
        # Test 2: Optimiseur MEV
        if not await test_mev_optimizer(config):
            print("❌ Test MEV échoué")
            return
        
        # Test 3: Analyse de performance
        if not await run_performance_analysis():
            print("❌ Analyse de performance échouée")
            return
        
        # Test 4: Simulation du bot (optionnel)
        print("\n❓ Voulez-vous lancer une simulation du bot? (y/N)")
        # Pour l'exemple, on lance automatiquement une courte simulation
        print("🚀 Lancement d'une simulation de 30 secondes...")
        
        if not await run_bot_simulation(config, duration_seconds=30):
            print("❌ Simulation échouée")
            return
        
        print("\n✅ Tous les tests ont réussi!")
        print("💡 Le bot de scalping DEX est prêt à être utilisé")
        print("⚠️ Rappel: Testez toujours sur testnet avant la production")
        
    except KeyboardInterrupt:
        print("\n🛑 Tests interrompus par l'utilisateur")
    except Exception as e:
        print(f"\n❌ Erreur inattendue: {e}")
        central_logger.log(
            level="ERROR",
            message=f"Erreur dans les exemples: {e}",
            category=LogCategory.ERROR
        )

if __name__ == "__main__":
    asyncio.run(main())
