"""
📱 Module de notifications pour botCrypto
Gestion centralisée des notifications Telegram et autres
"""

import requests
import logging
from typing import Optional
from config.settings import config

class NotificationManager:
    """Gestionnaire centralisé des notifications"""
    
    def __init__(self, telegram_token: Optional[str] = None, chat_id: Optional[str] = None):
        """
        Initialise le gestionnaire de notifications
        
        Args:
            telegram_token: Token du bot Telegram (optionnel, utilise config par défaut)
            chat_id: ID du chat Telegram (optionnel, utilise config par défaut)
        """
        self.telegram_token = telegram_token or config.telegram_token
        self.chat_id = chat_id or config.telegram_chat_id
        self.logger = logging.getLogger(__name__)
    
    def send_telegram(self, message: str, parse_mode: str = "HTML") -> bool:
        """
        Envoie un message via Telegram
        
        Args:
            message: Message à envoyer
            parse_mode: Mode de parsing ('HTML' ou 'Markdown')
            
        Returns:
            bool: True si envoyé avec succès, False sinon
        """
        if not self.telegram_token or not self.chat_id:
            self.logger.warning(f"📱 Telegram non configuré: {message}")
            print(f"📱 Telegram non configuré: {message}")
            return False
        
        try:
            url = f"https://api.telegram.org/bot{self.telegram_token}/sendMessage"
            data = {
                'chat_id': self.chat_id,
                'text': message,
                'parse_mode': parse_mode
            }
            
            response = requests.post(url, data=data, timeout=10)
            
            if response.status_code == 200:
                self.logger.info(f"✅ Message Telegram envoyé: {message[:50]}...")
                return True
            else:
                self.logger.error(f"❌ Erreur Telegram {response.status_code}: {response.text}")
                return False
                
        except requests.exceptions.RequestException as e:
            self.logger.error(f"❌ Erreur réseau Telegram: {e}")
            return False
        except Exception as e:
            self.logger.error(f"❌ Erreur envoi Telegram: {e}")
            return False
    
    def send_bot_start(self, bot_name: str, balance: float, price: float, environment: str = "test") -> bool:
        """Notification de démarrage de bot"""
        emoji = "🧪" if environment == "test" else "🚀"
        message = (
            f"{emoji} <b>Bot {bot_name} démarré</b>\n"
            f"🌍 Environnement: {environment.upper()}\n"
            f"💰 Solde USDT: {balance:.2f}\n"
            f"📊 Prix initial: {price:.2f} USDT"
        )
        return self.send_telegram(message)
    
    def send_stop_loss(self, price: float, bot_name: str = "Trading") -> bool:
        """Notification de stop-loss"""
        message = (
            f"❌ <b>Stop-Loss atteint</b>\n"
            f"🤖 Bot: {bot_name}\n"
            f"💸 Prix: {price:.2f} USDT\n"
            f"⏹️ Arrêt du bot"
        )
        return self.send_telegram(message)
    
    def send_take_profit(self, price: float, bot_name: str = "Trading") -> bool:
        """Notification de take-profit"""
        message = (
            f"🎉 <b>Take-Profit atteint</b>\n"
            f"🤖 Bot: {bot_name}\n"
            f"💰 Prix: {price:.2f} USDT\n"
            f"✅ Objectif atteint !"
        )
        return self.send_telegram(message)
    
    def send_trade_executed(self, side: str, quantity: float, price: float, symbol: str = "BTCUSDT") -> bool:
        """Notification d'exécution de trade"""
        emoji = "🟢" if side.upper() == "BUY" else "🔴"
        message = (
            f"{emoji} <b>Trade exécuté</b>\n"
            f"📈 {side.upper()} {quantity:.6f} {symbol}\n"
            f"💵 Prix: {price:.2f} USDT"
        )
        return self.send_telegram(message)
    
    def send_hourly_summary(self, balance: float, pnl: float, pnl_percent: float, open_orders: int) -> bool:
        """Notification de résumé horaire"""
        pnl_emoji = "📈" if pnl >= 0 else "📉"
        message = (
            f"🕒 <b>Résumé horaire</b>\n"
            f"💰 Solde USDT: {balance:.2f}\n"
            f"{pnl_emoji} PnL: {pnl:+.2f} USDT ({pnl_percent:+.2f}%)\n"
            f"📑 Ordres ouverts: {open_orders}"
        )
        return self.send_telegram(message)
    
    def send_error(self, error_message: str, bot_name: str = "Bot") -> bool:
        """Notification d'erreur"""
        message = (
            f"🚨 <b>Erreur {bot_name}</b>\n"
            f"❌ {error_message[:200]}..."  # Limiter la longueur
        )
        return self.send_telegram(message)
    
    def send_security_alert(self, alert_message: str) -> bool:
        """Notification d'alerte de sécurité"""
        message = (
            f"🔒 <b>ALERTE SÉCURITÉ</b>\n"
            f"⚠️ {alert_message}"
        )
        return self.send_telegram(message)
    
    def send_snipe_attempt(self, token_address: str, amount: float) -> bool:
        """Notification de tentative de snipe"""
        message = (
            f"🎯 <b>Tentative de Snipe</b>\n"
            f"🪙 Token: <code>{token_address[:10]}...{token_address[-6:]}</code>\n"
            f"💰 Montant: {amount} BNB"
        )
        return self.send_telegram(message)
    
    def send_snipe_success(self, token_address: str, tx_hash: str) -> bool:
        """Notification de snipe réussi"""
        message = (
            f"✅ <b>Snipe Réussi !</b>\n"
            f"🪙 Token: <code>{token_address[:10]}...{token_address[-6:]}</code>\n"
            f"🔗 TX: <code>{tx_hash[:10]}...{tx_hash[-6:]}</code>"
        )
        return self.send_telegram(message)

# Instance globale du gestionnaire de notifications
notifier = NotificationManager()
