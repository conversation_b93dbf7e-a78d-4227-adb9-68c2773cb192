#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📱 Système de Notifications Telegram
Envoie des notifications de trading en temps réel via Telegram
"""

import os
import json
import time
import asyncio
import requests
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
import logging

class TelegramNotifier:
    """Gestionnaire de notifications Telegram pour le bot de trading"""
    
    def __init__(self, bot_token: str = None, chat_id: str = None):
        """
        Initialise le notificateur Telegram
        
        Args:
            bot_token: Token du bot Telegram
            chat_id: ID du chat/utilisateur à notifier
        """
        self.bot_token = bot_token or os.getenv('TELEGRAM_BOT_TOKEN')
        self.chat_id = chat_id or os.getenv('TELEGRAM_CHAT_ID')
        self.base_url = f"https://api.telegram.org/bot{self.bot_token}"
        
        # Configuration des notifications
        self.notification_interval = 30 * 60  # 30 minutes par défaut
        self.last_notification = 0
        self.session_start = datetime.now()
        
        # Statistiques de session
        self.session_stats = {
            'trades_count': 0,
            'total_profit': 0.0,
            'start_balance': 0.0,
            'current_balance': 0.0,
            'best_trade': 0.0,
            'worst_trade': 0.0,
            'win_rate': 0.0
        }
        
        self.logger = logging.getLogger(__name__)
        
    def test_connection(self) -> bool:
        """Teste la connexion Telegram"""
        try:
            response = requests.get(f"{self.base_url}/getMe", timeout=10)
            if response.status_code == 200:
                bot_info = response.json()
                self.logger.info(f"✅ Connexion Telegram OK: {bot_info['result']['first_name']}")
                return True
            else:
                self.logger.error(f"❌ Erreur Telegram: {response.status_code}")
                return False
        except Exception as e:
            self.logger.error(f"❌ Erreur connexion Telegram: {e}")
            return False
    
    def send_message(self, message: str, parse_mode: str = "Markdown") -> bool:
        """
        Envoie un message Telegram
        
        Args:
            message: Message à envoyer
            parse_mode: Mode de formatage (Markdown/HTML)
            
        Returns:
            bool: True si envoyé avec succès
        """
        try:
            data = {
                'chat_id': self.chat_id,
                'text': message,
                'parse_mode': parse_mode
            }
            
            response = requests.post(f"{self.base_url}/sendMessage", data=data, timeout=10)
            
            if response.status_code == 200:
                self.logger.debug("📱 Message Telegram envoyé")
                return True
            else:
                self.logger.error(f"❌ Erreur envoi Telegram: {response.status_code}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Erreur envoi message: {e}")
            return False
    
    def send_startup_notification(self, bot_config: Dict[str, Any]):
        """Envoie la notification de démarrage"""
        duration_hours = bot_config.get('duration_hours', 'Indéfini')
        mode = bot_config.get('mode', 'testnet')
        
        message = f"""
🚀 **BOT CRYPTO DÉMARRÉ**

📅 **Début:** {self.session_start.strftime('%d/%m/%Y %H:%M:%S')}
⏱️ **Durée:** {duration_hours}h
💰 **Mode:** {mode.upper()}
🎯 **Stratégie:** Grid Trading Optimisé

🔧 **Paramètres:**
• Grid Size: {bot_config.get('grid_size', 'N/A')}
• Grid Spacing: {bot_config.get('grid_spacing', 'N/A')}
• Order Size: {bot_config.get('order_size', 'N/A')}
• Stop Loss: {bot_config.get('stop_loss_percent', 'N/A')}%

📊 **Balance initiale:** {bot_config.get('start_balance', 'N/A')} USDT

🔔 Notifications toutes les 30min pour les tests > 1h
        """
        
        self.send_message(message)
        self.session_stats['start_balance'] = bot_config.get('start_balance', 0.0)
    
    def send_periodic_update(self, current_stats: Dict[str, Any]):
        """Envoie une mise à jour périodique"""
        current_time = time.time()
        
        # Vérifier si il faut envoyer une notification
        if current_time - self.last_notification < self.notification_interval:
            return
        
        # Mettre à jour les stats
        self.update_session_stats(current_stats)
        
        # Calculer la durée écoulée
        elapsed = datetime.now() - self.session_start
        hours = int(elapsed.total_seconds() // 3600)
        minutes = int((elapsed.total_seconds() % 3600) // 60)
        
        # Calculer le P&L
        pnl = self.session_stats['current_balance'] - self.session_stats['start_balance']
        pnl_percent = (pnl / self.session_stats['start_balance'] * 100) if self.session_stats['start_balance'] > 0 else 0
        
        # Préparer le message
        status_emoji = "📈" if pnl >= 0 else "📉"
        
        message = f"""
{status_emoji} **MISE À JOUR BOT** ({hours}h{minutes:02d}m)

💰 **Performance:**
• Balance: {self.session_stats['current_balance']:.2f} USDT
• P&L: {pnl:+.2f} USDT ({pnl_percent:+.2f}%)
• Trades: {self.session_stats['trades_count']}
• Win Rate: {self.session_stats['win_rate']:.1f}%

📊 **Trades:**
• Meilleur: +{self.session_stats['best_trade']:.2f} USDT
• Pire: {self.session_stats['worst_trade']:+.2f} USDT

⏰ **Prochaine update:** {(datetime.now() + timedelta(minutes=30)).strftime('%H:%M')}
        """
        
        self.send_message(message)
        self.last_notification = current_time
    
    def send_trade_notification(self, trade_info: Dict[str, Any]):
        """Envoie une notification de trade (optionnel, pour trades importants)"""
        if trade_info.get('profit_percent', 0) > 1.0:  # Seulement pour profits > 1%
            message = f"""
💎 **TRADE EXCEPTIONNEL**

🎯 **{trade_info['side']}** {trade_info['symbol']}
💰 **Profit:** +{trade_info['profit']:.2f} USDT ({trade_info['profit_percent']:+.2f}%)
📊 **Prix:** {trade_info['price']:.2f} USDT
⏱️ **Heure:** {datetime.now().strftime('%H:%M:%S')}
            """
            self.send_message(message)
    
    def send_completion_notification(self, final_stats: Dict[str, Any]):
        """Envoie la notification de fin de test"""
        elapsed = datetime.now() - self.session_start
        hours = int(elapsed.total_seconds() // 3600)
        minutes = int((elapsed.total_seconds() % 3600) // 60)
        
        pnl = final_stats.get('total_pnl', 0)
        pnl_percent = final_stats.get('pnl_percentage', 0)
        
        status_emoji = "🎉" if pnl >= 0 else "😞"
        
        message = f"""
{status_emoji} **TEST TERMINÉ** ({hours}h{minutes:02d}m)

📊 **RÉSULTATS FINAUX:**
• Total Trades: {final_stats.get('total_trades', 0)}
• Trades Gagnants: {final_stats.get('profitable_trades', 0)}
• Win Rate: {final_stats.get('win_rate', 0):.1f}%
• P&L Total: {pnl:+.2f} USDT ({pnl_percent:+.2f}%)

📈 **Métriques:**
• Profit Factor: {final_stats.get('profit_factor', 0):.2f}
• Sharpe Ratio: {final_stats.get('sharpe_ratio', 0):.3f}
• Temps d'exécution moyen: {final_stats.get('avg_execution_time', 0):.2f}s

🎯 **Recommandation:** {'✅ Stratégie viable pour production' if pnl > 0 else '⚠️ Optimisation nécessaire'}
        """
        
        self.send_message(message)
    
    def send_error_notification(self, error_message: str):
        """Envoie une notification d'erreur"""
        message = f"""
🚨 **ERREUR BOT**

❌ **Problème détecté:**
{error_message}

⏰ **Heure:** {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}

🔧 **Action requise:** Vérifiez les logs et redémarrez si nécessaire
        """
        
        self.send_message(message)
    
    def update_session_stats(self, current_stats: Dict[str, Any]):
        """Met à jour les statistiques de session"""
        self.session_stats.update({
            'trades_count': current_stats.get('trades_executed', 0),
            'current_balance': current_stats.get('current_balance', 0.0),
            'win_rate': current_stats.get('win_rate', 0.0),
            'best_trade': current_stats.get('best_trade', 0.0),
            'worst_trade': current_stats.get('worst_trade', 0.0)
        })
    
    def set_notification_interval(self, minutes: int):
        """Définit l'intervalle de notification en minutes"""
        self.notification_interval = minutes * 60
        self.logger.info(f"📱 Intervalle de notification: {minutes} minutes")


def setup_telegram_notifications() -> Optional[TelegramNotifier]:
    """
    Configure et teste les notifications Telegram
    
    Returns:
        TelegramNotifier ou None si la configuration échoue
    """
    bot_token = os.getenv('TELEGRAM_BOT_TOKEN')
    chat_id = os.getenv('TELEGRAM_CHAT_ID')
    
    if not bot_token or not chat_id:
        print("⚠️ Configuration Telegram manquante dans .env.local")
        print("   Ajoutez: TELEGRAM_BOT_TOKEN et TELEGRAM_CHAT_ID")
        return None
    
    notifier = TelegramNotifier(bot_token, chat_id)
    
    if notifier.test_connection():
        print("✅ Notifications Telegram configurées")
        return notifier
    else:
        print("❌ Échec configuration Telegram")
        return None


if __name__ == "__main__":
    # Test du système de notifications
    notifier = setup_telegram_notifications()
    if notifier:
        notifier.send_message("🧪 Test de notification - Bot Crypto")
