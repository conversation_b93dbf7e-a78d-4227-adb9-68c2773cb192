#!/usr/bin/env python3
"""
📊 Dashboard de Monitoring pour botCrypto
Interface web pour surveiller les performances des bots
"""

import sys
import json
import time
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Optional

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from flask import Flask, render_template, jsonify, request
import sqlite3
from config.settings import config
from utils.notifications import notifier

class BotMonitor:
    """Gestionnaire de monitoring des bots"""
    
    def __init__(self, db_path: str = "monitoring/bot_data.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialise la base de données SQLite"""
        Path(self.db_path).parent.mkdir(exist_ok=True)
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Table des métriques de performance
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS bot_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                bot_name TEXT NOT NULL,
                environment TEXT NOT NULL,
                metric_type TEXT NOT NULL,
                value REAL NOT NULL,
                metadata TEXT
            )
        ''')
        
        # Table des trades
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS trades (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                bot_name TEXT NOT NULL,
                environment TEXT NOT NULL,
                symbol TEXT NOT NULL,
                side TEXT NOT NULL,
                quantity REAL NOT NULL,
                price REAL NOT NULL,
                status TEXT NOT NULL,
                tx_hash TEXT,
                profit_loss REAL DEFAULT 0
            )
        ''')
        
        # Table des alertes
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS alerts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                bot_name TEXT NOT NULL,
                alert_type TEXT NOT NULL,
                message TEXT NOT NULL,
                severity TEXT NOT NULL,
                resolved BOOLEAN DEFAULT FALSE
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def log_metric(self, bot_name: str, environment: str, metric_type: str, 
                   value: float, metadata: Dict = None):
        """Enregistre une métrique"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO bot_metrics (bot_name, environment, metric_type, value, metadata)
            VALUES (?, ?, ?, ?, ?)
        ''', (bot_name, environment, metric_type, value, json.dumps(metadata or {})))
        
        conn.commit()
        conn.close()
    
    def log_trade(self, bot_name: str, environment: str, symbol: str, side: str,
                  quantity: float, price: float, status: str, tx_hash: str = None,
                  profit_loss: float = 0):
        """Enregistre un trade"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO trades (bot_name, environment, symbol, side, quantity, 
                              price, status, tx_hash, profit_loss)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (bot_name, environment, symbol, side, quantity, price, status, tx_hash, profit_loss))
        
        conn.commit()
        conn.close()
    
    def log_alert(self, bot_name: str, alert_type: str, message: str, severity: str = "INFO"):
        """Enregistre une alerte"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO alerts (bot_name, alert_type, message, severity)
            VALUES (?, ?, ?, ?)
        ''', (bot_name, alert_type, message, severity))
        
        conn.commit()
        conn.close()
    
    def get_bot_status(self) -> Dict:
        """Récupère le statut de tous les bots"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Dernières métriques par bot
        cursor.execute('''
            SELECT bot_name, environment, metric_type, value, timestamp
            FROM bot_metrics 
            WHERE timestamp > datetime('now', '-1 hour')
            ORDER BY timestamp DESC
        ''')
        
        metrics = cursor.fetchall()
        
        # Trades récents
        cursor.execute('''
            SELECT bot_name, environment, COUNT(*) as trade_count,
                   SUM(profit_loss) as total_pnl
            FROM trades 
            WHERE timestamp > datetime('now', '-24 hours')
            GROUP BY bot_name, environment
        ''')
        
        trades = cursor.fetchall()
        
        # Alertes non résolues
        cursor.execute('''
            SELECT bot_name, COUNT(*) as alert_count
            FROM alerts 
            WHERE resolved = FALSE AND timestamp > datetime('now', '-24 hours')
            GROUP BY bot_name
        ''')
        
        alerts = cursor.fetchall()
        
        conn.close()
        
        return {
            "metrics": metrics,
            "trades": trades,
            "alerts": alerts,
            "last_update": datetime.now().isoformat()
        }
    
    def get_performance_data(self, bot_name: str, hours: int = 24) -> Dict:
        """Récupère les données de performance d'un bot"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Métriques de performance
        cursor.execute('''
            SELECT metric_type, value, timestamp
            FROM bot_metrics 
            WHERE bot_name = ? AND timestamp > datetime('now', '-{} hours')
            ORDER BY timestamp
        '''.format(hours), (bot_name,))
        
        metrics = cursor.fetchall()
        
        # Historique des trades
        cursor.execute('''
            SELECT symbol, side, quantity, price, profit_loss, timestamp
            FROM trades 
            WHERE bot_name = ? AND timestamp > datetime('now', '-{} hours')
            ORDER BY timestamp
        '''.format(hours), (bot_name,))
        
        trades = cursor.fetchall()
        
        conn.close()
        
        return {
            "metrics": metrics,
            "trades": trades
        }

# Instance globale du monitor
monitor = BotMonitor()

# Application Flask
app = Flask(__name__)

@app.route('/')
def dashboard():
    """Page principale du dashboard"""
    return render_template('dashboard.html')

@app.route('/api/status')
def api_status():
    """API pour récupérer le statut des bots"""
    return jsonify(monitor.get_bot_status())

@app.route('/api/bot/<bot_name>')
def api_bot_details(bot_name):
    """API pour récupérer les détails d'un bot"""
    hours = request.args.get('hours', 24, type=int)
    return jsonify(monitor.get_performance_data(bot_name, hours))

@app.route('/api/alert', methods=['POST'])
def api_create_alert():
    """API pour créer une alerte"""
    data = request.json
    monitor.log_alert(
        data['bot_name'],
        data['alert_type'],
        data['message'],
        data.get('severity', 'INFO')
    )
    return jsonify({"status": "success"})

def run_dashboard(host: str = "127.0.0.1", port: int = 5000, debug: bool = False):
    """Lance le dashboard web"""
    print(f"🌐 Dashboard disponible sur http://{host}:{port}")
    app.run(host=host, port=port, debug=debug)

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Dashboard de monitoring botCrypto")
    parser.add_argument("--host", default="127.0.0.1", help="Adresse IP d'écoute")
    parser.add_argument("--port", type=int, default=5000, help="Port d'écoute")
    parser.add_argument("--debug", action="store_true", help="Mode debug")
    
    args = parser.parse_args()
    
    run_dashboard(args.host, args.port, args.debug)
