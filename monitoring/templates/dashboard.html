<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 botCrypto Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 10px;
        }
        
        .card-title {
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
        }
        
        .status-online { background-color: #4CAF50; }
        .status-offline { background-color: #f44336; }
        .status-warning { background-color: #ff9800; }
        
        .metric {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .metric-label {
            font-weight: 500;
            color: #666;
        }
        
        .metric-value {
            font-weight: bold;
            color: #333;
        }
        
        .positive { color: #4CAF50; }
        .negative { color: #f44336; }
        
        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .alerts {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .alert {
            padding: 10px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid;
        }
        
        .alert-info { 
            background: #e3f2fd; 
            border-color: #2196f3; 
        }
        
        .alert-warning { 
            background: #fff3e0; 
            border-color: #ff9800; 
        }
        
        .alert-error { 
            background: #ffebee; 
            border-color: #f44336; 
        }
        
        .refresh-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 50px;
            padding: 15px 20px;
            cursor: pointer;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }
        
        .refresh-btn:hover {
            background: #5a6fd8;
            transform: scale(1.05);
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .loading {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 botCrypto Dashboard</h1>
            <p>Monitoring en temps réel de vos bots de trading</p>
            <p id="last-update">Dernière mise à jour: --</p>
        </div>
        
        <div id="status-grid" class="status-grid">
            <div class="loading">📊 Chargement des données...</div>
        </div>
        
        <div class="chart-container">
            <h3>📈 Performance des dernières 24h</h3>
            <canvas id="performanceChart" width="400" height="200"></canvas>
        </div>
        
        <div class="alerts">
            <h3>🚨 Alertes récentes</h3>
            <div id="alerts-list">
                <div class="loading">🔍 Chargement des alertes...</div>
            </div>
        </div>
    </div>
    
    <button class="refresh-btn" onclick="refreshData()">
        🔄 Actualiser
    </button>
    
    <script>
        let performanceChart;
        
        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            initChart();
            refreshData();
            
            // Auto-refresh toutes les 30 secondes
            setInterval(refreshData, 30000);
        });
        
        function initChart() {
            const ctx = document.getElementById('performanceChart').getContext('2d');
            performanceChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'PnL Total (USDT)',
                        data: [],
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: true
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        },
                        x: {
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        }
                    }
                }
            });
        }
        
        async function refreshData() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();
                
                updateBotStatus(data);
                updateChart(data);
                updateAlerts(data);
                
                document.getElementById('last-update').textContent = 
                    `Dernière mise à jour: ${new Date().toLocaleTimeString()}`;
                    
            } catch (error) {
                console.error('Erreur lors du rafraîchissement:', error);
                showError('Erreur de connexion au serveur');
            }
        }
        
        function updateBotStatus(data) {
            const grid = document.getElementById('status-grid');
            
            // Grouper les métriques par bot
            const botData = {};
            
            data.metrics.forEach(metric => {
                const key = `${metric[0]}_${metric[1]}`;
                if (!botData[key]) {
                    botData[key] = {
                        name: metric[0],
                        environment: metric[1],
                        metrics: {}
                    };
                }
                botData[key].metrics[metric[2]] = metric[3];
            });
            
            // Ajouter les données de trades
            data.trades.forEach(trade => {
                const key = `${trade[0]}_${trade[1]}`;
                if (botData[key]) {
                    botData[key].tradeCount = trade[2];
                    botData[key].totalPnl = trade[3];
                }
            });
            
            // Générer les cartes
            let html = '';
            Object.values(botData).forEach(bot => {
                const status = getStatus(bot);
                html += createBotCard(bot, status);
            });
            
            if (html === '') {
                html = '<div class="card"><div class="card-header"><span class="card-title">Aucun bot actif</span></div><p>Aucune donnée disponible</p></div>';
            }
            
            grid.innerHTML = html;
        }
        
        function createBotCard(bot, status) {
            return `
                <div class="card">
                    <div class="card-header">
                        <span class="card-title">${bot.name} (${bot.environment})</span>
                        <span class="status-indicator status-${status}"></span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Trades (24h)</span>
                        <span class="metric-value">${bot.tradeCount || 0}</span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">PnL Total</span>
                        <span class="metric-value ${(bot.totalPnl || 0) >= 0 ? 'positive' : 'negative'}">
                            ${(bot.totalPnl || 0).toFixed(4)} USDT
                        </span>
                    </div>
                    <div class="metric">
                        <span class="metric-label">Statut</span>
                        <span class="metric-value">${getStatusText(status)}</span>
                    </div>
                </div>
            `;
        }
        
        function getStatus(bot) {
            // Logique simple pour déterminer le statut
            if (Object.keys(bot.metrics).length === 0) return 'offline';
            if (bot.totalPnl < -100) return 'warning';
            return 'online';
        }
        
        function getStatusText(status) {
            switch(status) {
                case 'online': return '🟢 En ligne';
                case 'offline': return '🔴 Hors ligne';
                case 'warning': return '🟡 Attention';
                default: return '❓ Inconnu';
            }
        }
        
        function updateChart(data) {
            // Simuler des données de performance pour la démo
            const now = new Date();
            const labels = [];
            const values = [];
            
            for (let i = 23; i >= 0; i--) {
                const time = new Date(now.getTime() - i * 60 * 60 * 1000);
                labels.push(time.getHours() + 'h');
                
                // Calculer PnL cumulé (simulation)
                let totalPnl = 0;
                data.trades.forEach(trade => {
                    totalPnl += trade[3] || 0;
                });
                
                values.push(totalPnl * (0.8 + Math.random() * 0.4));
            }
            
            performanceChart.data.labels = labels;
            performanceChart.data.datasets[0].data = values;
            performanceChart.update();
        }
        
        function updateAlerts(data) {
            const alertsList = document.getElementById('alerts-list');
            
            if (data.alerts.length === 0) {
                alertsList.innerHTML = '<p>✅ Aucune alerte récente</p>';
                return;
            }
            
            let html = '';
            data.alerts.forEach(alert => {
                html += `
                    <div class="alert alert-warning">
                        <strong>${alert[0]}</strong>: ${alert[1]} alerte(s) non résolue(s)
                    </div>
                `;
            });
            
            alertsList.innerHTML = html;
        }
        
        function showError(message) {
            const grid = document.getElementById('status-grid');
            grid.innerHTML = `
                <div class="card">
                    <div class="card-header">
                        <span class="card-title">❌ Erreur</span>
                    </div>
                    <p>${message}</p>
                </div>
            `;
        }
    </script>
</body>
</html>
