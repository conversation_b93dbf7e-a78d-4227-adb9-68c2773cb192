# 🔐 TEMPLATE DE CONFIGURATION - COPIER VERS .env.local
# ⚠️  NE JAMAIS COMMITTER CE FICHIER AVEC DE VRAIES VALEURS

# BSC/Crypto Wallet Configuration
PRIVATE_KEY=your_wallet_private_key_here
WALLET_ADDRESS=your_wallet_address_here
BSC_SCAN_API_KEY=your_bscscan_api_key_here
TOKEN_BALANCE=******************************************
WRAPPED_BNB=******************************************
PANCAKESWAP_ROUTER=******************************************

# Binance API Keys (General)
safe_bot_API_KEY=your_binance_api_key_here
safe_bot_API_SECRET=your_binance_api_secret_here

# Test Environment API Keys
TEST_CONNEXION_API_KEY=your_test_api_key_here
TEST_CONNEXION_API_SECRET=your_test_api_secret_here

# Test Bot Specific Keys
safe_bot_TEST_API_KEY=your_test_bot_api_key_here
safe_bot_TEST_API_SECRET=your_test_bot_api_secret_here

# Production Bot Keys
safe_bot_PROD_API_KEY=your_prod_api_key_here
safe_bot_PROD_API_SECRET=your_prod_api_secret_here
BASE_URL=https://api.binance.com

# Telegram Notifications
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_CHAT_ID=your_telegram_chat_id_here

# Instructions:
# 1. Copiez ce fichier vers .env.local
# 2. Remplacez toutes les valeurs par vos vraies credentials
# 3. Ne jamais committer .env.local dans Git
