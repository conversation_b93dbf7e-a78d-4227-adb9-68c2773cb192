"""
🧪 Framework de validation des stratégies de trading
Validation complète des performances, risques et robustesse des stratégies
"""

import asyncio
import time
import statistics
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from enum import Enum
import logging
from pathlib import Path
import sys

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent.parent))

from utils.error_handler import error_handler, ErrorCategory, ErrorSeverity
from logging_system.central_logger import central_logger, LogCategory

class ValidationResult(Enum):
    """Résultats de validation"""
    PASS = "PASS"
    FAIL = "FAIL"
    WARNING = "WARNING"
    SKIP = "SKIP"

@dataclass
class ValidationMetric:
    """Métrique de validation"""
    name: str
    value: float
    threshold: float
    comparison: str  # 'gt', 'lt', 'gte', 'lte', 'eq'
    result: ValidationResult
    message: str

@dataclass
class StrategyValidationReport:
    """Rapport de validation d'une stratégie"""
    strategy_name: str
    validation_date: datetime
    total_tests: int
    passed_tests: int
    failed_tests: int
    warnings: int
    
    # Métriques de performance
    performance_metrics: List[ValidationMetric]
    risk_metrics: List[ValidationMetric]
    robustness_metrics: List[ValidationMetric]
    
    # Résumé
    overall_score: float
    recommendation: str
    critical_issues: List[str]
    
    # Données détaillées
    backtest_results: Dict[str, Any]
    stress_test_results: Dict[str, Any]

class StrategyValidator:
    """Validateur de stratégies de trading"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Seuils de validation par défaut
        self.default_thresholds = {
            # Performance
            'min_total_return': 5.0,        # 5% minimum
            'min_sharpe_ratio': 1.0,        # Sharpe > 1.0
            'min_win_rate': 50.0,           # 50% minimum
            'max_drawdown': 20.0,           # 20% maximum
            
            # Risque
            'max_var_95': 5.0,              # VaR 95% < 5%
            'max_volatility': 25.0,         # Volatilité < 25%
            'min_profit_factor': 1.2,       # Profit factor > 1.2
            'max_correlation': 0.8,         # Corrélation < 80%
            
            # Robustesse
            'min_trades': 100,              # 100 trades minimum
            'min_test_period_days': 90,     # 90 jours minimum
            'max_consecutive_losses': 10,   # 10 pertes consécutives max
            'min_stability_score': 0.7      # Score de stabilité > 70%
        }
        
        central_logger.log(
            level="INFO",
            message="Validateur de stratégies initialisé",
            category=LogCategory.SYSTEM
        )
    
    async def validate_strategy(self, strategy_name: str, strategy_data: Dict[str, Any],
                              custom_thresholds: Dict[str, float] = None) -> StrategyValidationReport:
        """Valide une stratégie complète"""
        try:
            # Fusionner les seuils
            thresholds = {**self.default_thresholds}
            if custom_thresholds:
                thresholds.update(custom_thresholds)
            
            central_logger.log(
                level="INFO",
                message=f"Début validation stratégie: {strategy_name}",
                category=LogCategory.STRATEGY
            )
            
            # Extraire les données nécessaires
            trades = strategy_data.get('trades', [])
            prices = strategy_data.get('prices', [])
            positions = strategy_data.get('positions', [])
            
            # Validation des métriques de performance
            performance_metrics = await self._validate_performance_metrics(
                trades, prices, thresholds
            )
            
            # Validation des métriques de risque
            risk_metrics = await self._validate_risk_metrics(
                trades, prices, positions, thresholds
            )
            
            # Validation de la robustesse
            robustness_metrics = await self._validate_robustness_metrics(
                trades, strategy_data, thresholds
            )
            
            # Tests de stress
            stress_test_results = await self._run_stress_tests(strategy_data)
            
            # Calcul du score global
            all_metrics = performance_metrics + risk_metrics + robustness_metrics
            overall_score = self._calculate_overall_score(all_metrics)
            
            # Génération des recommandations
            recommendation, critical_issues = self._generate_recommendations(all_metrics)
            
            # Compilation du rapport
            report = StrategyValidationReport(
                strategy_name=strategy_name,
                validation_date=datetime.now(),
                total_tests=len(all_metrics),
                passed_tests=len([m for m in all_metrics if m.result == ValidationResult.PASS]),
                failed_tests=len([m for m in all_metrics if m.result == ValidationResult.FAIL]),
                warnings=len([m for m in all_metrics if m.result == ValidationResult.WARNING]),
                performance_metrics=performance_metrics,
                risk_metrics=risk_metrics,
                robustness_metrics=robustness_metrics,
                overall_score=overall_score,
                recommendation=recommendation,
                critical_issues=critical_issues,
                backtest_results=self._extract_backtest_summary(strategy_data),
                stress_test_results=stress_test_results
            )
            
            central_logger.log(
                level="INFO",
                message=f"Validation terminée: {strategy_name}",
                category=LogCategory.STRATEGY,
                score=overall_score,
                passed=report.passed_tests,
                failed=report.failed_tests
            )
            
            return report
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.VALIDATION, ErrorSeverity.HIGH, {
                'function': 'validate_strategy',
                'strategy_name': strategy_name
            })
            raise
    
    async def _validate_performance_metrics(self, trades: List[Dict], prices: List[Dict],
                                          thresholds: Dict[str, float]) -> List[ValidationMetric]:
        """Valide les métriques de performance"""
        metrics = []
        
        try:
            if not trades:
                metrics.append(ValidationMetric(
                    name="trades_available",
                    value=0,
                    threshold=1,
                    comparison="gte",
                    result=ValidationResult.FAIL,
                    message="Aucun trade disponible pour l'analyse"
                ))
                return metrics
            
            # Calcul du rendement total
            total_pnl = sum(trade.get('pnl', 0) for trade in trades)
            initial_capital = trades[0].get('capital', 10000) if trades else 10000
            total_return = (total_pnl / initial_capital) * 100
            
            metrics.append(self._create_metric(
                "total_return", total_return, thresholds['min_total_return'], "gte",
                f"Rendement total: {total_return:.2f}%"
            ))
            
            # Calcul du win rate
            winning_trades = len([t for t in trades if t.get('pnl', 0) > 0])
            win_rate = (winning_trades / len(trades)) * 100
            
            metrics.append(self._create_metric(
                "win_rate", win_rate, thresholds['min_win_rate'], "gte",
                f"Taux de réussite: {win_rate:.1f}%"
            ))
            
            # Calcul du Sharpe ratio
            returns = [trade.get('pnl', 0) / initial_capital for trade in trades]
            if len(returns) > 1:
                avg_return = statistics.mean(returns)
                std_return = statistics.stdev(returns)
                sharpe_ratio = (avg_return / std_return) if std_return > 0 else 0
                
                metrics.append(self._create_metric(
                    "sharpe_ratio", sharpe_ratio, thresholds['min_sharpe_ratio'], "gte",
                    f"Ratio de Sharpe: {sharpe_ratio:.2f}"
                ))
            
            # Calcul du drawdown maximum
            cumulative_pnl = 0
            peak = 0
            max_drawdown = 0
            
            for trade in trades:
                cumulative_pnl += trade.get('pnl', 0)
                if cumulative_pnl > peak:
                    peak = cumulative_pnl
                drawdown = ((peak - cumulative_pnl) / max(peak, initial_capital)) * 100
                max_drawdown = max(max_drawdown, drawdown)
            
            metrics.append(self._create_metric(
                "max_drawdown", max_drawdown, thresholds['max_drawdown'], "lte",
                f"Drawdown maximum: {max_drawdown:.2f}%"
            ))
            
            # Profit factor
            gross_profit = sum(trade.get('pnl', 0) for trade in trades if trade.get('pnl', 0) > 0)
            gross_loss = abs(sum(trade.get('pnl', 0) for trade in trades if trade.get('pnl', 0) < 0))
            profit_factor = gross_profit / max(gross_loss, 1)
            
            metrics.append(self._create_metric(
                "profit_factor", profit_factor, thresholds['min_profit_factor'], "gte",
                f"Facteur de profit: {profit_factor:.2f}"
            ))
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.VALIDATION, ErrorSeverity.MEDIUM, {
                'function': '_validate_performance_metrics'
            })
        
        return metrics
    
    async def _validate_risk_metrics(self, trades: List[Dict], prices: List[Dict],
                                   positions: List[Dict], thresholds: Dict[str, float]) -> List[ValidationMetric]:
        """Valide les métriques de risque"""
        metrics = []
        
        try:
            if not trades:
                return metrics
            
            # Calcul de la volatilité
            returns = [trade.get('pnl', 0) for trade in trades]
            if len(returns) > 1:
                volatility = statistics.stdev(returns) / statistics.mean(returns) * 100 if statistics.mean(returns) != 0 else 0
                
                metrics.append(self._create_metric(
                    "volatility", abs(volatility), thresholds['max_volatility'], "lte",
                    f"Volatilité: {abs(volatility):.2f}%"
                ))
            
            # Value at Risk (VaR) 95%
            if len(returns) >= 20:
                sorted_returns = sorted(returns)
                var_95_index = int(len(sorted_returns) * 0.05)
                var_95 = abs(sorted_returns[var_95_index]) / max(abs(statistics.mean(returns)), 1) * 100
                
                metrics.append(self._create_metric(
                    "var_95", var_95, thresholds['max_var_95'], "lte",
                    f"VaR 95%: {var_95:.2f}%"
                ))
            
            # Analyse des pertes consécutives
            consecutive_losses = 0
            max_consecutive_losses = 0
            
            for trade in trades:
                if trade.get('pnl', 0) < 0:
                    consecutive_losses += 1
                    max_consecutive_losses = max(max_consecutive_losses, consecutive_losses)
                else:
                    consecutive_losses = 0
            
            metrics.append(self._create_metric(
                "max_consecutive_losses", max_consecutive_losses, thresholds['max_consecutive_losses'], "lte",
                f"Pertes consécutives max: {max_consecutive_losses}"
            ))
            
            # Exposition maximale
            if positions:
                max_exposure = max(pos.get('size', 0) * pos.get('price', 0) for pos in positions)
                initial_capital = trades[0].get('capital', 10000) if trades else 10000
                exposure_percentage = (max_exposure / initial_capital) * 100
                
                metrics.append(self._create_metric(
                    "max_exposure", exposure_percentage, 50.0, "lte",  # 50% max par défaut
                    f"Exposition maximale: {exposure_percentage:.1f}%"
                ))
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.VALIDATION, ErrorSeverity.MEDIUM, {
                'function': '_validate_risk_metrics'
            })
        
        return metrics
    
    async def _validate_robustness_metrics(self, trades: List[Dict], strategy_data: Dict[str, Any],
                                         thresholds: Dict[str, float]) -> List[ValidationMetric]:
        """Valide les métriques de robustesse"""
        metrics = []
        
        try:
            # Nombre de trades
            num_trades = len(trades)
            metrics.append(self._create_metric(
                "num_trades", num_trades, thresholds['min_trades'], "gte",
                f"Nombre de trades: {num_trades}"
            ))
            
            # Période de test
            if trades:
                start_date = min(datetime.fromisoformat(trade['timestamp']) for trade in trades if 'timestamp' in trade)
                end_date = max(datetime.fromisoformat(trade['timestamp']) for trade in trades if 'timestamp' in trade)
                test_period_days = (end_date - start_date).days
                
                metrics.append(self._create_metric(
                    "test_period_days", test_period_days, thresholds['min_test_period_days'], "gte",
                    f"Période de test: {test_period_days} jours"
                ))
            
            # Score de stabilité (variance des rendements mensuels)
            if len(trades) >= 30:
                monthly_returns = self._calculate_monthly_returns(trades)
                if len(monthly_returns) > 1:
                    stability_score = 1 - (statistics.stdev(monthly_returns) / max(abs(statistics.mean(monthly_returns)), 0.01))
                    stability_score = max(0, min(1, stability_score))
                    
                    metrics.append(self._create_metric(
                        "stability_score", stability_score, thresholds['min_stability_score'], "gte",
                        f"Score de stabilité: {stability_score:.2f}"
                    ))
            
            # Diversification (nombre de symboles tradés)
            symbols = set(trade.get('symbol', 'UNKNOWN') for trade in trades)
            diversification_score = min(len(symbols) / 10, 1.0)  # Normalisé sur 10 symboles
            
            metrics.append(self._create_metric(
                "diversification", diversification_score, 0.3, "gte",
                f"Diversification: {len(symbols)} symboles"
            ))
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.VALIDATION, ErrorSeverity.MEDIUM, {
                'function': '_validate_robustness_metrics'
            })
        
        return metrics
    
    async def _run_stress_tests(self, strategy_data: Dict[str, Any]) -> Dict[str, Any]:
        """Exécute des tests de stress"""
        stress_results = {}
        
        try:
            trades = strategy_data.get('trades', [])
            
            # Test de stress : marché baissier
            bear_market_pnl = self._simulate_market_stress(trades, -0.3)  # -30%
            stress_results['bear_market_pnl'] = bear_market_pnl
            
            # Test de stress : volatilité élevée
            high_volatility_pnl = self._simulate_volatility_stress(trades, 2.0)  # 2x volatilité
            stress_results['high_volatility_pnl'] = high_volatility_pnl
            
            # Test de stress : liquidité réduite
            low_liquidity_impact = self._simulate_liquidity_stress(trades, 0.5)  # 50% de liquidité
            stress_results['low_liquidity_impact'] = low_liquidity_impact
            
            # Test de stress : frais élevés
            high_fees_impact = self._simulate_fee_stress(trades, 5.0)  # 5x frais
            stress_results['high_fees_impact'] = high_fees_impact
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.VALIDATION, ErrorSeverity.LOW, {
                'function': '_run_stress_tests'
            })
        
        return stress_results
    
    def _create_metric(self, name: str, value: float, threshold: float, 
                      comparison: str, message: str) -> ValidationMetric:
        """Crée une métrique de validation"""
        # Déterminer le résultat
        if comparison == "gte":
            result = ValidationResult.PASS if value >= threshold else ValidationResult.FAIL
        elif comparison == "lte":
            result = ValidationResult.PASS if value <= threshold else ValidationResult.FAIL
        elif comparison == "gt":
            result = ValidationResult.PASS if value > threshold else ValidationResult.FAIL
        elif comparison == "lt":
            result = ValidationResult.PASS if value < threshold else ValidationResult.FAIL
        elif comparison == "eq":
            result = ValidationResult.PASS if abs(value - threshold) < 0.001 else ValidationResult.FAIL
        else:
            result = ValidationResult.WARNING
        
        return ValidationMetric(
            name=name,
            value=value,
            threshold=threshold,
            comparison=comparison,
            result=result,
            message=message
        )
    
    def _calculate_overall_score(self, metrics: List[ValidationMetric]) -> float:
        """Calcule le score global"""
        if not metrics:
            return 0.0
        
        # Pondération des résultats
        weights = {
            ValidationResult.PASS: 1.0,
            ValidationResult.WARNING: 0.5,
            ValidationResult.FAIL: 0.0,
            ValidationResult.SKIP: 0.0
        }
        
        total_weight = sum(weights[metric.result] for metric in metrics)
        max_weight = len(metrics) * weights[ValidationResult.PASS]
        
        return (total_weight / max_weight) * 100 if max_weight > 0 else 0.0
    
    def _generate_recommendations(self, metrics: List[ValidationMetric]) -> Tuple[str, List[str]]:
        """Génère des recommandations"""
        failed_metrics = [m for m in metrics if m.result == ValidationResult.FAIL]
        warning_metrics = [m for m in metrics if m.result == ValidationResult.WARNING]
        
        critical_issues = []
        
        # Identifier les problèmes critiques
        for metric in failed_metrics:
            if metric.name in ['max_drawdown', 'max_consecutive_losses', 'var_95']:
                critical_issues.append(f"Risque élevé: {metric.message}")
            elif metric.name in ['total_return', 'sharpe_ratio', 'win_rate']:
                critical_issues.append(f"Performance insuffisante: {metric.message}")
            elif metric.name in ['num_trades', 'test_period_days']:
                critical_issues.append(f"Données insuffisantes: {metric.message}")
        
        # Générer la recommandation globale
        if len(failed_metrics) == 0:
            if len(warning_metrics) == 0:
                recommendation = "APPROUVÉ - Stratégie validée avec succès"
            else:
                recommendation = "APPROUVÉ AVEC RÉSERVES - Surveiller les points d'attention"
        elif len(critical_issues) > 0:
            recommendation = "REJETÉ - Problèmes critiques détectés"
        else:
            recommendation = "RÉVISION REQUISE - Améliorations nécessaires"
        
        return recommendation, critical_issues
    
    def _calculate_monthly_returns(self, trades: List[Dict]) -> List[float]:
        """Calcule les rendements mensuels"""
        monthly_pnl = {}
        
        for trade in trades:
            if 'timestamp' in trade and 'pnl' in trade:
                date = datetime.fromisoformat(trade['timestamp'])
                month_key = f"{date.year}-{date.month:02d}"
                
                if month_key not in monthly_pnl:
                    monthly_pnl[month_key] = 0
                monthly_pnl[month_key] += trade['pnl']
        
        return list(monthly_pnl.values())
    
    def _simulate_market_stress(self, trades: List[Dict], market_impact: float) -> float:
        """Simule un stress de marché"""
        stressed_pnl = 0
        for trade in trades:
            original_pnl = trade.get('pnl', 0)
            # Appliquer l'impact du marché
            stressed_pnl += original_pnl * (1 + market_impact)
        return stressed_pnl
    
    def _simulate_volatility_stress(self, trades: List[Dict], volatility_multiplier: float) -> float:
        """Simule un stress de volatilité"""
        import random
        stressed_pnl = 0
        for trade in trades:
            original_pnl = trade.get('pnl', 0)
            # Ajouter de la volatilité aléatoire
            volatility_impact = random.uniform(-0.1, 0.1) * volatility_multiplier
            stressed_pnl += original_pnl * (1 + volatility_impact)
        return stressed_pnl
    
    def _simulate_liquidity_stress(self, trades: List[Dict], liquidity_factor: float) -> float:
        """Simule un stress de liquidité"""
        impact = 0
        for trade in trades:
            trade_size = trade.get('size', 0) * trade.get('price', 0)
            # Impact proportionnel à la taille du trade
            slippage_impact = (trade_size / 10000) * (1 - liquidity_factor) * 0.01
            impact += trade_size * slippage_impact
        return impact
    
    def _simulate_fee_stress(self, trades: List[Dict], fee_multiplier: float) -> float:
        """Simule un stress de frais"""
        additional_fees = 0
        for trade in trades:
            trade_size = trade.get('size', 0) * trade.get('price', 0)
            base_fee = trade_size * 0.001  # 0.1% de base
            additional_fees += base_fee * (fee_multiplier - 1)
        return additional_fees
    
    def _extract_backtest_summary(self, strategy_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extrait un résumé du backtest"""
        trades = strategy_data.get('trades', [])
        
        if not trades:
            return {}
        
        total_pnl = sum(trade.get('pnl', 0) for trade in trades)
        winning_trades = len([t for t in trades if t.get('pnl', 0) > 0])
        
        return {
            'total_trades': len(trades),
            'winning_trades': winning_trades,
            'losing_trades': len(trades) - winning_trades,
            'total_pnl': total_pnl,
            'average_pnl_per_trade': total_pnl / len(trades) if trades else 0,
            'largest_win': max((t.get('pnl', 0) for t in trades), default=0),
            'largest_loss': min((t.get('pnl', 0) for t in trades), default=0)
        }

# Instance globale du validateur
strategy_validator = StrategyValidator()
