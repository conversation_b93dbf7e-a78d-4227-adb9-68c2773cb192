"""
🧪 Tests unitaires pour le bot de scalping DEX
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta
import sys
from pathlib import Path

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent.parent))

from bots.dex_scalping.dex_scalping_bot import DexScalpingBot, ScalpingConfig
from bots.dex_scalping.opportunity_detector import OpportunityDetector, ScalpingOpportunity
from bots.dex_scalping.scalping_engine import ScalpingEngine

@pytest.mark.unit
class TestDexScalpingBot:
    """Tests pour le bot de scalping DEX"""
    
    def setup_method(self):
        """Configuration avant chaque test"""
        self.config = ScalpingConfig(
            target_pairs=['ETH/USDC', 'WBTC/USDC'],
            min_profit_usd=10.0,
            max_position_size_usd=1000.0,
            max_slippage_percentage=1.0,
            scan_interval_seconds=5
        )
        self.bot = DexScalpingBot(self.config)
    
    def test_initialization(self):
        """Test de l'initialisation du bot"""
        assert self.bot.config == self.config
        assert self.bot.is_running == False
        assert self.bot.start_time is None
        assert isinstance(self.bot.opportunity_detector, OpportunityDetector)
        assert isinstance(self.bot.scalping_engine, ScalpingEngine)
    
    def test_config_validation(self):
        """Test de la validation de la configuration"""
        # Configuration valide
        valid_config = ScalpingConfig(
            target_pairs=['ETH/USDC'],
            min_profit_usd=5.0,
            max_position_size_usd=500.0
        )
        bot = DexScalpingBot(valid_config)
        assert bot.config.min_profit_usd == 5.0
        
        # Configuration avec valeurs par défaut
        minimal_config = ScalpingConfig(target_pairs=['ETH/USDC'])
        bot = DexScalpingBot(minimal_config)
        assert bot.config.min_profit_usd == 20.0  # Valeur par défaut
        assert bot.config.max_slippage_percentage == 2.0
    
    @pytest.mark.asyncio
    async def test_start_stop_bot(self):
        """Test du démarrage et arrêt du bot"""
        # Mock des composants
        self.bot.opportunity_detector.start_monitoring = AsyncMock()
        self.bot.scalping_engine.start_execution = AsyncMock()
        
        # Test du démarrage
        start_task = asyncio.create_task(self.bot.start())
        await asyncio.sleep(0.1)  # Laisser le temps au bot de démarrer
        
        assert self.bot.is_running == True
        assert self.bot.start_time is not None
        
        # Test de l'arrêt
        await self.bot.stop()
        start_task.cancel()
        
        try:
            await start_task
        except asyncio.CancelledError:
            pass
        
        assert self.bot.is_running == False
    
    def test_get_status(self):
        """Test de récupération du statut"""
        status = self.bot.get_status()
        
        assert 'is_running' in status
        assert 'start_time' in status
        assert 'target_pairs' in status
        assert 'opportunities_detected' in status
        assert 'trades_executed' in status
        assert 'total_profit' in status
        
        assert status['is_running'] == False
        assert status['target_pairs'] == self.config.target_pairs
    
    def test_get_performance_stats(self):
        """Test des statistiques de performance"""
        # Ajouter quelques données de test
        self.bot.opportunities_detected = 25
        self.bot.trades_executed = 15
        self.bot.total_profit = 250.0
        self.bot.total_volume = 15000.0
        
        stats = self.bot.get_performance_stats()
        
        assert stats['opportunities_detected'] == 25
        assert stats['trades_executed'] == 15
        assert stats['success_rate'] == 60.0  # 15/25 * 100
        assert stats['total_profit'] == 250.0
        assert stats['total_volume'] == 15000.0
        assert stats['average_profit_per_trade'] == 16.67  # 250/15
    
    @pytest.mark.asyncio
    async def test_opportunity_processing(self):
        """Test du traitement des opportunités"""
        # Créer une opportunité de test
        opportunity = ScalpingOpportunity(
            pair='ETH/USDC',
            buy_dex='uniswap_v2',
            sell_dex='sushiswap',
            buy_price=2000.0,
            sell_price=2020.0,
            profit_usd=18.0,
            confidence_score=0.8,
            timestamp=datetime.now()
        )
        
        # Mock de l'exécution
        self.bot.scalping_engine.execute_scalping_trade = AsyncMock(return_value=True)
        
        # Traiter l'opportunité
        await self.bot._process_opportunity(opportunity)
        
        # Vérifier que l'exécution a été appelée
        self.bot.scalping_engine.execute_scalping_trade.assert_called_once_with(opportunity)
        assert self.bot.opportunities_detected == 1
    
    @pytest.mark.asyncio
    async def test_opportunity_filtering(self):
        """Test du filtrage des opportunités"""
        # Opportunité trop petite (profit < min_profit_usd)
        small_opportunity = ScalpingOpportunity(
            pair='ETH/USDC',
            buy_dex='uniswap_v2',
            sell_dex='sushiswap',
            buy_price=2000.0,
            sell_price=2005.0,
            profit_usd=5.0,  # < 10.0 (min_profit_usd)
            confidence_score=0.8,
            timestamp=datetime.now()
        )
        
        # Mock de l'exécution
        self.bot.scalping_engine.execute_scalping_trade = AsyncMock()
        
        # Traiter l'opportunité
        await self.bot._process_opportunity(small_opportunity)
        
        # Vérifier que l'exécution n'a pas été appelée
        self.bot.scalping_engine.execute_scalping_trade.assert_not_called()
        assert self.bot.opportunities_detected == 1  # Comptée mais pas exécutée
    
    @pytest.mark.asyncio
    async def test_error_handling(self):
        """Test de la gestion d'erreurs"""
        # Opportunité qui génère une erreur
        opportunity = ScalpingOpportunity(
            pair='ETH/USDC',
            buy_dex='uniswap_v2',
            sell_dex='sushiswap',
            buy_price=2000.0,
            sell_price=2020.0,
            profit_usd=18.0,
            confidence_score=0.8,
            timestamp=datetime.now()
        )
        
        # Mock qui lève une exception
        self.bot.scalping_engine.execute_scalping_trade = AsyncMock(
            side_effect=Exception("Erreur de test")
        )
        
        # Traiter l'opportunité (ne doit pas lever d'exception)
        await self.bot._process_opportunity(opportunity)
        
        # Vérifier que l'erreur a été gérée
        assert self.bot.opportunities_detected == 1
        assert self.bot.trades_executed == 0  # Pas d'exécution réussie
    
    def test_add_remove_target_pair(self):
        """Test d'ajout/suppression de paires cibles"""
        initial_count = len(self.bot.config.target_pairs)
        
        # Ajouter une nouvelle paire
        self.bot.add_target_pair('LINK/USDC')
        assert len(self.bot.config.target_pairs) == initial_count + 1
        assert 'LINK/USDC' in self.bot.config.target_pairs
        
        # Ajouter une paire déjà existante (ne doit pas dupliquer)
        self.bot.add_target_pair('ETH/USDC')
        assert len(self.bot.config.target_pairs) == initial_count + 1
        
        # Supprimer une paire
        self.bot.remove_target_pair('LINK/USDC')
        assert len(self.bot.config.target_pairs) == initial_count
        assert 'LINK/USDC' not in self.bot.config.target_pairs
        
        # Supprimer une paire inexistante (ne doit pas lever d'erreur)
        self.bot.remove_target_pair('INEXISTANT/USDC')
        assert len(self.bot.config.target_pairs) == initial_count
    
    def test_update_config(self):
        """Test de mise à jour de la configuration"""
        # Nouvelles valeurs
        new_config = {
            'min_profit_usd': 15.0,
            'max_position_size_usd': 2000.0,
            'max_slippage_percentage': 1.5
        }
        
        # Mettre à jour
        self.bot.update_config(new_config)
        
        # Vérifier les changements
        assert self.bot.config.min_profit_usd == 15.0
        assert self.bot.config.max_position_size_usd == 2000.0
        assert self.bot.config.max_slippage_percentage == 1.5
        
        # Vérifier que les autres valeurs sont inchangées
        assert self.bot.config.target_pairs == ['ETH/USDC', 'WBTC/USDC']
    
    @pytest.mark.asyncio
    async def test_performance_monitoring(self):
        """Test du monitoring de performance"""
        # Simuler quelques trades
        self.bot.trades_executed = 10
        self.bot.total_profit = 150.0
        self.bot.total_volume = 10000.0
        
        # Ajouter des données de performance
        self.bot.trade_history = [
            {'profit': 20.0, 'timestamp': datetime.now()},
            {'profit': -5.0, 'timestamp': datetime.now()},
            {'profit': 15.0, 'timestamp': datetime.now()}
        ]
        
        # Tester le monitoring
        await self.bot._monitor_performance()
        
        # Vérifier que les métriques sont calculées
        stats = self.bot.get_performance_stats()
        assert 'success_rate' in stats
        assert 'average_profit_per_trade' in stats
        assert 'total_profit' in stats
    
    @pytest.mark.asyncio
    async def test_risk_management(self):
        """Test de la gestion des risques"""
        # Simuler des pertes importantes
        self.bot.total_profit = -500.0  # Perte importante
        
        # Mock du portfolio manager
        with patch('risk_management.portfolio_manager.portfolio_manager') as mock_pm:
            mock_pm.check_daily_loss_limit.return_value = False
            
            # Tester la vérification des risques
            risk_ok = await self.bot._check_risk_limits()
            assert risk_ok == False
    
    def test_opportunity_queue_management(self):
        """Test de la gestion de la queue d'opportunités"""
        # Créer plusieurs opportunités
        opportunities = []
        for i in range(5):
            opp = ScalpingOpportunity(
                pair='ETH/USDC',
                buy_dex='uniswap_v2',
                sell_dex='sushiswap',
                buy_price=2000.0,
                sell_price=2000.0 + i * 5,  # Profits croissants
                profit_usd=10.0 + i * 5,
                confidence_score=0.8,
                timestamp=datetime.now()
            )
            opportunities.append(opp)
        
        # Ajouter à la queue
        for opp in opportunities:
            self.bot.opportunity_queue.append(opp)
        
        assert len(self.bot.opportunity_queue) == 5
        
        # Vérifier l'ordre (les plus profitables en premier)
        sorted_queue = sorted(self.bot.opportunity_queue, 
                            key=lambda x: x.profit_usd, reverse=True)
        
        assert sorted_queue[0].profit_usd == 30.0  # Le plus profitable
        assert sorted_queue[-1].profit_usd == 10.0  # Le moins profitable
    
    @pytest.mark.asyncio
    async def test_concurrent_execution_limits(self):
        """Test des limites d'exécution concurrente"""
        # Configurer une limite basse
        self.bot.config.max_concurrent_trades = 2
        
        # Créer plusieurs opportunités
        opportunities = []
        for i in range(5):
            opp = ScalpingOpportunity(
                pair=f'TOKEN{i}/USDC',
                buy_dex='uniswap_v2',
                sell_dex='sushiswap',
                buy_price=100.0,
                sell_price=110.0,
                profit_usd=15.0,
                confidence_score=0.8,
                timestamp=datetime.now()
            )
            opportunities.append(opp)
        
        # Mock de l'exécution (lente)
        async def slow_execution(opp):
            await asyncio.sleep(0.1)
            return True
        
        self.bot.scalping_engine.execute_scalping_trade = slow_execution
        
        # Traiter toutes les opportunités en parallèle
        tasks = [self.bot._process_opportunity(opp) for opp in opportunities]
        await asyncio.gather(*tasks)
        
        # Vérifier que seulement 2 trades ont été exécutés simultanément
        # (difficile à tester précisément, mais on peut vérifier le résultat final)
        assert self.bot.opportunities_detected == 5
    
    def test_statistics_calculation(self):
        """Test du calcul des statistiques"""
        # Ajouter des données de test
        self.bot.trade_history = [
            {'profit': 20.0, 'volume': 1000.0, 'timestamp': datetime.now()},
            {'profit': -10.0, 'volume': 800.0, 'timestamp': datetime.now()},
            {'profit': 15.0, 'volume': 1200.0, 'timestamp': datetime.now()},
            {'profit': 25.0, 'volume': 1500.0, 'timestamp': datetime.now()}
        ]
        
        stats = self.bot._calculate_statistics()
        
        assert stats['total_trades'] == 4
        assert stats['winning_trades'] == 3
        assert stats['losing_trades'] == 1
        assert stats['win_rate'] == 75.0
        assert stats['total_profit'] == 50.0
        assert stats['total_volume'] == 4500.0
        assert stats['average_profit'] == 12.5
        assert stats['largest_win'] == 25.0
        assert stats['largest_loss'] == -10.0
    
    @pytest.mark.asyncio
    async def test_emergency_stop(self):
        """Test de l'arrêt d'urgence"""
        # Démarrer le bot
        self.bot.is_running = True
        
        # Simuler une condition d'arrêt d'urgence
        self.bot.total_profit = -1000.0  # Perte importante
        
        # Mock du portfolio manager
        with patch('risk_management.portfolio_manager.portfolio_manager') as mock_pm:
            mock_pm.check_daily_loss_limit.return_value = False
            
            # Déclencher l'arrêt d'urgence
            await self.bot._emergency_stop("Test d'arrêt d'urgence")
            
            # Vérifier que le bot s'est arrêté
            assert self.bot.is_running == False
    
    def test_configuration_persistence(self):
        """Test de la persistance de la configuration"""
        # Modifier la configuration
        self.bot.config.min_profit_usd = 25.0
        self.bot.config.target_pairs.append('LINK/USDC')
        
        # Sauvegarder (simulation)
        config_dict = {
            'target_pairs': self.bot.config.target_pairs,
            'min_profit_usd': self.bot.config.min_profit_usd,
            'max_position_size_usd': self.bot.config.max_position_size_usd,
            'max_slippage_percentage': self.bot.config.max_slippage_percentage
        }
        
        # Créer un nouveau bot avec la configuration sauvegardée
        new_config = ScalpingConfig(**config_dict)
        new_bot = DexScalpingBot(new_config)
        
        # Vérifier que la configuration est identique
        assert new_bot.config.min_profit_usd == 25.0
        assert 'LINK/USDC' in new_bot.config.target_pairs
        assert new_bot.config.max_position_size_usd == self.bot.config.max_position_size_usd
