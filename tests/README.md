# 🧪 Système de Tests et Validation

## 🎯 Vue d'ensemble

Suite de tests complète pour valider toutes les fonctionnalités du système de trading. Inclut tests unitaires, d'intégration, de performance, de sécurité et validation des stratégies.

## 🏗️ Structure

```
tests/
├── conftest.py                  # Configuration pytest et fixtures
├── run_tests.py                 # Script principal d'exécution
├── README.md                    # Cette documentation
├── unit/                        # Tests unitaires
│   ├── test_portfolio_manager.py
│   ├── test_dex_scalping_bot.py
│   └── ...
├── integration/                 # Tests d'intégration
│   ├── test_bot_integration.py
│   └── ...
├── performance/                 # Tests de performance
│   ├── test_performance.py
│   └── ...
├── security/                    # Tests de sécurité
│   ├── test_security.py
│   └── ...
└── validation/                  # Validation des stratégies
    ├── strategy_validator.py
    ├── test_strategy_validation.py
    └── ...
```

## 🚀 Démarrage Rapide

### Installation des Dépendances

```bash
# Dépendances de base
pip install pytest pytest-asyncio pytest-cov

# Dépendances optionnelles
pip install flake8 pylint black isort

# Dépendances système
pip install psutil
```

### Exécution des Tests

```bash
# Tous les tests
python tests/run_tests.py --all

# Tests rapides seulement
python tests/run_tests.py --quick

# Tests spécifiques
python tests/run_tests.py --unit
python tests/run_tests.py --integration
python tests/run_tests.py --performance
python tests/run_tests.py --security
python tests/run_tests.py --validation

# Avec couverture de code
python tests/run_tests.py --coverage

# Analyse de code
python tests/run_tests.py --lint
```

## 📊 Types de Tests

### 1. Tests Unitaires (`tests/unit/`)

Tests des composants individuels en isolation.

**Couverture :**
- Portfolio Manager
- Bots de trading (DEX Scalping, Copy Trading, Arbitrage)
- Système d'alertes
- Collecteur de métriques
- Utilitaires

**Exemple :**
```bash
# Tests unitaires seulement
pytest tests/unit/ -v

# Test spécifique
pytest tests/unit/test_portfolio_manager.py::TestPortfolioManager::test_open_position -v
```

### 2. Tests d'Intégration (`tests/integration/`)

Tests de l'interaction entre composants.

**Couverture :**
- Intégration bots + portfolio manager
- Intégration dashboard + bots
- Communication entre services
- Gestion d'erreurs cross-composants

**Exemple :**
```bash
# Tests d'intégration
pytest tests/integration/ -v

# Avec marqueurs
pytest -m integration -v
```

### 3. Tests de Performance (`tests/performance/`)

Tests de charge, latence et utilisation des ressources.

**Métriques testées :**
- Temps de démarrage des bots
- Débit de traitement des opportunités
- Utilisation mémoire sous charge
- Temps de réponse du dashboard
- Détection de fuites mémoire

**Exemple :**
```bash
# Tests de performance
pytest tests/performance/ -v -m performance

# Tests lents inclus
pytest tests/performance/ -v -m "performance or slow"
```

### 4. Tests de Sécurité (`tests/security/`)

Tests de vulnérabilités et sécurité.

**Couverture :**
- Validation des entrées
- Protection contre injection SQL/XSS
- Gestion des race conditions
- Protection contre DoS
- Sécurité cryptographique

**Exemple :**
```bash
# Tests de sécurité
pytest tests/security/ -v -m security
```

### 5. Validation des Stratégies (`tests/validation/`)

Framework complet de validation des stratégies de trading.

**Métriques validées :**
- Performance (rendement, Sharpe ratio, win rate)
- Risque (VaR, volatilité, drawdown)
- Robustesse (nombre de trades, stabilité)
- Tests de stress (marché baissier, volatilité)

**Exemple :**
```python
from tests.validation.strategy_validator import strategy_validator

# Valider une stratégie
strategy_data = {
    'trades': [...],  # Données de trades
    'prices': [...],  # Données de prix
    'positions': [...] # Données de positions
}

report = await strategy_validator.validate_strategy(
    "Ma Stratégie", 
    strategy_data
)

print(f"Score: {report.overall_score:.1f}%")
print(f"Recommandation: {report.recommendation}")
```

## 🔧 Configuration

### Fixtures Pytest (`conftest.py`)

Fixtures communes disponibles dans tous les tests :

```python
def test_example(mock_portfolio_manager, sample_trade_data, test_utils):
    # mock_portfolio_manager : Mock du gestionnaire de portefeuille
    # sample_trade_data : Données de trade d'exemple
    # test_utils : Utilitaires de test
    pass
```

**Fixtures disponibles :**
- `mock_portfolio_manager` : Mock du portfolio manager
- `mock_logger` : Mock du logger central
- `mock_web3` : Mock de Web3
- `sample_price_data` : Données de prix d'exemple
- `sample_trade_data` : Données de trade d'exemple
- `mock_dex_connector` : Mock du connecteur DEX
- `test_utils` : Utilitaires de test

### Marqueurs Pytest

```python
@pytest.mark.unit          # Tests unitaires
@pytest.mark.integration   # Tests d'intégration
@pytest.mark.performance   # Tests de performance
@pytest.mark.security      # Tests de sécurité
@pytest.mark.slow          # Tests lents (>5 secondes)
@pytest.mark.network       # Tests nécessitant le réseau
@pytest.mark.blockchain    # Tests blockchain
```

### Configuration pytest.ini

```ini
[tool:pytest]
markers =
    unit: Tests unitaires rapides
    integration: Tests d'intégration
    performance: Tests de performance
    security: Tests de sécurité
    slow: Tests lents (>5 secondes)
    network: Tests nécessitant une connexion réseau
    blockchain: Tests nécessitant une connexion blockchain

testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = -v --tb=short --strict-markers
```

## 📈 Métriques et Reporting

### Couverture de Code

```bash
# Générer un rapport de couverture
pytest --cov=. --cov-report=html --cov-report=term-missing

# Ouvrir le rapport HTML
open htmlcov/index.html
```

### Rapport de Tests

Le script `run_tests.py` génère automatiquement un rapport JSON :

```json
{
  "timestamp": "2024-01-15T10:30:00",
  "summary": {
    "total_test_suites": 8,
    "successful_suites": 7,
    "failed_suites": 1,
    "total_execution_time": 45.2
  },
  "details": [...]
}
```

### Métriques de Performance

Les tests de performance mesurent :

- **Temps de démarrage** : < 2 secondes
- **Débit de traitement** : > 100 opportunités/seconde
- **Utilisation mémoire** : < 100MB pour 10 bots
- **Temps de réponse API** : < 10ms en moyenne
- **Fuites mémoire** : < 20MB après cycles

## 🛠️ Développement

### Ajouter de Nouveaux Tests

1. **Tests unitaires** : Créer dans `tests/unit/`
2. **Tests d'intégration** : Créer dans `tests/integration/`
3. **Utiliser les fixtures** : Importer depuis `conftest.py`
4. **Marquer les tests** : Utiliser les marqueurs appropriés

```python
import pytest

@pytest.mark.unit
class TestNewComponent:
    def test_functionality(self, mock_portfolio_manager):
        # Test implementation
        pass
    
    @pytest.mark.asyncio
    async def test_async_functionality(self):
        # Async test implementation
        pass
```

### Bonnes Pratiques

1. **Isolation** : Chaque test doit être indépendant
2. **Mocking** : Utiliser des mocks pour les dépendances externes
3. **Assertions claires** : Messages d'erreur explicites
4. **Données de test** : Utiliser les fixtures pour les données
5. **Performance** : Tests unitaires < 1 seconde

### Debugging

```bash
# Mode debug avec pdb
pytest --pdb tests/unit/test_portfolio_manager.py

# Logs détaillés
pytest -s -v --log-cli-level=DEBUG

# Arrêt au premier échec
pytest -x tests/
```

## 🔍 Validation des Stratégies

### Framework de Validation

Le système inclut un framework complet pour valider les stratégies :

```python
from tests.validation.strategy_validator import StrategyValidator

validator = StrategyValidator()

# Seuils personnalisés
custom_thresholds = {
    'min_total_return': 10.0,    # 10% minimum
    'min_sharpe_ratio': 1.5,     # Sharpe > 1.5
    'max_drawdown': 15.0         # 15% maximum
}

# Validation
report = await validator.validate_strategy(
    strategy_name="Ma Stratégie",
    strategy_data=data,
    custom_thresholds=custom_thresholds
)

# Résultats
print(f"Score global: {report.overall_score:.1f}%")
print(f"Tests réussis: {report.passed_tests}/{report.total_tests}")
print(f"Recommandation: {report.recommendation}")

for issue in report.critical_issues:
    print(f"⚠️ {issue}")
```

### Métriques de Validation

**Performance :**
- Rendement total
- Ratio de Sharpe
- Taux de réussite
- Drawdown maximum
- Facteur de profit

**Risque :**
- Volatilité
- Value at Risk (VaR)
- Pertes consécutives
- Exposition maximale

**Robustesse :**
- Nombre de trades
- Période de test
- Score de stabilité
- Diversification

**Tests de Stress :**
- Marché baissier (-30%)
- Volatilité élevée (2x)
- Liquidité réduite (50%)
- Frais élevés (5x)

## 📞 Support et Dépannage

### Problèmes Courants

```bash
# Dépendances manquantes
pip install pytest pytest-asyncio pytest-cov psutil

# Permissions
chmod +x tests/run_tests.py

# Path Python
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
```

### Logs de Debug

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# Dans les tests
def test_with_logging(caplog):
    with caplog.at_level(logging.DEBUG):
        # Code de test
        pass
    
    assert "Expected log message" in caplog.text
```

### Performance des Tests

```bash
# Profiling des tests
pytest --durations=10 tests/

# Tests parallèles (si pytest-xdist installé)
pytest -n auto tests/
```

---

**💡 Conseil** : Exécutez `python tests/run_tests.py --quick` avant chaque commit pour valider rapidement vos changements.
