"""
🧪 Tests de sécurité pour le système de trading
Tests de validation, injection, overflow et sécurité générale
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from decimal import Decimal
import sys
from pathlib import Path

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent.parent))

from risk_management.portfolio_manager import PortfolioManager
from dashboard.web_server import DashboardWebServer
from dashboard.metrics_collector import MetricsCollector
from bots.dex_scalping.dex_scalping_bot import DexScalpingBot, ScalpingConfig

@pytest.mark.security
class TestSecurity:
    """Tests de sécurité du système"""
    
    def test_input_validation_addresses(self, security_test_data):
        """Test de validation des adresses Ethereum"""
        pm = PortfolioManager()
        
        # Test avec des adresses invalides
        for invalid_address in security_test_data['invalid_addresses']:
            # Les adresses invalides ne doivent pas causer de crash
            try:
                result = pm._validate_address(invalid_address)
                assert result == False, f"Adresse invalide acceptée: {invalid_address}"
            except Exception as e:
                # Une exception contrôlée est acceptable
                assert "invalid" in str(e).lower() or "address" in str(e).lower()
        
        # Test avec une adresse valide
        valid_address = "0x" + "a" * 40
        assert pm._validate_address(valid_address) == True
    
    def test_input_validation_numbers(self, security_test_data):
        """Test de validation des nombres"""
        pm = PortfolioManager()
        
        # Test avec des nombres problématiques
        for large_number in security_test_data['large_numbers']:
            try:
                # Essayer d'ouvrir une position avec un nombre invalide
                position_id = pm.open_position(
                    symbol="ETH/USDC",
                    position_type="LONG",
                    size=large_number,
                    entry_price=2000.0
                )
                # Si ça ne lève pas d'exception, ça doit retourner None
                assert position_id is None, f"Nombre invalide accepté: {large_number}"
            except (ValueError, OverflowError, TypeError):
                # Ces exceptions sont attendues
                pass
    
    def test_sql_injection_protection(self, security_test_data):
        """Test de protection contre l'injection SQL"""
        # Simuler des requêtes avec des inputs malicieux
        malicious_inputs = security_test_data['malicious_inputs']
        
        # Test avec le système de métriques
        collector = MetricsCollector()
        
        for malicious_input in malicious_inputs:
            try:
                # Essayer d'injecter du code malicieux dans les noms de métriques
                collector._add_metric_point(malicious_input, 100.0)
                
                # Récupérer la métrique (ne doit pas exécuter de code)
                history = collector.get_metric_history(malicious_input, hours=1)
                
                # Si ça fonctionne, vérifier que c'est sécurisé
                assert isinstance(history, list)
                
            except (ValueError, TypeError, KeyError):
                # Ces exceptions sont acceptables pour des inputs invalides
                pass
    
    def test_xss_protection(self, security_test_data):
        """Test de protection contre XSS"""
        collector = MetricsCollector()
        server = DashboardWebServer(collector, host="127.0.0.1", port=8085)
        
        # Test avec des scripts malicieux
        for malicious_script in security_test_data['malicious_inputs']:
            if '<script>' in malicious_script:
                # Simuler l'ajout d'un bot avec un nom malicieux
                mock_bot = Mock()
                mock_bot.get_status.return_value = {
                    'is_running': True,
                    'bot_name': malicious_script
                }
                
                collector.register_bot(malicious_script, "test", mock_bot)
                
                # Récupérer le résumé
                summary = collector.get_dashboard_summary()
                
                # Vérifier que le script n'est pas exécuté
                assert 'bots' in summary
                # Le nom doit être échappé ou nettoyé
                for bot_id, bot_data in summary['bots'].items():
                    if malicious_script in bot_id:
                        # Vérifier qu'il n'y a pas de balises script non échappées
                        assert '<script>' not in str(bot_data)
    
    def test_path_traversal_protection(self, security_test_data):
        """Test de protection contre le path traversal"""
        # Test avec des chemins malicieux
        malicious_paths = [
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\config\\sam",
            "/etc/shadow",
            "C:\\Windows\\System32\\config\\SAM"
        ]
        
        # Simuler l'accès à des fichiers avec des chemins malicieux
        for malicious_path in malicious_paths:
            try:
                # Essayer d'accéder à un fichier avec un chemin malicieux
                # (simulation - en réalité on ne devrait jamais faire ça)
                normalized_path = str(Path(malicious_path).resolve())
                
                # Vérifier que le chemin ne sort pas du répertoire autorisé
                assert not normalized_path.startswith('/etc/')
                assert not normalized_path.startswith('C:\\Windows\\')
                
            except (OSError, ValueError):
                # Ces exceptions sont attendues pour des chemins invalides
                pass
    
    def test_denial_of_service_protection(self):
        """Test de protection contre les attaques DoS"""
        pm = PortfolioManager()
        pm.total_balance = 10000.0
        pm.available_balance = 8000.0
        
        # Test de création massive de positions (potentiel DoS)
        positions_created = 0
        max_attempts = 10000
        
        for i in range(max_attempts):
            position_id = pm.open_position(
                symbol=f"TOKEN{i}/USDC",
                position_type="LONG",
                size=0.001,  # Très petite taille
                entry_price=100.0
            )
            
            if position_id:
                positions_created += 1
            
            # Le système doit avoir des limites
            if positions_created >= pm.max_positions:
                break
        
        # Vérifier qu'il y a bien une limite
        assert positions_created < max_attempts, "Aucune limite de positions détectée"
        assert positions_created <= pm.max_positions, "Limite de positions dépassée"
    
    def test_race_condition_protection(self):
        """Test de protection contre les race conditions"""
        pm = PortfolioManager()
        pm.total_balance = 10000.0
        pm.available_balance = 8000.0
        
        import threading
        import time
        
        results = []
        errors = []
        
        def concurrent_position_creation(thread_id):
            try:
                position_id = pm.open_position(
                    symbol="ETH/USDC",
                    position_type="LONG",
                    size=1.0,
                    entry_price=2000.0
                )
                results.append((thread_id, position_id))
            except Exception as e:
                errors.append((thread_id, str(e)))
        
        # Lancer plusieurs threads simultanément
        threads = []
        for i in range(10):
            thread = threading.Thread(target=concurrent_position_creation, args=(i,))
            threads.append(thread)
        
        # Démarrer tous les threads en même temps
        for thread in threads:
            thread.start()
        
        # Attendre que tous se terminent
        for thread in threads:
            thread.join()
        
        # Vérifier la cohérence
        successful_positions = [r for r in results if r[1] is not None]
        
        # Il ne doit pas y avoir plus de positions que la balance le permet
        max_possible = int(pm.total_balance / 2000.0)  # 1 ETH * 2000 USD
        assert len(successful_positions) <= max_possible, \
            f"Trop de positions créées: {len(successful_positions)} > {max_possible}"
        
        # Vérifier que la balance est cohérente
        total_used = len(successful_positions) * 2000.0
        expected_balance = pm.total_balance - total_used
        assert abs(pm.available_balance - expected_balance) < 0.01, \
            "Balance incohérente après accès concurrent"
    
    def test_memory_exhaustion_protection(self):
        """Test de protection contre l'épuisement mémoire"""
        collector = MetricsCollector()
        
        # Essayer d'ajouter énormément de métriques
        initial_memory = len(collector.metric_points)
        
        # Ajouter beaucoup de points de données
        for i in range(100000):
            collector._add_metric_point(f"metric_{i % 1000}", i)
        
        # Vérifier qu'il y a une limite (deque avec maxlen)
        total_metrics = sum(len(points) for points in collector.metric_points.values())
        
        # Il doit y avoir une limite raisonnable
        assert total_metrics < 2000000, f"Trop de métriques stockées: {total_metrics}"
        
        # Vérifier que les anciennes données sont supprimées
        for metric_name, points in collector.metric_points.items():
            assert len(points) <= 1440, f"Trop de points pour {metric_name}: {len(points)}"
    
    def test_configuration_injection(self):
        """Test de protection contre l'injection de configuration"""
        # Test avec une configuration malicieuse
        malicious_config = {
            'target_pairs': ['ETH/USDC', '"; DROP TABLE trades; --'],
            'min_profit_usd': "'; DELETE FROM users; --",
            'max_position_size_usd': float('inf'),
            'scan_interval_seconds': -1
        }
        
        try:
            # Essayer de créer un bot avec une config malicieuse
            config = ScalpingConfig(**malicious_config)
            bot = DexScalpingBot(config)
            
            # Vérifier que les valeurs sont validées
            assert config.scan_interval_seconds > 0, "Intervalle négatif accepté"
            assert config.max_position_size_usd < float('inf'), "Valeur infinie acceptée"
            
            # Vérifier que les chaînes malicieuses sont nettoyées
            for pair in config.target_pairs:
                assert 'DROP TABLE' not in pair, "Code SQL injecté"
                assert 'DELETE FROM' not in pair, "Code SQL injecté"
            
        except (ValueError, TypeError) as e:
            # Ces exceptions sont acceptables pour des configs invalides
            assert "invalid" in str(e).lower() or "value" in str(e).lower()
    
    @pytest.mark.asyncio
    async def test_api_rate_limiting(self):
        """Test de limitation du taux de requêtes API"""
        collector = MetricsCollector()
        server = DashboardWebServer(collector, host="127.0.0.1", port=8086)
        
        # Simuler beaucoup de requêtes rapides
        request_count = 0
        start_time = asyncio.get_event_loop().time()
        
        # Faire 1000 requêtes aussi vite que possible
        for i in range(1000):
            try:
                summary = collector.get_dashboard_summary()
                request_count += 1
                
                # Vérifier s'il y a une limitation
                current_time = asyncio.get_event_loop().time()
                if current_time - start_time > 1.0:  # Plus d'une seconde
                    break
                    
            except Exception as e:
                # Une limitation peut lever une exception
                if "rate limit" in str(e).lower() or "too many" in str(e).lower():
                    break
        
        elapsed_time = asyncio.get_event_loop().time() - start_time
        requests_per_second = request_count / elapsed_time
        
        # Il devrait y avoir une limitation raisonnable
        # (ou le système est très rapide, ce qui est aussi acceptable)
        print(f"Requêtes traitées: {request_count} en {elapsed_time:.2f}s ({requests_per_second:.1f} req/s)")
    
    def test_privilege_escalation_protection(self):
        """Test de protection contre l'escalade de privilèges"""
        pm = PortfolioManager()
        
        # Essayer d'accéder à des méthodes privées
        private_methods = [attr for attr in dir(pm) if attr.startswith('_')]
        
        for method_name in private_methods:
            method = getattr(pm, method_name)
            
            # Les méthodes privées ne doivent pas être exposées publiquement
            if callable(method) and not method_name.startswith('__'):
                # Vérifier qu'il y a une validation d'accès
                try:
                    # Essayer d'appeler la méthode (peut lever une exception)
                    if method_name == '_validate_address':
                        result = method("0x" + "a" * 40)
                        assert isinstance(result, bool)
                    elif method_name == '_calculate_fees':
                        result = method(1000.0)
                        assert isinstance(result, (int, float))
                except (TypeError, ValueError):
                    # Ces exceptions sont acceptables
                    pass
    
    def test_data_sanitization(self):
        """Test de nettoyage des données"""
        collector = MetricsCollector()
        
        # Test avec des données contenant des caractères spéciaux
        special_chars_data = [
            "normal_metric",
            "metric<script>alert('xss')</script>",
            "metric'; DROP TABLE metrics; --",
            "metric\x00\x01\x02",  # Caractères de contrôle
            "metric\n\r\t",        # Caractères de nouvelle ligne
            "metric" + "A" * 1000  # Très long
        ]
        
        for metric_name in special_chars_data:
            try:
                collector._add_metric_point(metric_name, 100.0)
                
                # Récupérer la métrique
                history = collector.get_metric_history(metric_name, hours=1)
                
                # Vérifier que les données sont nettoyées
                assert isinstance(history, list)
                
                # Vérifier qu'il n'y a pas de caractères dangereux dans les clés
                for key in collector.metric_points.keys():
                    assert '<script>' not in key, "Script non nettoyé"
                    assert 'DROP TABLE' not in key, "SQL non nettoyé"
                    assert len(key) < 500, "Nom de métrique trop long"
                
            except (ValueError, TypeError, KeyError):
                # Ces exceptions sont acceptables pour des données invalides
                pass
    
    def test_cryptographic_security(self):
        """Test de sécurité cryptographique"""
        # Test de génération d'IDs sécurisés
        pm = PortfolioManager()
        pm.total_balance = 10000.0
        pm.available_balance = 8000.0
        
        # Générer plusieurs IDs de position
        position_ids = []
        for i in range(100):
            position_id = pm.open_position(
                symbol="ETH/USDC",
                position_type="LONG",
                size=0.01,
                entry_price=2000.0
            )
            if position_id:
                position_ids.append(position_id)
        
        # Vérifier l'unicité
        assert len(position_ids) == len(set(position_ids)), "IDs non uniques détectés"
        
        # Vérifier la complexité (pas de patterns simples)
        for position_id in position_ids[:10]:
            assert len(position_id) >= 8, f"ID trop court: {position_id}"
            assert not position_id.isdigit(), f"ID trop simple: {position_id}"
            assert position_id != position_id.lower(), f"ID sans variation de casse: {position_id}"
    
    def test_error_information_disclosure(self):
        """Test de non-divulgation d'informations dans les erreurs"""
        pm = PortfolioManager()
        
        # Essayer des opérations qui vont échouer
        error_scenarios = [
            lambda: pm.close_position("inexistant", 2000.0),
            lambda: pm.update_position_price("inexistant", 2000.0),
            lambda: pm.open_position("", "INVALID", -1, 0),
        ]
        
        for scenario in error_scenarios:
            try:
                result = scenario()
                # Si ça ne lève pas d'exception, vérifier le résultat
                assert result is None or result == False, "Opération invalide réussie"
            except Exception as e:
                error_message = str(e).lower()
                
                # Vérifier que l'erreur ne divulgue pas d'informations sensibles
                sensitive_info = [
                    'password', 'secret', 'key', 'token', 'private',
                    'database', 'connection', 'server', 'host', 'port'
                ]
                
                for sensitive in sensitive_info:
                    assert sensitive not in error_message, \
                        f"Information sensible divulguée: {sensitive} dans {error_message}"
