#!/usr/bin/env python3
"""
🧪 Tests de sécurité pour botCrypto
Tests unitaires pour les validations de sécurité
"""

import sys
import unittest
from pathlib import Path

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from utils.security import SecurityValidator

class TestSecurityValidator(unittest.TestCase):
    """Tests pour le validateur de sécurité"""
    
    def setUp(self):
        """Configuration des tests"""
        self.validator = SecurityValidator()
    
    def test_valid_address(self):
        """Test de validation d'adresse"""
        # Adresse valide (WBNB)
        valid_address = "******************************************"
        self.assertTrue(self.validator.is_valid_address(valid_address))
        
        # Adresse invalide
        invalid_address = "0xinvalid"
        self.assertFalse(self.validator.is_valid_address(invalid_address))
        
        # Adresse vide
        self.assertFalse(self.validator.is_valid_address(""))
    
    def test_blacklist_functionality(self):
        """Test de la fonctionnalité blacklist"""
        test_token = "0x1234567890123456789012345678901234567890"
        
        # Token pas encore blacklisté
        self.assertFalse(self.validator.is_token_blacklisted(test_token))
        
        # Ajouter à la blacklist
        self.validator.add_to_blacklist(test_token, "Test")
        
        # Vérifier qu'il est maintenant blacklisté
        self.assertTrue(self.validator.is_token_blacklisted(test_token))
    
    def test_trade_amount_validation(self):
        """Test de validation des montants de trade"""
        # Montant valide
        valid_amount = 0.05
        result = self.validator.validate_trade_amount(valid_amount)
        self.assertTrue(result["is_valid"])
        self.assertEqual(len(result["warnings"]), 0)
        
        # Montant trop élevé
        high_amount = 0.5
        result = self.validator.validate_trade_amount(high_amount)
        self.assertFalse(result["is_valid"])
        self.assertGreater(len(result["warnings"]), 0)
        
        # Montant négatif
        negative_amount = -0.1
        result = self.validator.validate_trade_amount(negative_amount)
        self.assertFalse(result["is_valid"])
        self.assertGreater(len(result["warnings"]), 0)
    
    def test_comprehensive_check_structure(self):
        """Test de la structure du check complet"""
        # Utiliser WBNB comme token de test (adresse connue)
        test_token = "******************************************"
        test_amount = 0.05
        
        result = self.validator.comprehensive_token_check(test_token, test_amount)
        
        # Vérifier la structure de la réponse
        self.assertIn("is_safe_to_trade", result)
        self.assertIn("risk_level", result)
        self.assertIn("warnings", result)
        self.assertIn("checks", result)
        
        # Vérifier les sous-checks
        self.assertIn("amount", result["checks"])
        self.assertIn("contract", result["checks"])
        self.assertIn("liquidity", result["checks"])
        self.assertIn("age", result["checks"])
        
        # Vérifier les types
        self.assertIsInstance(result["is_safe_to_trade"], bool)
        self.assertIn(result["risk_level"], ["LOW", "MEDIUM", "HIGH"])
        self.assertIsInstance(result["warnings"], list)

if __name__ == "__main__":
    unittest.main()
