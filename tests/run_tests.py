#!/usr/bin/env python3
"""
🧪 Script principal d'exécution des tests
Lance tous les types de tests avec reporting détaillé
"""

import sys
import os
import time
import argparse
from pathlib import Path
import subprocess
import json
from datetime import datetime

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

def run_command(command, description=""):
    """Exécute une commande et retourne le résultat"""
    print(f"\n{'='*60}")
    print(f"🧪 {description}")
    print(f"{'='*60}")
    print(f"Commande: {command}")
    print()
    
    start_time = time.time()
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            cwd=Path(__file__).parent.parent
        )
        
        execution_time = time.time() - start_time
        
        print(f"⏱️  Temps d'exécution: {execution_time:.2f}s")
        print(f"📤 Code de retour: {result.returncode}")
        
        if result.stdout:
            print(f"\n📋 Sortie standard:")
            print(result.stdout)
        
        if result.stderr:
            print(f"\n⚠️  Erreurs:")
            print(result.stderr)
        
        return {
            'success': result.returncode == 0,
            'execution_time': execution_time,
            'stdout': result.stdout,
            'stderr': result.stderr,
            'returncode': result.returncode
        }
        
    except Exception as e:
        print(f"❌ Erreur lors de l'exécution: {e}")
        return {
            'success': False,
            'execution_time': time.time() - start_time,
            'error': str(e),
            'returncode': -1
        }

def run_unit_tests():
    """Lance les tests unitaires"""
    return run_command(
        "python -m pytest tests/unit/ -v --tb=short --durations=10",
        "Tests unitaires"
    )

def run_integration_tests():
    """Lance les tests d'intégration"""
    return run_command(
        "python -m pytest tests/integration/ -v --tb=short --durations=10",
        "Tests d'intégration"
    )

def run_performance_tests():
    """Lance les tests de performance"""
    return run_command(
        "python -m pytest tests/performance/ -v --tb=short --durations=10 -m performance",
        "Tests de performance"
    )

def run_security_tests():
    """Lance les tests de sécurité"""
    return run_command(
        "python -m pytest tests/security/ -v --tb=short --durations=10 -m security",
        "Tests de sécurité"
    )

def run_validation_tests():
    """Lance les tests de validation"""
    return run_command(
        "python -m pytest tests/validation/ -v --tb=short --durations=10",
        "Tests de validation des stratégies"
    )

def run_coverage_analysis():
    """Lance l'analyse de couverture"""
    return run_command(
        "python -m pytest --cov=. --cov-report=html --cov-report=term-missing tests/",
        "Analyse de couverture de code"
    )

def run_linting():
    """Lance l'analyse de code"""
    results = []
    
    # Flake8
    results.append(run_command(
        "python -m flake8 --max-line-length=100 --ignore=E501,W503 .",
        "Analyse Flake8"
    ))
    
    # Pylint (si disponible)
    try:
        results.append(run_command(
            "python -m pylint --disable=C0103,C0114,C0115,C0116 --max-line-length=100 bots/ dashboard/ risk_management/ utils/",
            "Analyse Pylint"
        ))
    except:
        print("⚠️  Pylint non disponible, ignoré")
    
    return results

def run_examples():
    """Lance les exemples pour vérifier qu'ils fonctionnent"""
    examples = [
        ("examples/dex_scalping_example.py", "Exemple DEX Scalping"),
        ("examples/copy_trading_example.py", "Exemple Copy Trading"),
        ("examples/dashboard_example.py", "Exemple Dashboard")
    ]
    
    results = []
    for example_file, description in examples:
        if Path(example_file).exists():
            results.append(run_command(
                f"timeout 30 python {example_file}",
                f"Exemple: {description}"
            ))
        else:
            print(f"⚠️  Exemple non trouvé: {example_file}")
    
    return results

def generate_test_report(results):
    """Génère un rapport de test détaillé"""
    report = {
        'timestamp': datetime.now().isoformat(),
        'summary': {
            'total_test_suites': len(results),
            'successful_suites': len([r for r in results if r['success']]),
            'failed_suites': len([r for r in results if not r['success']]),
            'total_execution_time': sum(r['execution_time'] for r in results)
        },
        'details': results
    }
    
    # Sauvegarder le rapport
    report_file = Path("test_report.json")
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n📊 Rapport sauvegardé: {report_file}")
    
    return report

def print_summary(results):
    """Affiche un résumé des résultats"""
    print(f"\n{'='*80}")
    print(f"📊 RÉSUMÉ DES TESTS")
    print(f"{'='*80}")
    
    successful = [r for r in results if r['success']]
    failed = [r for r in results if not r['success']]
    
    print(f"✅ Tests réussis: {len(successful)}")
    print(f"❌ Tests échoués: {len(failed)}")
    print(f"⏱️  Temps total: {sum(r['execution_time'] for r in results):.2f}s")
    
    if failed:
        print(f"\n❌ Échecs détectés:")
        for i, result in enumerate(failed, 1):
            print(f"   {i}. Code de retour: {result['returncode']}")
            if 'error' in result:
                print(f"      Erreur: {result['error']}")
    
    success_rate = len(successful) / len(results) * 100 if results else 0
    print(f"\n🎯 Taux de réussite: {success_rate:.1f}%")
    
    if success_rate >= 90:
        print("🎉 Excellent! Tous les tests passent.")
    elif success_rate >= 70:
        print("👍 Bon! La plupart des tests passent.")
    else:
        print("⚠️  Attention! Plusieurs tests échouent.")
    
    return success_rate >= 70

def main():
    """Fonction principale"""
    parser = argparse.ArgumentParser(description="Lance les tests du système de trading")
    parser.add_argument("--unit", action="store_true", help="Tests unitaires seulement")
    parser.add_argument("--integration", action="store_true", help="Tests d'intégration seulement")
    parser.add_argument("--performance", action="store_true", help="Tests de performance seulement")
    parser.add_argument("--security", action="store_true", help="Tests de sécurité seulement")
    parser.add_argument("--validation", action="store_true", help="Tests de validation seulement")
    parser.add_argument("--coverage", action="store_true", help="Analyse de couverture")
    parser.add_argument("--lint", action="store_true", help="Analyse de code")
    parser.add_argument("--examples", action="store_true", help="Tests des exemples")
    parser.add_argument("--all", action="store_true", help="Tous les tests")
    parser.add_argument("--quick", action="store_true", help="Tests rapides seulement")
    
    args = parser.parse_args()
    
    print("🧪 SYSTÈME DE TESTS AUTOMATISÉS")
    print("="*80)
    print(f"📅 Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🐍 Python: {sys.version}")
    print(f"📁 Répertoire: {Path.cwd()}")
    
    results = []
    
    # Vérifier que pytest est installé
    try:
        import pytest
        print(f"✅ Pytest version: {pytest.__version__}")
    except ImportError:
        print("❌ Pytest non installé. Installez avec: pip install pytest")
        return False
    
    # Exécuter les tests selon les arguments
    if args.all or (not any([args.unit, args.integration, args.performance, 
                            args.security, args.validation, args.coverage, 
                            args.lint, args.examples, args.quick])):
        # Tous les tests
        results.append(run_unit_tests())
        results.append(run_integration_tests())
        results.append(run_validation_tests())
        
        if not args.quick:
            results.append(run_performance_tests())
            results.append(run_security_tests())
            results.extend(run_linting())
            results.extend(run_examples())
            results.append(run_coverage_analysis())
    
    else:
        # Tests spécifiques
        if args.unit:
            results.append(run_unit_tests())
        
        if args.integration:
            results.append(run_integration_tests())
        
        if args.performance:
            results.append(run_performance_tests())
        
        if args.security:
            results.append(run_security_tests())
        
        if args.validation:
            results.append(run_validation_tests())
        
        if args.coverage:
            results.append(run_coverage_analysis())
        
        if args.lint:
            results.extend(run_linting())
        
        if args.examples:
            results.extend(run_examples())
        
        if args.quick:
            results.append(run_unit_tests())
            results.append(run_validation_tests())
    
    # Générer le rapport et afficher le résumé
    generate_test_report(results)
    success = print_summary(results)
    
    # Code de sortie
    return 0 if success else 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Tests interrompus par l'utilisateur")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Erreur inattendue: {e}")
        sys.exit(1)
