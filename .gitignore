# 🔐 Fichiers de configuration sensibles
.env.local
.env
*.env
.env*

# 📝 Documentation privée
private-docs/

# 📝 Logs et fichiers temporaires
*.log
*.tmp
*.temp
safe_bot_*.log
grid_trading*.log
snipebot*.log
testnet_trading.log

# 🐍 Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
venv/

# 🧪 Testing
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/

# 📊 Jupyter Notebook
.ipynb_checkpoints

# 🔧 IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# 🖥️ OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 📦 Node modules (si vous ajoutez du JS)
node_modules/

# 💰 Fichiers de backup de wallets
*.wallet
*.keystore
backup/
backups/

# 📈 Données de trading sensibles
trading_data/
positions/
orders/

# Securité
SECURITE_URGENTE.md
GUIDE_RECONFIGURATION.md
securisation.sh
validation_securite.py
validation_securite_complete.py