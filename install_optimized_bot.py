#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📦 Installation du Bot Optimisé
Script d'installation et de configuration automatique
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def print_banner():
    """Affiche la bannière d'installation"""
    print("""
╔══════════════════════════════════════════════════════════════════════════════╗
║                    📦 INSTALLATION BOT OPTIMISÉ                             ║
║                   Configuration Automatique Complète                        ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """)

def check_python_version():
    """Vérifie la version de Python"""
    print("🐍 Vérification de Python...")
    
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python 3.8+ requis (version actuelle: {version.major}.{version.minor})")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} détecté")
    return True

def install_requirements():
    """Installe les dépendances Python"""
    print("\n📚 Installation des dépendances...")
    
    requirements = [
        "requests>=2.28.0",
        "pandas>=1.5.0",
        "numpy>=1.21.0",
        "matplotlib>=3.5.0",
        "seaborn>=0.11.0",
        "python-dotenv>=0.19.0",
        "psutil>=5.9.0"
    ]
    
    for requirement in requirements:
        print(f"📦 Installation de {requirement}...")
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", requirement
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            print(f"✅ {requirement} installé")
        except subprocess.CalledProcessError:
            print(f"❌ Erreur installation {requirement}")
            return False
    
    return True

def create_env_template():
    """Crée un template .env.local si inexistant"""
    print("\n⚙️ Configuration de l'environnement...")
    
    env_file = Path(".env.local")
    
    if env_file.exists():
        print("✅ Fichier .env.local existant trouvé")
        return True
    
    env_template = """# Configuration du Bot Optimisé
# ================================

# Clés API Binance Testnet
safe_bot_TEST_API_KEY=your_testnet_api_key_here
safe_bot_TEST_API_SECRET=your_testnet_api_secret_here

# Clés API Binance Production (ATTENTION: Argent réel!)
safe_bot_PROD_API_KEY=your_production_api_key_here
safe_bot_PROD_API_SECRET=your_production_api_secret_here

# URLs des API
TEST_BASE_URL=https://testnet.binance.vision
BASE_URL=https://api.binance.com

# Paramètres de Trading Optimisés
# ================================

# Configuration de la grille
GRID_SIZE=15
GRID_SPACING=50
ORDER_SIZE=0.002

# Capital maximum (USDT)
CAPITAL_MAX=1000

# Gestion des risques
STOP_LOSS_PERCENT=2
TAKE_PROFIT_PERCENT=3

# Filtres de marché avancés
MIN_VOLUME_24H=1000000
MIN_PRICE_CHANGE=0.5
MAX_SPREAD=0.1
MIN_PROFIT_THRESHOLD=0.2

# Optimisation automatique
AUTO_OPTIMIZATION=true
OPTIMIZATION_INTERVAL=3600
PERFORMANCE_THRESHOLD=0.1

# Configuration des tests
TEST_DURATION_HOURS=168

# Gestion des logs
LOG_ROTATION_SIZE=10
LOG_RETENTION_DAYS=7
DETAILED_LOGGING=true
"""
    
    try:
        with open(env_file, 'w') as f:
            f.write(env_template)
        print("✅ Fichier .env.local créé avec la configuration par défaut")
        print("⚠️ IMPORTANT: Configurez vos clés API dans .env.local")
        return True
    except Exception as e:
        print(f"❌ Erreur création .env.local: {e}")
        return False

def create_directories():
    """Crée les répertoires nécessaires"""
    print("\n📁 Création des répertoires...")
    
    directories = [
        "logs",
        "charts",
        "configs",
        "backups"
    ]
    
    for directory in directories:
        dir_path = Path(directory)
        try:
            dir_path.mkdir(exist_ok=True)
            print(f"✅ Répertoire {directory}/ créé")
        except Exception as e:
            print(f"❌ Erreur création {directory}/: {e}")
            return False
    
    return True

def check_api_connectivity():
    """Vérifie la connectivité API"""
    print("\n🌐 Vérification de la connectivité...")
    
    try:
        import requests
        
        # Test Binance Testnet
        response = requests.get("https://testnet.binance.vision/api/v3/ping", timeout=10)
        if response.status_code == 200:
            print("✅ Binance Testnet accessible")
        else:
            print("⚠️ Binance Testnet non accessible")
        
        # Test Binance Production
        response = requests.get("https://api.binance.com/api/v3/ping", timeout=10)
        if response.status_code == 200:
            print("✅ Binance Production accessible")
        else:
            print("⚠️ Binance Production non accessible")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur connectivité: {e}")
        return False

def make_scripts_executable():
    """Rend les scripts exécutables (Unix/Linux)"""
    if platform.system() in ['Linux', 'Darwin']:  # Linux ou macOS
        print("\n🔧 Configuration des permissions...")
        
        scripts = [
            "run_optimized_bot.py",
            "quick_test.py",
            "bots/optimized_safe_bot.py",
            "utils/performance_analyzer.py"
        ]
        
        for script in scripts:
            script_path = Path(script)
            if script_path.exists():
                try:
                    os.chmod(script_path, 0o755)
                    print(f"✅ {script} rendu exécutable")
                except Exception as e:
                    print(f"⚠️ Erreur permissions {script}: {e}")

def create_quick_start_guide():
    """Crée un guide de démarrage rapide"""
    print("\n📖 Création du guide de démarrage...")
    
    guide_content = """# 🚀 Guide de Démarrage Rapide - Bot Optimisé

## 1. Configuration Initiale

1. **Configurez vos clés API dans .env.local** :
   ```
   safe_bot_TEST_API_KEY=votre_cle_testnet
   safe_bot_TEST_API_SECRET=votre_secret_testnet
   ```

2. **Obtenez vos clés API Binance Testnet** :
   - Allez sur https://testnet.binance.vision/
   - Créez un compte et générez des clés API
   - Copiez-les dans .env.local

## 2. Premier Test

```bash
# Test rapide de 1 heure
python quick_test.py --test-1h

# Interface complète
python run_optimized_bot.py
```

## 3. Analyse des Résultats

```bash
# Analyser les performances
python utils/performance_analyzer.py --charts
```

## 4. Déploiement Production

```bash
# Après tests réussis en testnet
python quick_test.py --deploy
```

## ⚠️ Important

- Testez TOUJOURS en testnet d'abord
- Ne déployez en production qu'avec des stratégies rentables
- Surveillez les logs régulièrement
- Commencez avec de petits montants

## 📞 Support

Consultez docs/OPTIMIZED_BOT_GUIDE.md pour plus de détails.
"""
    
    try:
        with open("QUICK_START.md", 'w') as f:
            f.write(guide_content)
        print("✅ Guide de démarrage créé: QUICK_START.md")
    except Exception as e:
        print(f"❌ Erreur création guide: {e}")

def run_initial_test():
    """Propose de lancer un test initial"""
    print("\n🧪 Test Initial")
    print("=" * 30)
    
    if not Path(".env.local").exists():
        print("❌ Configurez d'abord .env.local avec vos clés API")
        return
    
    # Vérifier si les clés sont configurées
    try:
        from dotenv import load_dotenv
        load_dotenv(".env.local")
        
        test_api_key = os.getenv("safe_bot_TEST_API_KEY")
        if not test_api_key or test_api_key == "your_testnet_api_key_here":
            print("❌ Clés API testnet non configurées dans .env.local")
            return
        
        print("✅ Clés API testnet configurées")
        
        run_test = input("🚀 Lancer un test rapide de 5 minutes ? (oui/non): ").lower()
        if run_test in ['oui', 'o', 'yes', 'y']:
            print("\n🧪 Lancement du test de validation...")
            try:
                # Import et test rapide
                sys.path.insert(0, str(Path.cwd()))
                from bots.optimized_safe_bot import OptimizedSafeBot
                
                bot = OptimizedSafeBot(mode="testnet")
                
                # Test de connectivité
                price = bot.get_current_price()
                if price:
                    print(f"✅ Connectivité OK - Prix BTC: {price:,.2f} USDT")
                    print("🎉 Installation réussie!")
                else:
                    print("❌ Problème de connectivité API")
                
            except Exception as e:
                print(f"❌ Erreur test: {e}")
                print("💡 Vérifiez vos clés API et la connectivité")
        
    except Exception as e:
        print(f"❌ Erreur vérification config: {e}")

def main():
    """Fonction principale d'installation"""
    print_banner()
    
    print("🔧 Démarrage de l'installation automatique...")
    
    # Vérifications préalables
    if not check_python_version():
        sys.exit(1)
    
    # Installation des dépendances
    if not install_requirements():
        print("❌ Échec installation des dépendances")
        sys.exit(1)
    
    # Configuration de l'environnement
    if not create_env_template():
        print("❌ Échec configuration environnement")
        sys.exit(1)
    
    # Création des répertoires
    if not create_directories():
        print("❌ Échec création répertoires")
        sys.exit(1)
    
    # Vérification connectivité
    check_api_connectivity()
    
    # Permissions (Unix/Linux)
    make_scripts_executable()
    
    # Guide de démarrage
    create_quick_start_guide()
    
    print("\n" + "="*80)
    print("🎉 INSTALLATION TERMINÉE AVEC SUCCÈS!")
    print("="*80)
    
    print("\n📋 PROCHAINES ÉTAPES:")
    print("1. 🔑 Configurez vos clés API dans .env.local")
    print("2. 📖 Lisez QUICK_START.md pour débuter")
    print("3. 🧪 Lancez votre premier test: python quick_test.py --test-1h")
    print("4. 📊 Analysez les résultats avec l'outil d'analyse")
    print("5. 🚀 Déployez en production si rentable")
    
    print("\n📁 FICHIERS IMPORTANTS:")
    print("• .env.local - Configuration des clés API")
    print("• QUICK_START.md - Guide de démarrage")
    print("• docs/OPTIMIZED_BOT_GUIDE.md - Documentation complète")
    print("• run_optimized_bot.py - Interface principale")
    print("• quick_test.py - Tests rapides")
    
    # Test initial optionnel
    run_initial_test()
    
    print("\n🎯 Objectif: Maximiser la rentabilité en testnet, puis répliquer en production!")
    print("💡 Commencez toujours par des tests courts en testnet")
    print("⚠️ Ne déployez en production qu'avec des stratégies rentables validées")

if __name__ == "__main__":
    main()
