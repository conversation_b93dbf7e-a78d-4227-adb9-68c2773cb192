"""
📋 Gestionnaire de sessions de paper trading
Permet de gérer plusieurs simulations simultanées
"""

import threading
import logging
from datetime import datetime
from typing import Dict, List, Optional
from pathlib import Path
import sys
import json

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from paper_trading.simulator import PaperTradingSimulator
from backtesting.strategies import trading_strategies

class PaperTradingSessionManager:
    """Gestionnaire de sessions de paper trading"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.sessions = {}  # session_id -> simulator
        self.session_counter = 0
        self.lock = threading.Lock()
        
        self.logger.info("📋 Gestionnaire de sessions paper trading initialisé")
    
    def create_session(self, strategy_name: str, symbol: str = 'BTC/USDT', 
                      capital: float = 1000, use_real_data: bool = False) -> str:
        """
        Crée une nouvelle session de paper trading
        
        Args:
            strategy_name: Nom de la stratégie à utiliser
            symbol: Symbole à trader
            capital: Capital initial
            use_real_data: Utiliser des données de marché réelles
            
        Returns:
            str: ID de la session créée
        """
        with self.lock:
            self.session_counter += 1
            session_id = f"session_{self.session_counter}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Créer le simulateur
        simulator = PaperTradingSimulator(capital, symbol)
        simulator.set_real_market_data(use_real_data)
        
        # Récupérer et définir la stratégie
        strategies = trading_strategies.get_all_strategies()
        if strategy_name not in strategies:
            raise ValueError(f"Stratégie '{strategy_name}' non trouvée. "
                           f"Disponibles: {list(strategies.keys())}")
        
        strategy_func = strategies[strategy_name]
        simulator.set_strategy(strategy_func, strategy_name)
        
        # Enregistrer la session
        self.sessions[session_id] = {
            'simulator': simulator,
            'created_at': datetime.now(),
            'strategy_name': strategy_name,
            'symbol': symbol,
            'capital': capital,
            'use_real_data': use_real_data,
            'status': 'created'
        }
        
        self.logger.info(f"✅ Session créée: {session_id} ({strategy_name} sur {symbol})")
        return session_id
    
    def start_session(self, session_id: str) -> bool:
        """Démarre une session de paper trading"""
        if session_id not in self.sessions:
            self.logger.error(f"❌ Session {session_id} non trouvée")
            return False
        
        session = self.sessions[session_id]
        simulator = session['simulator']
        
        if session['status'] == 'running':
            self.logger.warning(f"⚠️ Session {session_id} déjà en cours")
            return False
        
        # Démarrer la simulation
        success = simulator.start_simulation()
        if success:
            session['status'] = 'running'
            session['started_at'] = datetime.now()
            self.logger.info(f"🚀 Session {session_id} démarrée")
        
        return success
    
    def stop_session(self, session_id: str) -> bool:
        """Arrête une session de paper trading"""
        if session_id not in self.sessions:
            self.logger.error(f"❌ Session {session_id} non trouvée")
            return False
        
        session = self.sessions[session_id]
        simulator = session['simulator']
        
        if session['status'] != 'running':
            self.logger.warning(f"⚠️ Session {session_id} n'est pas en cours")
            return False
        
        # Arrêter la simulation
        success = simulator.stop_simulation()
        if success:
            session['status'] = 'stopped'
            session['stopped_at'] = datetime.now()
            self.logger.info(f"🛑 Session {session_id} arrêtée")
        
        return success
    
    def get_session_status(self, session_id: str) -> Optional[Dict]:
        """Récupère le statut d'une session"""
        if session_id not in self.sessions:
            return None
        
        session = self.sessions[session_id]
        simulator = session['simulator']
        
        # Informations de base
        status_info = {
            'session_id': session_id,
            'strategy_name': session['strategy_name'],
            'symbol': session['symbol'],
            'initial_capital': session['capital'],
            'use_real_data': session['use_real_data'],
            'status': session['status'],
            'created_at': session['created_at'].isoformat(),
        }
        
        # Ajouter les timestamps selon le statut
        if 'started_at' in session:
            status_info['started_at'] = session['started_at'].isoformat()
        if 'stopped_at' in session:
            status_info['stopped_at'] = session['stopped_at'].isoformat()
        
        # Ajouter les performances si la session a été démarrée
        if session['status'] in ['running', 'stopped']:
            performance = simulator.get_performance_summary()
            status_info['performance'] = performance
        
        return status_info
    
    def list_sessions(self) -> List[Dict]:
        """Liste toutes les sessions"""
        sessions_list = []
        
        for session_id in self.sessions:
            status = self.get_session_status(session_id)
            if status:
                sessions_list.append(status)
        
        # Trier par date de création (plus récent en premier)
        sessions_list.sort(key=lambda x: x['created_at'], reverse=True)
        
        return sessions_list
    
    def delete_session(self, session_id: str) -> bool:
        """Supprime une session"""
        if session_id not in self.sessions:
            self.logger.error(f"❌ Session {session_id} non trouvée")
            return False
        
        session = self.sessions[session_id]
        
        # Arrêter la session si elle est en cours
        if session['status'] == 'running':
            self.stop_session(session_id)
        
        # Supprimer la session
        del self.sessions[session_id]
        self.logger.info(f"🗑️ Session {session_id} supprimée")
        
        return True
    
    def save_session_results(self, session_id: str, filepath: Optional[str] = None) -> Optional[str]:
        """Sauvegarde les résultats d'une session"""
        if session_id not in self.sessions:
            self.logger.error(f"❌ Session {session_id} non trouvée")
            return None
        
        session = self.sessions[session_id]
        simulator = session['simulator']
        
        # Générer le chemin si non fourni
        if not filepath:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            strategy_name = session['strategy_name'].replace(' ', '_')
            filepath = f"assets/results/paper_trading/{session_id}_{strategy_name}_{timestamp}.json"
        
        # Sauvegarder
        try:
            saved_path = simulator.save_results(filepath)
            self.logger.info(f"💾 Résultats session {session_id} sauvegardés: {saved_path}")
            return saved_path
        except Exception as e:
            self.logger.error(f"❌ Erreur sauvegarde session {session_id}: {e}")
            return None
    
    def stop_all_sessions(self):
        """Arrête toutes les sessions en cours"""
        running_sessions = [sid for sid, session in self.sessions.items() 
                          if session['status'] == 'running']
        
        for session_id in running_sessions:
            self.stop_session(session_id)
        
        self.logger.info(f"🛑 {len(running_sessions)} sessions arrêtées")
    
    def cleanup_old_sessions(self, max_age_hours: int = 24):
        """Nettoie les anciennes sessions"""
        current_time = datetime.now()
        old_sessions = []
        
        for session_id, session in self.sessions.items():
            age = current_time - session['created_at']
            if age.total_seconds() > max_age_hours * 3600 and session['status'] != 'running':
                old_sessions.append(session_id)
        
        for session_id in old_sessions:
            self.delete_session(session_id)
        
        if old_sessions:
            self.logger.info(f"🧹 {len(old_sessions)} anciennes sessions supprimées")
    
    def get_summary_stats(self) -> Dict:
        """Récupère des statistiques résumées"""
        total_sessions = len(self.sessions)
        running_sessions = sum(1 for s in self.sessions.values() if s['status'] == 'running')
        stopped_sessions = sum(1 for s in self.sessions.values() if s['status'] == 'stopped')
        
        # Calculer les performances moyennes des sessions terminées
        completed_sessions = [s for s in self.sessions.values() if s['status'] == 'stopped']
        
        if completed_sessions:
            avg_return = sum(s['simulator'].get_performance_summary()['total_return_percent'] 
                           for s in completed_sessions) / len(completed_sessions)
            avg_trades = sum(s['simulator'].get_performance_summary()['total_trades'] 
                           for s in completed_sessions) / len(completed_sessions)
        else:
            avg_return = 0
            avg_trades = 0
        
        return {
            'total_sessions': total_sessions,
            'running_sessions': running_sessions,
            'stopped_sessions': stopped_sessions,
            'created_sessions': total_sessions - running_sessions - stopped_sessions,
            'avg_return_percent': avg_return,
            'avg_trades_per_session': avg_trades
        }
    
    def create_custom_strategy_session(self, strategy_func: callable, strategy_name: str,
                                     symbol: str = 'BTC/USDT', capital: float = 1000,
                                     use_real_data: bool = False) -> str:
        """
        Crée une session avec une stratégie personnalisée
        
        Args:
            strategy_func: Fonction de stratégie personnalisée
            strategy_name: Nom de la stratégie
            symbol: Symbole à trader
            capital: Capital initial
            use_real_data: Utiliser des données réelles
            
        Returns:
            str: ID de la session créée
        """
        with self.lock:
            self.session_counter += 1
            session_id = f"custom_{self.session_counter}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Créer le simulateur
        simulator = PaperTradingSimulator(capital, symbol)
        simulator.set_real_market_data(use_real_data)
        simulator.set_strategy(strategy_func, strategy_name)
        
        # Enregistrer la session
        self.sessions[session_id] = {
            'simulator': simulator,
            'created_at': datetime.now(),
            'strategy_name': strategy_name,
            'symbol': symbol,
            'capital': capital,
            'use_real_data': use_real_data,
            'status': 'created',
            'is_custom': True
        }
        
        self.logger.info(f"✅ Session personnalisée créée: {session_id} ({strategy_name})")
        return session_id

# Instance globale
session_manager = PaperTradingSessionManager()
