# 📝 Système de Logging Avancé

## 🎯 Vue d'ensemble

Le système de logging de botCrypto fournit une solution complète et professionnelle pour le logging, l'audit trail et l'analytics de performance. Il combine logging structuré, traçabilité complète et métriques en temps réel.

## 🏗️ Architecture

```
logging_system/
├── __init__.py              # Module principal
├── advanced_logger.py       # Logger structuré avec rotation
├── audit_trail.py          # Audit trail avec intégrité
├── performance_analytics.py # Métriques et analytics
├── central_logger.py       # Logger centralisé intégré
└── README.md               # Cette documentation
```

## 🚀 Utilisation Rapide

### 1. Logging basique

```python
from logging_system.central_logger import central_logger
from logging_system.advanced_logger import LogLevel, LogCategory

# Log simple
central_logger.log(LogLevel.INFO, "Message d'information", LogCategory.SYSTEM)

# Log avec données supplémentaires
central_logger.log(
    LogLevel.WARNING, 
    "Alerte de performance", 
    LogCategory.PERFORMANCE,
    cpu_usage=85.2,
    memory_usage=78.5
)
```

### 2. Logs de trading avec audit automatique

```python
# Trade exécuté (audit automatique)
central_logger.trade_executed(
    message="Trade BTC/USDT exécuté",
    symbol="BTC/USDT",
    side="buy",
    quantity=0.001,
    price=50000,
    trade_id="trade_123"
)

# Position ouverte
central_logger.position_opened(
    message="Position BTC ouverte",
    symbol="BTC/USDT",
    position_type="LONG",
    quantity=0.001,
    entry_price=50000
)
```

### 3. Monitoring de performance

```python
# Métrique simple
central_logger.performance_metric(
    metric_name="api_response_time",
    value=0.125,
    unit="seconds"
)

# Opération chronométrée
with central_logger.timed_operation("complex_calculation"):
    # Votre code ici
    result = complex_function()
```

## 📊 Composants Principaux

### Advanced Logger

Logger structuré avec support multi-format et rotation automatique.

**Fonctionnalités :**
- Logs structurés JSON et texte
- Rotation automatique (50MB par fichier)
- Catégorisation par type (Trading, System, Audit, etc.)
- Context managers pour logging contextuel
- Recherche dans les logs

**Exemple :**
```python
from logging_system.advanced_logger import advanced_logger, LogLevel, LogCategory

# Log avec contexte
with advanced_logger.context_manager("user_session_123"):
    advanced_logger.info("Action utilisateur", LogCategory.AUDIT, action="login")
```

### Audit Trail

Système d'audit avec intégrité des données et traçabilité complète.

**Fonctionnalités :**
- Stockage SQLite avec checksums d'intégrité
- 20+ types d'actions auditées
- Recherche et filtrage avancés
- Export sécurisé
- Vérification d'intégrité

**Exemple :**
```python
from logging_system.audit_trail import audit_trail, AuditAction, AuditSeverity

# Audit manuel
audit_trail.log_audit(
    action=AuditAction.CONFIG_CHANGED,
    severity=AuditSeverity.HIGH,
    old_values={"max_position": 0.1},
    new_values={"max_position": 0.05},
    user_id="admin"
)

# Décorateur d'audit
@audit_trail.audit_decorator(AuditAction.TRADE_EXECUTED, AuditSeverity.HIGH)
def execute_trade(symbol, quantity, price):
    # Votre logique de trading
    pass
```

### Performance Analytics

Collecte et analyse des métriques système et business.

**Fonctionnalités :**
- Métriques système automatiques (CPU, RAM, disque)
- Métriques business personnalisées
- Agrégations en temps réel
- Score de santé système
- Rapports de performance

**Exemple :**
```python
from logging_system.performance_analytics import performance_analytics

# Démarrer le monitoring
performance_analytics.start_system_monitoring(interval=60)

# Métrique custom
performance_analytics.record_metric(
    name="order_latency",
    value=0.045,
    unit="seconds",
    category="trading"
)

# Timer de performance
with performance_analytics.timer("database_query"):
    result = database.query("SELECT * FROM trades")
```

## 🎯 Logger Centralisé

Point d'entrée unique intégrant tous les composants.

### Méthodes Spécialisées

```python
# Trading
central_logger.trade_executed(message, symbol, side, quantity, price)
central_logger.position_opened(message, symbol, position_type, quantity, entry_price)
central_logger.position_closed(message, symbol, quantity, exit_price, pnl)

# Stratégies
central_logger.strategy_action(message, strategy_name, action, symbol)

# Configuration
central_logger.config_changed(message, config_key, old_value, new_value)

# Urgence
central_logger.emergency_stop(message, reason)

# Risques
central_logger.risk_alert(message, risk_type, risk_level, symbol)
```

### Context Managers

```python
# Opération chronométrée avec audit
with central_logger.timed_operation(
    "portfolio_rebalancing",
    category=LogCategory.TRADING,
    audit_action=AuditAction.CONFIG_CHANGED
):
    # Votre code de rééquilibrage
    rebalance_portfolio()
```

## 📋 Types de Logs

### Niveaux de Log

| Niveau | Valeur | Usage |
|--------|--------|-------|
| **TRACE** | 5 | Debug très détaillé |
| **DEBUG** | 10 | Informations de développement |
| **INFO** | 20 | Informations générales |
| **WARNING** | 30 | Avertissements |
| **ERROR** | 40 | Erreurs |
| **CRITICAL** | 50 | Erreurs critiques |
| **AUDIT** | 60 | Audit spécialisé |

### Catégories de Log

| Catégorie | Description | Fichier |
|-----------|-------------|---------|
| **TRADING** | Opérations de trading | `trading/trading.log` |
| **STRATEGY** | Logique des stratégies | `trading/trading.log` |
| **RISK** | Gestion des risques | `trading/trading.log` |
| **NETWORK** | Communications réseau | `main.log` |
| **SYSTEM** | Système et configuration | `main.log` |
| **AUDIT** | Audit trail | `audit/audit.log` |
| **PERFORMANCE** | Métriques de performance | `performance/performance.log` |
| **ERROR** | Erreurs système | `errors/errors.log` |

## 🔍 Recherche et Analytics

### Recherche dans les Logs

```python
# Recherche simple
results = central_logger.search_logs(
    query="BTC/USDT",
    category=LogCategory.TRADING,
    max_results=50
)

# Recherche avec dates
results = central_logger.search_logs(
    query="error",
    start_date=datetime.now() - timedelta(hours=24),
    end_date=datetime.now()
)
```

### Recherche dans l'Audit

```python
# Recherche par action
audits = central_logger.search_audit(
    action=AuditAction.TRADE_EXECUTED,
    limit=100
)

# Recherche par utilisateur
audits = central_logger.search_audit(
    user_id="trader_001",
    start_date=datetime.now() - timedelta(days=7)
)
```

### Rapports et Analytics

```python
# Rapport complet
report = central_logger.get_comprehensive_report(hours=24)

# Statistiques de métriques
stats = performance_analytics.get_metric_statistics("api_response_time", hours=1)

# Score de santé
health = performance_analytics.get_system_health_score()
```

## 📊 Métriques Automatiques

### Métriques Système

- **CPU** : Utilisation processeur (%)
- **Mémoire** : Utilisation RAM (%)
- **Disque** : Espace utilisé (%)
- **Réseau** : Bytes envoyés/reçus
- **Threads** : Nombre de threads actifs
- **Fichiers** : Fichiers ouverts

### Métriques Business

- **Portfolio** : Valeur totale
- **Trades** : Nombre et volume
- **P&L** : Profits et pertes
- **Positions** : Positions actives
- **Win Rate** : Taux de réussite
- **Drawdown** : Perte maximale

## 🔧 Configuration Avancée

### Rotation des Logs

```python
# Configuration automatique
# - Taille max: 50MB par fichier
# - Backup: 10 fichiers
# - Compression: Automatique après 30 jours

# Rotation manuelle
advanced_logger.rotate_logs()

# Archivage
advanced_logger.archive_old_logs(days_old=30)
```

### Monitoring Custom

```python
# Callback de métrique
def get_custom_metric():
    return {"active_users": 42, "queue_size": 15}

performance_analytics.register_metric_callback("custom", get_custom_metric)
```

### Audit Personnalisé

```python
# Décorateur pour audit automatique
@audit_trail.audit_decorator(
    action=AuditAction.CONFIG_CHANGED,
    severity=AuditSeverity.HIGH,
    resource_type="strategy"
)
def update_strategy_config(strategy_id, new_config):
    # Votre code ici
    pass
```

## 📤 Export et Sauvegarde

### Export Complet

```python
# Export toutes données
exports = central_logger.export_all_data(
    start_date=datetime.now() - timedelta(days=7),
    end_date=datetime.now()
)

# Contient:
# - exports['audit']: Audit trail JSON
# - exports['metrics']: Métriques JSON
```

### Export Sélectif

```python
# Export audit seulement
audit_data = audit_trail.export_audit_trail(
    start_date=start_date,
    end_date=end_date,
    format="json"  # ou "csv"
)

# Export métriques
metrics_data = performance_analytics.export_metrics(
    metric_names=["api_response_time", "portfolio_value"],
    hours=24,
    format="json"
)
```

## 🛡️ Sécurité et Intégrité

### Vérification d'Intégrité

```python
# Vérifier une entrée d'audit
is_valid = audit_trail.verify_integrity("audit_entry_id")

# Vérification en lot
for entry_id in audit_ids:
    if not audit_trail.verify_integrity(entry_id):
        print(f"⚠️ Intégrité compromise: {entry_id}")
```

### Checksums

Chaque entrée d'audit inclut un checksum SHA-256 calculé sur :
- ID de l'entrée
- Timestamp
- Action et sévérité
- Données utilisateur
- Valeurs anciennes/nouvelles

## 🧪 Tests et Validation

### Exécuter les Exemples

```bash
# Exemples complets
python examples/logging_system_example.py
```

### Tests Unitaires

```bash
# Tests du système de logging
python -m pytest tests/test_logging_system.py -v
```

## 📝 Bonnes Pratiques

### 1. Niveaux de Log Appropriés

```python
# ✅ Bon
central_logger.log(LogLevel.INFO, "Trade exécuté", LogCategory.TRADING)
central_logger.log(LogLevel.ERROR, "Erreur API", LogCategory.ERROR)

# ❌ Mauvais
central_logger.log(LogLevel.CRITICAL, "Trade exécuté", LogCategory.TRADING)  # Trop sévère
```

### 2. Données Structurées

```python
# ✅ Bon
central_logger.log(
    LogLevel.INFO, "Position ouverte",
    LogCategory.TRADING,
    symbol="BTC/USDT",
    quantity=0.001,
    price=50000
)

# ❌ Mauvais
central_logger.log(LogLevel.INFO, "Position BTC/USDT 0.001 @ 50000", LogCategory.TRADING)
```

### 3. Context Managers

```python
# ✅ Bon - Timing automatique
with central_logger.timed_operation("api_call"):
    response = api.get_price("BTC/USDT")

# ❌ Mauvais - Timing manuel
start = time.time()
response = api.get_price("BTC/USDT")
duration = time.time() - start
central_logger.performance_metric("api_call", duration)
```

### 4. Audit Critique

```python
# ✅ Bon - Audit automatique pour actions critiques
central_logger.emergency_stop("Volatilité excessive", reason="high_vol")

# ❌ Mauvais - Pas d'audit pour action critique
print("Arrêt d'urgence")  # Pas de trace
```

---

**💡 Conseil** : Utilisez le logger centralisé pour toutes vos opérations. Il gère automatiquement l'audit et les métriques selon le contexte.
