"""
📝 Logger avancé avec support multi-format et rotation
Système de logging structuré avec métriques et audit trail
"""

import logging
import logging.handlers
import json
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
import sys
import gzip
import shutil
from dataclasses import dataclass, asdict
from enum import Enum
import uuid

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from utils.notifications import notifier

class LogLevel(Enum):
    """Niveaux de log personnalisés"""
    TRACE = 5
    DEBUG = 10
    INFO = 20
    WARNING = 30
    ERROR = 40
    CRITICAL = 50
    AUDIT = 60  # Niveau spécial pour l'audit

class LogCategory(Enum):
    """Catégories de logs"""
    TRADING = "TRADING"
    STRATEGY = "STRATEGY"
    RISK = "RISK"
    NETWORK = "NETWORK"
    SYSTEM = "SYSTEM"
    AUDIT = "AUDIT"
    PERFORMANCE = "PERFORMANCE"
    ERROR = "ERROR"

@dataclass
class LogEntry:
    """Entrée de log structurée"""
    timestamp: datetime
    level: str
    category: str
    message: str
    module: str
    function: str
    line_number: int
    thread_id: str
    session_id: str
    user_id: Optional[str] = None
    trade_id: Optional[str] = None
    strategy_id: Optional[str] = None
    symbol: Optional[str] = None
    extra_data: Optional[Dict[str, Any]] = None
    execution_time: Optional[float] = None
    memory_usage: Optional[int] = None

class StructuredFormatter(logging.Formatter):
    """Formateur de logs structuré"""
    
    def __init__(self, format_type: str = "json"):
        super().__init__()
        self.format_type = format_type
        self.session_id = str(uuid.uuid4())[:8]
    
    def format(self, record: logging.LogRecord) -> str:
        """Formate un enregistrement de log"""
        # Extraire les données supplémentaires
        extra_data = {}
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname', 
                          'filename', 'module', 'lineno', 'funcName', 'created', 
                          'msecs', 'relativeCreated', 'thread', 'threadName', 
                          'processName', 'process', 'getMessage', 'exc_info', 
                          'exc_text', 'stack_info']:
                extra_data[key] = value
        
        # Créer l'entrée structurée
        log_entry = LogEntry(
            timestamp=datetime.fromtimestamp(record.created),
            level=record.levelname,
            category=getattr(record, 'category', LogCategory.SYSTEM.value),
            message=record.getMessage(),
            module=record.module,
            function=record.funcName,
            line_number=record.lineno,
            thread_id=str(record.thread),
            session_id=self.session_id,
            user_id=getattr(record, 'user_id', None),
            trade_id=getattr(record, 'trade_id', None),
            strategy_id=getattr(record, 'strategy_id', None),
            symbol=getattr(record, 'symbol', None),
            extra_data=extra_data if extra_data else None,
            execution_time=getattr(record, 'execution_time', None),
            memory_usage=getattr(record, 'memory_usage', None)
        )
        
        if self.format_type == "json":
            return json.dumps(asdict(log_entry), default=str, ensure_ascii=False)
        elif self.format_type == "human":
            return self._format_human_readable(log_entry)
        else:
            return super().format(record)
    
    def _format_human_readable(self, entry: LogEntry) -> str:
        """Formate en format lisible par l'humain"""
        timestamp = entry.timestamp.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
        
        base = f"[{timestamp}] {entry.level:<8} [{entry.category}] {entry.message}"
        
        if entry.symbol:
            base += f" | Symbol: {entry.symbol}"
        
        if entry.trade_id:
            base += f" | Trade: {entry.trade_id}"
        
        if entry.execution_time:
            base += f" | Time: {entry.execution_time:.3f}s"
        
        if entry.extra_data:
            base += f" | Extra: {json.dumps(entry.extra_data, default=str)}"
        
        return base

class AdvancedLogger:
    """Logger avancé avec fonctionnalités étendues"""
    
    def __init__(self, name: str = "botCrypto", log_dir: str = "logs"):
        self.name = name
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        # Créer les répertoires de logs
        (self.log_dir / "trading").mkdir(exist_ok=True)
        (self.log_dir / "audit").mkdir(exist_ok=True)
        (self.log_dir / "performance").mkdir(exist_ok=True)
        (self.log_dir / "errors").mkdir(exist_ok=True)
        (self.log_dir / "archived").mkdir(exist_ok=True)
        
        # Configuration des loggers
        self.loggers = {}
        self.handlers = {}
        
        # Métriques de logging
        self.log_counts = {level.name: 0 for level in LogLevel}
        self.log_rates = {}  # Messages par minute
        self.last_rate_calculation = time.time()
        
        # Session et contexte
        self.session_id = str(uuid.uuid4())[:8]
        self.context_stack = []
        
        # Thread safety
        self.lock = threading.Lock()
        
        # Initialiser les loggers
        self._setup_loggers()
        
        print(f"📝 Logger avancé initialisé: {name} (Session: {self.session_id})")
    
    def _setup_loggers(self):
        """Configure tous les loggers"""
        # Logger principal
        self._create_logger("main", "main.log", LogLevel.INFO)
        
        # Loggers spécialisés
        self._create_logger("trading", "trading/trading.log", LogLevel.DEBUG)
        self._create_logger("audit", "audit/audit.log", LogLevel.AUDIT)
        self._create_logger("performance", "performance/performance.log", LogLevel.INFO)
        self._create_logger("errors", "errors/errors.log", LogLevel.WARNING)
        
        # Logger pour les métriques (JSON uniquement)
        self._create_logger("metrics", "metrics.jsonl", LogLevel.INFO, format_type="json")
    
    def _create_logger(self, logger_name: str, log_file: str, 
                      level: LogLevel, format_type: str = "human"):
        """Crée un logger avec configuration spécifique"""
        logger = logging.getLogger(f"{self.name}.{logger_name}")
        logger.setLevel(level.value)
        
        # Éviter la duplication des handlers
        if logger.handlers:
            logger.handlers.clear()
        
        # Handler pour fichier avec rotation
        file_path = self.log_dir / log_file
        file_handler = logging.handlers.RotatingFileHandler(
            file_path,
            maxBytes=50 * 1024 * 1024,  # 50MB
            backupCount=10,
            encoding='utf-8'
        )
        
        # Handler pour console (seulement pour le logger principal)
        if logger_name == "main":
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(LogLevel.INFO.value)
            console_handler.setFormatter(StructuredFormatter("human"))
            logger.addHandler(console_handler)
        
        # Formateur
        formatter = StructuredFormatter(format_type)
        file_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        
        # Stocker les références
        self.loggers[logger_name] = logger
        self.handlers[logger_name] = file_handler
    
    def log(self, level: LogLevel, message: str, category: LogCategory = LogCategory.SYSTEM,
            **kwargs):
        """Log un message avec niveau et catégorie"""
        with self.lock:
            # Choisir le logger approprié
            logger_name = self._get_logger_for_category(category)
            logger = self.loggers.get(logger_name, self.loggers["main"])
            
            # Ajouter le contexte
            extra = {
                'category': category.value,
                'session_id': self.session_id,
                **kwargs
            }
            
            # Ajouter le contexte de la pile
            if self.context_stack:
                extra['context'] = self.context_stack[-1]
            
            # Logger le message
            logger.log(level.value, message, extra=extra)
            
            # Mettre à jour les métriques
            self._update_metrics(level)
    
    def _get_logger_for_category(self, category: LogCategory) -> str:
        """Détermine le logger à utiliser selon la catégorie"""
        mapping = {
            LogCategory.TRADING: "trading",
            LogCategory.AUDIT: "audit",
            LogCategory.PERFORMANCE: "performance",
            LogCategory.ERROR: "errors",
            LogCategory.RISK: "trading",
            LogCategory.STRATEGY: "trading"
        }
        return mapping.get(category, "main")
    
    def _update_metrics(self, level: LogLevel):
        """Met à jour les métriques de logging"""
        self.log_counts[level.name] += 1
        
        # Calculer le taux (messages par minute)
        current_time = time.time()
        if current_time - self.last_rate_calculation >= 60:  # Chaque minute
            for level_name in self.log_counts:
                self.log_rates[level_name] = self.log_counts[level_name]
                self.log_counts[level_name] = 0
            self.last_rate_calculation = current_time
    
    # Méthodes de convenance
    def trace(self, message: str, category: LogCategory = LogCategory.SYSTEM, **kwargs):
        self.log(LogLevel.TRACE, message, category, **kwargs)
    
    def debug(self, message: str, category: LogCategory = LogCategory.SYSTEM, **kwargs):
        self.log(LogLevel.DEBUG, message, category, **kwargs)
    
    def info(self, message: str, category: LogCategory = LogCategory.SYSTEM, **kwargs):
        self.log(LogLevel.INFO, message, category, **kwargs)
    
    def warning(self, message: str, category: LogCategory = LogCategory.SYSTEM, **kwargs):
        self.log(LogLevel.WARNING, message, category, **kwargs)
    
    def error(self, message: str, category: LogCategory = LogCategory.ERROR, **kwargs):
        self.log(LogLevel.ERROR, message, category, **kwargs)
    
    def critical(self, message: str, category: LogCategory = LogCategory.ERROR, **kwargs):
        self.log(LogLevel.CRITICAL, message, category, **kwargs)
        
        # Notification automatique pour les erreurs critiques
        try:
            notifier.send_telegram(f"🚨 ERREUR CRITIQUE: {message}")
        except:
            pass  # Ne pas faire échouer le logging si la notification échoue
    
    def audit(self, message: str, **kwargs):
        """Log d'audit spécialisé"""
        self.log(LogLevel.AUDIT, message, LogCategory.AUDIT, **kwargs)
    
    def performance(self, message: str, execution_time: float = None, **kwargs):
        """Log de performance avec temps d'exécution"""
        if execution_time:
            kwargs['execution_time'] = execution_time
        self.log(LogLevel.INFO, message, LogCategory.PERFORMANCE, **kwargs)
    
    def trade(self, message: str, symbol: str = None, trade_id: str = None, **kwargs):
        """Log spécialisé pour le trading"""
        if symbol:
            kwargs['symbol'] = symbol
        if trade_id:
            kwargs['trade_id'] = trade_id
        self.log(LogLevel.INFO, message, LogCategory.TRADING, **kwargs)
    
    def push_context(self, context: str):
        """Ajoute un contexte à la pile"""
        self.context_stack.append(context)
    
    def pop_context(self):
        """Retire le dernier contexte de la pile"""
        if self.context_stack:
            return self.context_stack.pop()
        return None
    
    def context_manager(self, context: str):
        """Context manager pour le logging contextuel"""
        return LogContext(self, context)
    
    def get_log_stats(self) -> Dict[str, Any]:
        """Retourne les statistiques de logging"""
        return {
            'session_id': self.session_id,
            'log_counts': self.log_counts.copy(),
            'log_rates': self.log_rates.copy(),
            'active_loggers': list(self.loggers.keys()),
            'log_directory': str(self.log_dir),
            'context_depth': len(self.context_stack)
        }
    
    def rotate_logs(self):
        """Force la rotation des logs"""
        for handler_name, handler in self.handlers.items():
            if hasattr(handler, 'doRollover'):
                handler.doRollover()
                self.info(f"Logs rotés pour {handler_name}", LogCategory.SYSTEM)
    
    def archive_old_logs(self, days_old: int = 30):
        """Archive les anciens logs"""
        cutoff_date = datetime.now() - timedelta(days=days_old)
        archived_count = 0
        
        for log_file in self.log_dir.rglob("*.log*"):
            if log_file.stat().st_mtime < cutoff_date.timestamp():
                # Compresser et déplacer vers archives
                archive_path = self.log_dir / "archived" / f"{log_file.name}.gz"
                
                with open(log_file, 'rb') as f_in:
                    with gzip.open(archive_path, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)
                
                log_file.unlink()  # Supprimer l'original
                archived_count += 1
        
        if archived_count > 0:
            self.info(f"Archivage terminé: {archived_count} fichiers", LogCategory.SYSTEM)
    
    def search_logs(self, query: str, category: LogCategory = None, 
                   start_date: datetime = None, end_date: datetime = None,
                   max_results: int = 100) -> List[Dict[str, Any]]:
        """Recherche dans les logs"""
        results = []
        
        # Déterminer les fichiers à rechercher
        if category:
            logger_name = self._get_logger_for_category(category)
            log_files = [self.log_dir / f"{logger_name}.log"]
        else:
            log_files = list(self.log_dir.rglob("*.log"))
        
        for log_file in log_files:
            if not log_file.exists():
                continue
            
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    for line_num, line in enumerate(f, 1):
                        if query.lower() in line.lower():
                            # Parser la ligne si c'est du JSON
                            try:
                                log_data = json.loads(line)
                                log_date = datetime.fromisoformat(log_data['timestamp'])
                                
                                # Filtrer par date si spécifié
                                if start_date and log_date < start_date:
                                    continue
                                if end_date and log_date > end_date:
                                    continue
                                
                                results.append({
                                    'file': str(log_file),
                                    'line_number': line_num,
                                    'data': log_data
                                })
                            except json.JSONDecodeError:
                                # Ligne de log non-JSON
                                results.append({
                                    'file': str(log_file),
                                    'line_number': line_num,
                                    'raw_line': line.strip()
                                })
                            
                            if len(results) >= max_results:
                                break
            except Exception as e:
                self.error(f"Erreur recherche dans {log_file}: {e}")
        
        return results

class LogContext:
    """Context manager pour le logging contextuel"""
    
    def __init__(self, logger: AdvancedLogger, context: str):
        self.logger = logger
        self.context = context
    
    def __enter__(self):
        self.logger.push_context(self.context)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.logger.pop_context()

# Instance globale
advanced_logger = AdvancedLogger()
