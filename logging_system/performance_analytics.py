"""
📊 Système d'analytics et métriques de performance
Collecte et analyse des métriques système et business
"""

import time
import threading
import psutil
import gc
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
from pathlib import Path
import sys
import json
import statistics

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from logging_system.advanced_logger import advanced_logger, LogCategory

@dataclass
class PerformanceMetric:
    """Métrique de performance"""
    name: str
    value: float
    unit: str
    timestamp: datetime
    category: str
    tags: Optional[Dict[str, str]] = None

@dataclass
class SystemMetrics:
    """Métriques système"""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    memory_available_mb: float
    disk_usage_percent: float
    disk_free_gb: float
    network_bytes_sent: int
    network_bytes_recv: int
    active_threads: int
    open_files: int
    python_objects: int

@dataclass
class BusinessMetrics:
    """Métriques business"""
    timestamp: datetime
    total_trades: int
    successful_trades: int
    failed_trades: int
    total_volume: float
    total_pnl: float
    active_positions: int
    portfolio_value: float
    daily_return: float
    max_drawdown: float
    win_rate: float

class PerformanceTimer:
    """Timer pour mesurer les performances"""
    
    def __init__(self, name: str, category: str = "performance"):
        self.name = name
        self.category = category
        self.start_time = None
        self.end_time = None
    
    def __enter__(self):
        self.start_time = time.perf_counter()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.end_time = time.perf_counter()
        duration = self.end_time - self.start_time
        
        # Enregistrer la métrique
        performance_analytics.record_metric(
            name=self.name,
            value=duration,
            unit="seconds",
            category=self.category
        )

class PerformanceAnalytics:
    """Système d'analytics de performance"""
    
    def __init__(self, retention_hours: int = 24):
        self.retention_hours = retention_hours
        
        # Stockage des métriques
        self.metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=10000))
        self.system_metrics: deque = deque(maxlen=1000)
        self.business_metrics: deque = deque(maxlen=1000)
        
        # Agrégations en temps réel
        self.aggregations: Dict[str, Dict[str, float]] = defaultdict(dict)
        
        # Monitoring système
        self.is_monitoring = False
        self.monitor_thread = None
        self.monitor_interval = 60  # secondes
        
        # Callbacks pour métriques custom
        self.metric_callbacks: Dict[str, Callable] = {}
        
        # Thread safety
        self.lock = threading.Lock()
        
        # Métriques de base
        self.start_time = datetime.now()
        self.last_cleanup = datetime.now()
        
        advanced_logger.info("📊 Analytics de performance initialisé", LogCategory.PERFORMANCE)
    
    def record_metric(self, name: str, value: float, unit: str = "", 
                     category: str = "custom", tags: Optional[Dict[str, str]] = None):
        """Enregistre une métrique"""
        with self.lock:
            metric = PerformanceMetric(
                name=name,
                value=value,
                unit=unit,
                timestamp=datetime.now(),
                category=category,
                tags=tags
            )
            
            self.metrics[name].append(metric)
            
            # Mettre à jour les agrégations
            self._update_aggregations(name, value)
            
            # Log si c'est une métrique importante
            if category in ["trading", "system", "error"]:
                advanced_logger.performance(
                    f"Métrique {name}: {value} {unit}",
                    category=LogCategory.PERFORMANCE,
                    metric_name=name,
                    metric_value=value,
                    metric_unit=unit,
                    metric_category=category
                )
    
    def _update_aggregations(self, name: str, value: float):
        """Met à jour les agrégations en temps réel"""
        if name not in self.aggregations:
            self.aggregations[name] = {
                'count': 0,
                'sum': 0,
                'min': float('inf'),
                'max': float('-inf'),
                'avg': 0
            }
        
        agg = self.aggregations[name]
        agg['count'] += 1
        agg['sum'] += value
        agg['min'] = min(agg['min'], value)
        agg['max'] = max(agg['max'], value)
        agg['avg'] = agg['sum'] / agg['count']
    
    def start_system_monitoring(self, interval: int = 60):
        """Démarre le monitoring système"""
        if self.is_monitoring:
            advanced_logger.warning("⚠️ Monitoring déjà en cours", LogCategory.PERFORMANCE)
            return
        
        self.monitor_interval = interval
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitoring_loop,
            daemon=True
        )
        self.monitor_thread.start()
        
        advanced_logger.info(f"🚀 Monitoring système démarré (intervalle: {interval}s)", LogCategory.PERFORMANCE)
    
    def stop_system_monitoring(self):
        """Arrête le monitoring système"""
        if not self.is_monitoring:
            return
        
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        
        advanced_logger.info("🛑 Monitoring système arrêté", LogCategory.PERFORMANCE)
    
    def _monitoring_loop(self):
        """Boucle de monitoring système"""
        while self.is_monitoring:
            try:
                # Collecter les métriques système
                self._collect_system_metrics()
                
                # Collecter les métriques business si disponibles
                self._collect_business_metrics()
                
                # Nettoyer les anciennes métriques
                self._cleanup_old_metrics()
                
                # Exécuter les callbacks custom
                self._execute_metric_callbacks()
                
                time.sleep(self.monitor_interval)
                
            except Exception as e:
                advanced_logger.error(f"Erreur monitoring: {e}", LogCategory.PERFORMANCE)
                time.sleep(self.monitor_interval)
    
    def _collect_system_metrics(self):
        """Collecte les métriques système"""
        try:
            # CPU et mémoire
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            
            # Disque
            disk = psutil.disk_usage('/')
            
            # Réseau
            network = psutil.net_io_counters()
            
            # Processus Python
            process = psutil.Process()
            
            metrics = SystemMetrics(
                timestamp=datetime.now(),
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                memory_used_mb=memory.used / 1024 / 1024,
                memory_available_mb=memory.available / 1024 / 1024,
                disk_usage_percent=disk.used / disk.total * 100,
                disk_free_gb=disk.free / 1024 / 1024 / 1024,
                network_bytes_sent=network.bytes_sent,
                network_bytes_recv=network.bytes_recv,
                active_threads=threading.active_count(),
                open_files=len(process.open_files()),
                python_objects=len(gc.get_objects())
            )
            
            self.system_metrics.append(metrics)
            
            # Enregistrer comme métriques individuelles
            self.record_metric("cpu_percent", cpu_percent, "%", "system")
            self.record_metric("memory_percent", memory.percent, "%", "system")
            self.record_metric("disk_usage_percent", metrics.disk_usage_percent, "%", "system")
            self.record_metric("active_threads", metrics.active_threads, "count", "system")
            
        except Exception as e:
            advanced_logger.error(f"Erreur collecte métriques système: {e}", LogCategory.PERFORMANCE)
    
    def _collect_business_metrics(self):
        """Collecte les métriques business"""
        try:
            # Importer ici pour éviter les dépendances circulaires
            from risk_management.portfolio_manager import portfolio_manager
            
            # Récupérer les métriques du portefeuille
            summary = portfolio_manager.get_portfolio_summary()
            risk_metrics = portfolio_manager.get_risk_metrics()
            
            # Calculer les métriques business
            total_trades = len(portfolio_manager.trade_history)
            successful_trades = len([t for t in portfolio_manager.trade_history 
                                   if t.get('pnl', 0) > 0])
            failed_trades = total_trades - successful_trades
            
            metrics = BusinessMetrics(
                timestamp=datetime.now(),
                total_trades=total_trades,
                successful_trades=successful_trades,
                failed_trades=failed_trades,
                total_volume=sum(t.get('value', 0) for t in portfolio_manager.trade_history),
                total_pnl=risk_metrics.realized_pnl + risk_metrics.unrealized_pnl,
                active_positions=len(portfolio_manager.positions),
                portfolio_value=summary['portfolio_value'],
                daily_return=summary.get('daily_return', 0),
                max_drawdown=summary['max_drawdown'],
                win_rate=successful_trades / total_trades * 100 if total_trades > 0 else 0
            )
            
            self.business_metrics.append(metrics)
            
            # Enregistrer comme métriques individuelles
            self.record_metric("portfolio_value", metrics.portfolio_value, "USDT", "business")
            self.record_metric("total_pnl", metrics.total_pnl, "USDT", "business")
            self.record_metric("active_positions", metrics.active_positions, "count", "business")
            self.record_metric("win_rate", metrics.win_rate, "%", "business")
            
        except Exception as e:
            # Ne pas logger l'erreur si les modules ne sont pas disponibles
            pass
    
    def _cleanup_old_metrics(self):
        """Nettoie les anciennes métriques"""
        if datetime.now() - self.last_cleanup < timedelta(hours=1):
            return
        
        cutoff_time = datetime.now() - timedelta(hours=self.retention_hours)
        
        with self.lock:
            for metric_name, metric_list in self.metrics.items():
                # Filtrer les métriques trop anciennes
                while metric_list and metric_list[0].timestamp < cutoff_time:
                    metric_list.popleft()
        
        self.last_cleanup = datetime.now()
    
    def _execute_metric_callbacks(self):
        """Exécute les callbacks de métriques custom"""
        for name, callback in self.metric_callbacks.items():
            try:
                result = callback()
                if isinstance(result, (int, float)):
                    self.record_metric(name, result, category="callback")
                elif isinstance(result, dict):
                    for key, value in result.items():
                        if isinstance(value, (int, float)):
                            self.record_metric(f"{name}_{key}", value, category="callback")
            except Exception as e:
                advanced_logger.error(f"Erreur callback {name}: {e}", LogCategory.PERFORMANCE)
    
    def register_metric_callback(self, name: str, callback: Callable):
        """Enregistre un callback pour collecter des métriques custom"""
        self.metric_callbacks[name] = callback
        advanced_logger.info(f"Callback métrique enregistré: {name}", LogCategory.PERFORMANCE)
    
    def get_metric_statistics(self, metric_name: str, 
                            hours: int = 1) -> Optional[Dict[str, float]]:
        """Calcule les statistiques pour une métrique"""
        if metric_name not in self.metrics:
            return None
        
        cutoff_time = datetime.now() - timedelta(hours=hours)
        values = [m.value for m in self.metrics[metric_name] 
                 if m.timestamp >= cutoff_time]
        
        if not values:
            return None
        
        return {
            'count': len(values),
            'min': min(values),
            'max': max(values),
            'avg': statistics.mean(values),
            'median': statistics.median(values),
            'std_dev': statistics.stdev(values) if len(values) > 1 else 0,
            'p95': self._percentile(values, 95),
            'p99': self._percentile(values, 99)
        }
    
    def _percentile(self, values: List[float], percentile: int) -> float:
        """Calcule un percentile"""
        sorted_values = sorted(values)
        index = int(len(sorted_values) * percentile / 100)
        return sorted_values[min(index, len(sorted_values) - 1)]
    
    def get_system_health_score(self) -> float:
        """Calcule un score de santé système (0-100)"""
        if not self.system_metrics:
            return 100
        
        latest = self.system_metrics[-1]
        score = 100
        
        # Pénalités basées sur les métriques système
        if latest.cpu_percent > 80:
            score -= (latest.cpu_percent - 80) * 2
        
        if latest.memory_percent > 80:
            score -= (latest.memory_percent - 80) * 2
        
        if latest.disk_usage_percent > 90:
            score -= (latest.disk_usage_percent - 90) * 5
        
        # Bonus pour stabilité
        if len(self.system_metrics) > 10:
            recent_cpu = [m.cpu_percent for m in list(self.system_metrics)[-10:]]
            cpu_stability = 100 - statistics.stdev(recent_cpu)
            score += min(10, cpu_stability / 10)
        
        return max(0, min(100, score))
    
    def get_performance_report(self, hours: int = 24) -> Dict[str, Any]:
        """Génère un rapport de performance complet"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        # Métriques système récentes
        recent_system = [m for m in self.system_metrics if m.timestamp >= cutoff_time]
        
        # Métriques business récentes
        recent_business = [m for m in self.business_metrics if m.timestamp >= cutoff_time]
        
        # Top métriques par catégorie
        top_metrics = {}
        for metric_name in self.metrics:
            stats = self.get_metric_statistics(metric_name, hours)
            if stats:
                top_metrics[metric_name] = stats
        
        # Uptime
        uptime = datetime.now() - self.start_time
        
        return {
            'report_period_hours': hours,
            'generated_at': datetime.now().isoformat(),
            'uptime_seconds': uptime.total_seconds(),
            'system_health_score': self.get_system_health_score(),
            'system_metrics': {
                'samples_count': len(recent_system),
                'avg_cpu_percent': statistics.mean([m.cpu_percent for m in recent_system]) if recent_system else 0,
                'avg_memory_percent': statistics.mean([m.memory_percent for m in recent_system]) if recent_system else 0,
                'avg_active_threads': statistics.mean([m.active_threads for m in recent_system]) if recent_system else 0
            },
            'business_metrics': {
                'samples_count': len(recent_business),
                'latest_portfolio_value': recent_business[-1].portfolio_value if recent_business else 0,
                'latest_win_rate': recent_business[-1].win_rate if recent_business else 0,
                'total_trades_period': recent_business[-1].total_trades if recent_business else 0
            },
            'top_metrics': dict(list(top_metrics.items())[:20]),  # Top 20
            'aggregations': dict(self.aggregations)
        }
    
    def export_metrics(self, metric_names: Optional[List[str]] = None,
                      hours: int = 24, format: str = "json") -> str:
        """Exporte les métriques"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        export_data = {
            'export_timestamp': datetime.now().isoformat(),
            'period_hours': hours,
            'metrics': {}
        }
        
        # Sélectionner les métriques à exporter
        metrics_to_export = metric_names if metric_names else list(self.metrics.keys())
        
        for metric_name in metrics_to_export:
            if metric_name in self.metrics:
                recent_metrics = [
                    asdict(m) for m in self.metrics[metric_name]
                    if m.timestamp >= cutoff_time
                ]
                export_data['metrics'][metric_name] = recent_metrics
        
        if format == "json":
            return json.dumps(export_data, default=str, indent=2)
        else:
            raise ValueError(f"Format non supporté: {format}")
    
    def timer(self, name: str, category: str = "performance"):
        """Retourne un timer de performance"""
        return PerformanceTimer(name, category)

# Instance globale
performance_analytics = PerformanceAnalytics()
