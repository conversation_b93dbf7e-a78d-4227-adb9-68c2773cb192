"""
🔍 Système d'audit trail complet
Traçabilité de toutes les opérations critiques avec intégrité des données
"""

import hashlib
import json
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
import sys
import sqlite3
import uuid

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from logging_system.advanced_logger import advanced_logger, LogCategory

class AuditAction(Enum):
    """Types d'actions auditées"""
    USER_LOGIN = "USER_LOGIN"
    USER_LOGOUT = "USER_LOGOUT"
    TRADE_EXECUTED = "TRADE_EXECUTED"
    TRADE_CANCELLED = "TRADE_CANCELLED"
    POSITION_OPENED = "POSITION_OPENED"
    POSITION_CLOSED = "POSITION_CLOSED"
    STRATEGY_STARTED = "STRATEGY_STARTED"
    STRATEGY_STOPPED = "STRATEGY_STOPPED"
    CONFIG_CHANGED = "CONFIG_CHANGED"
    RISK_LIMIT_CHANGED = "RISK_LIMIT_CHANGED"
    EMERGENCY_STOP = "EMERGENCY_STOP"
    FUNDS_DEPOSITED = "FUNDS_DEPOSITED"
    FUNDS_WITHDRAWN = "FUNDS_WITHDRAWN"
    API_KEY_CREATED = "API_KEY_CREATED"
    API_KEY_DELETED = "API_KEY_DELETED"
    SYSTEM_START = "SYSTEM_START"
    SYSTEM_STOP = "SYSTEM_STOP"
    DATA_EXPORT = "DATA_EXPORT"
    DATA_IMPORT = "DATA_IMPORT"

class AuditSeverity(Enum):
    """Niveaux de sévérité pour l'audit"""
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"

@dataclass
class AuditEntry:
    """Entrée d'audit trail"""
    id: str
    timestamp: datetime
    action: AuditAction
    severity: AuditSeverity
    user_id: Optional[str]
    session_id: str
    ip_address: Optional[str]
    user_agent: Optional[str]
    resource_type: Optional[str]
    resource_id: Optional[str]
    old_values: Optional[Dict[str, Any]]
    new_values: Optional[Dict[str, Any]]
    additional_data: Optional[Dict[str, Any]]
    success: bool
    error_message: Optional[str]
    execution_time: Optional[float]
    checksum: Optional[str]

class AuditTrail:
    """Système d'audit trail avec intégrité des données"""
    
    def __init__(self, db_path: str = "logs/audit.db"):
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(exist_ok=True)
        
        # Thread safety
        self.lock = threading.Lock()
        
        # Session tracking
        self.session_id = str(uuid.uuid4())[:8]
        self.current_user = None
        
        # Initialiser la base de données
        self._init_database()
        
        # Enregistrer le démarrage du système
        self.log_audit(
            action=AuditAction.SYSTEM_START,
            severity=AuditSeverity.MEDIUM,
            additional_data={'version': '2.0', 'session_id': self.session_id}
        )
        
        advanced_logger.info(f"🔍 Audit trail initialisé (Session: {self.session_id})", LogCategory.AUDIT)
    
    def _init_database(self):
        """Initialise la base de données SQLite pour l'audit"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS audit_entries (
                    id TEXT PRIMARY KEY,
                    timestamp TEXT NOT NULL,
                    action TEXT NOT NULL,
                    severity TEXT NOT NULL,
                    user_id TEXT,
                    session_id TEXT NOT NULL,
                    ip_address TEXT,
                    user_agent TEXT,
                    resource_type TEXT,
                    resource_id TEXT,
                    old_values TEXT,
                    new_values TEXT,
                    additional_data TEXT,
                    success BOOLEAN NOT NULL,
                    error_message TEXT,
                    execution_time REAL,
                    checksum TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Index pour les recherches fréquentes
            conn.execute("CREATE INDEX IF NOT EXISTS idx_timestamp ON audit_entries(timestamp)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_action ON audit_entries(action)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_user_id ON audit_entries(user_id)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_session_id ON audit_entries(session_id)")
            
            conn.commit()
    
    def log_audit(self, action: AuditAction, severity: AuditSeverity = AuditSeverity.MEDIUM,
                  user_id: Optional[str] = None, ip_address: Optional[str] = None,
                  user_agent: Optional[str] = None, resource_type: Optional[str] = None,
                  resource_id: Optional[str] = None, old_values: Optional[Dict] = None,
                  new_values: Optional[Dict] = None, additional_data: Optional[Dict] = None,
                  success: bool = True, error_message: Optional[str] = None,
                  execution_time: Optional[float] = None) -> str:
        """
        Enregistre une entrée d'audit
        
        Returns:
            ID de l'entrée d'audit créée
        """
        with self.lock:
            # Générer un ID unique
            audit_id = str(uuid.uuid4())
            
            # Créer l'entrée d'audit
            entry = AuditEntry(
                id=audit_id,
                timestamp=datetime.now(),
                action=action,
                severity=severity,
                user_id=user_id or self.current_user,
                session_id=self.session_id,
                ip_address=ip_address,
                user_agent=user_agent,
                resource_type=resource_type,
                resource_id=resource_id,
                old_values=old_values,
                new_values=new_values,
                additional_data=additional_data,
                success=success,
                error_message=error_message,
                execution_time=execution_time,
                checksum=None  # Sera calculé après
            )
            
            # Calculer le checksum pour l'intégrité
            entry.checksum = self._calculate_checksum(entry)
            
            # Sauvegarder en base
            self._save_to_database(entry)
            
            # Logger dans le système de logs
            self._log_to_system(entry)
            
            return audit_id
    
    def _calculate_checksum(self, entry: AuditEntry) -> str:
        """Calcule un checksum pour vérifier l'intégrité"""
        # Créer une représentation canonique de l'entrée
        data_for_hash = {
            'id': entry.id,
            'timestamp': entry.timestamp.isoformat(),
            'action': entry.action.value,
            'severity': entry.severity.value,
            'user_id': entry.user_id,
            'session_id': entry.session_id,
            'resource_type': entry.resource_type,
            'resource_id': entry.resource_id,
            'old_values': entry.old_values,
            'new_values': entry.new_values,
            'additional_data': entry.additional_data,
            'success': entry.success,
            'error_message': entry.error_message
        }
        
        # Sérialiser de manière déterministe
        canonical_json = json.dumps(data_for_hash, sort_keys=True, default=str)
        
        # Calculer le hash SHA-256
        return hashlib.sha256(canonical_json.encode('utf-8')).hexdigest()
    
    def _save_to_database(self, entry: AuditEntry):
        """Sauvegarde l'entrée en base de données"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT INTO audit_entries (
                    id, timestamp, action, severity, user_id, session_id,
                    ip_address, user_agent, resource_type, resource_id,
                    old_values, new_values, additional_data, success,
                    error_message, execution_time, checksum
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                entry.id,
                entry.timestamp.isoformat(),
                entry.action.value,
                entry.severity.value,
                entry.user_id,
                entry.session_id,
                entry.ip_address,
                entry.user_agent,
                entry.resource_type,
                entry.resource_id,
                json.dumps(entry.old_values) if entry.old_values else None,
                json.dumps(entry.new_values) if entry.new_values else None,
                json.dumps(entry.additional_data) if entry.additional_data else None,
                entry.success,
                entry.error_message,
                entry.execution_time,
                entry.checksum
            ))
            conn.commit()
    
    def _log_to_system(self, entry: AuditEntry):
        """Log l'entrée d'audit dans le système de logs"""
        message = f"AUDIT: {entry.action.value}"
        
        if entry.resource_type and entry.resource_id:
            message += f" | {entry.resource_type}:{entry.resource_id}"
        
        if not entry.success and entry.error_message:
            message += f" | ERROR: {entry.error_message}"
        
        # Données supplémentaires pour le log structuré
        extra_data = {
            'audit_id': entry.id,
            'action': entry.action.value,
            'severity': entry.severity.value,
            'success': entry.success,
            'user_id': entry.user_id,
            'resource_type': entry.resource_type,
            'resource_id': entry.resource_id
        }
        
        if entry.execution_time:
            extra_data['execution_time'] = entry.execution_time
        
        advanced_logger.audit(message, **extra_data)
    
    def verify_integrity(self, entry_id: str) -> bool:
        """Vérifie l'intégrité d'une entrée d'audit"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("SELECT * FROM audit_entries WHERE id = ?", (entry_id,))
            row = cursor.fetchone()
            
            if not row:
                return False
            
            # Reconstruire l'entrée
            entry = self._row_to_entry(row)
            
            # Recalculer le checksum
            stored_checksum = entry.checksum
            entry.checksum = None
            calculated_checksum = self._calculate_checksum(entry)
            
            return stored_checksum == calculated_checksum
    
    def _row_to_entry(self, row) -> AuditEntry:
        """Convertit une ligne de base en AuditEntry"""
        return AuditEntry(
            id=row[0],
            timestamp=datetime.fromisoformat(row[1]),
            action=AuditAction(row[2]),
            severity=AuditSeverity(row[3]),
            user_id=row[4],
            session_id=row[5],
            ip_address=row[6],
            user_agent=row[7],
            resource_type=row[8],
            resource_id=row[9],
            old_values=json.loads(row[10]) if row[10] else None,
            new_values=json.loads(row[11]) if row[11] else None,
            additional_data=json.loads(row[12]) if row[12] else None,
            success=bool(row[13]),
            error_message=row[14],
            execution_time=row[15],
            checksum=row[16]
        )
    
    def search_audit_trail(self, action: Optional[AuditAction] = None,
                          user_id: Optional[str] = None,
                          start_date: Optional[datetime] = None,
                          end_date: Optional[datetime] = None,
                          resource_type: Optional[str] = None,
                          success: Optional[bool] = None,
                          limit: int = 100) -> List[AuditEntry]:
        """Recherche dans l'audit trail"""
        query = "SELECT * FROM audit_entries WHERE 1=1"
        params = []
        
        if action:
            query += " AND action = ?"
            params.append(action.value)
        
        if user_id:
            query += " AND user_id = ?"
            params.append(user_id)
        
        if start_date:
            query += " AND timestamp >= ?"
            params.append(start_date.isoformat())
        
        if end_date:
            query += " AND timestamp <= ?"
            params.append(end_date.isoformat())
        
        if resource_type:
            query += " AND resource_type = ?"
            params.append(resource_type)
        
        if success is not None:
            query += " AND success = ?"
            params.append(success)
        
        query += " ORDER BY timestamp DESC LIMIT ?"
        params.append(limit)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute(query, params)
            rows = cursor.fetchall()
            
            return [self._row_to_entry(row) for row in rows]
    
    def get_audit_statistics(self, days: int = 30) -> Dict[str, Any]:
        """Génère des statistiques d'audit"""
        start_date = datetime.now() - timedelta(days=days)
        
        with sqlite3.connect(self.db_path) as conn:
            # Total des entrées
            cursor = conn.execute(
                "SELECT COUNT(*) FROM audit_entries WHERE timestamp >= ?",
                (start_date.isoformat(),)
            )
            total_entries = cursor.fetchone()[0]
            
            # Par action
            cursor = conn.execute("""
                SELECT action, COUNT(*) 
                FROM audit_entries 
                WHERE timestamp >= ? 
                GROUP BY action 
                ORDER BY COUNT(*) DESC
            """, (start_date.isoformat(),))
            by_action = dict(cursor.fetchall())
            
            # Par utilisateur
            cursor = conn.execute("""
                SELECT user_id, COUNT(*) 
                FROM audit_entries 
                WHERE timestamp >= ? AND user_id IS NOT NULL
                GROUP BY user_id 
                ORDER BY COUNT(*) DESC
            """, (start_date.isoformat(),))
            by_user = dict(cursor.fetchall())
            
            # Taux de succès
            cursor = conn.execute("""
                SELECT success, COUNT(*) 
                FROM audit_entries 
                WHERE timestamp >= ? 
                GROUP BY success
            """, (start_date.isoformat(),))
            success_stats = dict(cursor.fetchall())
            
            # Activité par jour
            cursor = conn.execute("""
                SELECT DATE(timestamp) as date, COUNT(*) 
                FROM audit_entries 
                WHERE timestamp >= ? 
                GROUP BY DATE(timestamp) 
                ORDER BY date DESC
            """, (start_date.isoformat(),))
            daily_activity = dict(cursor.fetchall())
        
        return {
            'period_days': days,
            'total_entries': total_entries,
            'by_action': by_action,
            'by_user': by_user,
            'success_rate': success_stats.get(True, 0) / total_entries * 100 if total_entries > 0 else 0,
            'daily_activity': daily_activity
        }
    
    def export_audit_trail(self, start_date: Optional[datetime] = None,
                          end_date: Optional[datetime] = None,
                          format: str = "json") -> str:
        """Exporte l'audit trail"""
        entries = self.search_audit_trail(
            start_date=start_date,
            end_date=end_date,
            limit=10000
        )
        
        # Log de l'export
        self.log_audit(
            action=AuditAction.DATA_EXPORT,
            severity=AuditSeverity.HIGH,
            additional_data={
                'export_format': format,
                'entries_count': len(entries),
                'start_date': start_date.isoformat() if start_date else None,
                'end_date': end_date.isoformat() if end_date else None
            }
        )
        
        if format == "json":
            return json.dumps([asdict(entry) for entry in entries], default=str, indent=2)
        elif format == "csv":
            import csv
            import io
            
            output = io.StringIO()
            if entries:
                writer = csv.DictWriter(output, fieldnames=asdict(entries[0]).keys())
                writer.writeheader()
                for entry in entries:
                    writer.writerow(asdict(entry))
            
            return output.getvalue()
        else:
            raise ValueError(f"Format non supporté: {format}")
    
    def set_current_user(self, user_id: str):
        """Définit l'utilisateur actuel pour les audits"""
        old_user = self.current_user
        self.current_user = user_id
        
        if old_user != user_id:
            self.log_audit(
                action=AuditAction.USER_LOGIN,
                severity=AuditSeverity.MEDIUM,
                user_id=user_id,
                additional_data={'previous_user': old_user}
            )
    
    def clear_current_user(self):
        """Efface l'utilisateur actuel"""
        if self.current_user:
            self.log_audit(
                action=AuditAction.USER_LOGOUT,
                severity=AuditSeverity.MEDIUM,
                user_id=self.current_user
            )
            self.current_user = None
    
    def audit_decorator(self, action: AuditAction, severity: AuditSeverity = AuditSeverity.MEDIUM,
                       resource_type: Optional[str] = None):
        """Décorateur pour auditer automatiquement les fonctions"""
        def decorator(func):
            def wrapper(*args, **kwargs):
                start_time = time.time()
                success = True
                error_message = None
                result = None
                
                try:
                    result = func(*args, **kwargs)
                    return result
                except Exception as e:
                    success = False
                    error_message = str(e)
                    raise
                finally:
                    execution_time = time.time() - start_time
                    
                    # Extraire l'ID de ressource si possible
                    resource_id = None
                    if args and hasattr(args[0], 'id'):
                        resource_id = str(args[0].id)
                    elif 'id' in kwargs:
                        resource_id = str(kwargs['id'])
                    
                    self.log_audit(
                        action=action,
                        severity=severity,
                        resource_type=resource_type,
                        resource_id=resource_id,
                        success=success,
                        error_message=error_message,
                        execution_time=execution_time,
                        additional_data={
                            'function': func.__name__,
                            'module': func.__module__
                        }
                    )
            
            return wrapper
        return decorator

# Instance globale
audit_trail = AuditTrail()
