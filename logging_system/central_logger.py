"""
🎯 Logger centralisé intégrant tous les composants
Point d'entrée unique pour logging, audit et analytics
"""

import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union, Callable
from pathlib import Path
import sys
import json
from contextlib import contextmanager

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from logging_system.advanced_logger import advanced_logger, LogLevel, LogCategory
from logging_system.audit_trail import audit_trail, AuditAction, AuditSeverity
from logging_system.performance_analytics import performance_analytics

class CentralLogger:
    """Logger centralisé avec audit et analytics intégrés"""
    
    def __init__(self):
        self.session_id = advanced_logger.session_id
        
        # Configuration des hooks
        self.audit_hooks = {}  # action -> audit_config
        self.metric_hooks = {}  # metric_name -> config
        
        # Statistiques de logging
        self.log_stats = {
            'total_logs': 0,
            'logs_by_level': {},
            'logs_by_category': {},
            'audit_entries': 0,
            'metrics_recorded': 0
        }
        
        # Thread safety
        self.lock = threading.Lock()
        
        # Démarrer le monitoring automatique
        self._setup_automatic_monitoring()
        
        print(f"🎯 Logger centralisé initialisé (Session: {self.session_id})")
    
    def _setup_automatic_monitoring(self):
        """Configure le monitoring automatique"""
        # Démarrer le monitoring système
        performance_analytics.start_system_monitoring(interval=60)
        
        # Enregistrer des callbacks de métriques
        performance_analytics.register_metric_callback(
            "logging_stats", 
            lambda: self.log_stats['total_logs']
        )
        
        # Audit du démarrage
        audit_trail.log_audit(
            action=AuditAction.SYSTEM_START,
            severity=AuditSeverity.MEDIUM,
            additional_data={
                'component': 'central_logger',
                'session_id': self.session_id
            }
        )
    
    def log(self, level: Union[LogLevel, str], message: str, 
           category: Union[LogCategory, str] = LogCategory.SYSTEM,
           audit_action: Optional[AuditAction] = None,
           audit_severity: Optional[AuditSeverity] = None,
           metric_name: Optional[str] = None,
           metric_value: Optional[float] = None,
           execution_time: Optional[float] = None,
           **kwargs):
        """
        Log centralisé avec audit et métriques optionnels
        
        Args:
            level: Niveau de log
            message: Message à logger
            category: Catégorie du log
            audit_action: Action d'audit (optionnel)
            audit_severity: Sévérité d'audit (optionnel)
            metric_name: Nom de métrique (optionnel)
            metric_value: Valeur de métrique (optionnel)
            execution_time: Temps d'exécution (optionnel)
            **kwargs: Données supplémentaires
        """
        with self.lock:
            # Convertir les enums si nécessaire
            if isinstance(level, str):
                level = LogLevel[level.upper()]
            if isinstance(category, str):
                category = LogCategory[category.upper()]
            
            # Logger principal
            advanced_logger.log(level, message, category, **kwargs)
            
            # Audit si demandé
            if audit_action:
                audit_severity = audit_severity or AuditSeverity.MEDIUM
                audit_trail.log_audit(
                    action=audit_action,
                    severity=audit_severity,
                    additional_data={
                        'log_message': message,
                        'log_level': level.name,
                        'log_category': category.value,
                        **kwargs
                    },
                    execution_time=execution_time
                )
                self.log_stats['audit_entries'] += 1
            
            # Métrique si demandée
            if metric_name and metric_value is not None:
                performance_analytics.record_metric(
                    name=metric_name,
                    value=metric_value,
                    category=category.value.lower()
                )
                self.log_stats['metrics_recorded'] += 1
            
            # Métrique de temps d'exécution
            if execution_time:
                performance_analytics.record_metric(
                    name=f"execution_time_{category.value.lower()}",
                    value=execution_time,
                    unit="seconds",
                    category="performance"
                )
            
            # Mettre à jour les statistiques
            self.log_stats['total_logs'] += 1
            self.log_stats['logs_by_level'][level.name] = \
                self.log_stats['logs_by_level'].get(level.name, 0) + 1
            self.log_stats['logs_by_category'][category.value] = \
                self.log_stats['logs_by_category'].get(category.value, 0) + 1
    
    # Méthodes de convenance avec audit automatique
    def trade_executed(self, message: str, symbol: str, side: str, quantity: float,
                      price: float, trade_id: str = None, **kwargs):
        """Log d'exécution de trade avec audit automatique"""
        self.log(
            level=LogLevel.INFO,
            message=message,
            category=LogCategory.TRADING,
            audit_action=AuditAction.TRADE_EXECUTED,
            audit_severity=AuditSeverity.HIGH,
            metric_name="trade_executed",
            metric_value=quantity * price,
            symbol=symbol,
            side=side,
            quantity=quantity,
            price=price,
            trade_id=trade_id,
            **kwargs
        )
    
    def position_opened(self, message: str, symbol: str, position_type: str,
                       quantity: float, entry_price: float, **kwargs):
        """Log d'ouverture de position avec audit"""
        self.log(
            level=LogLevel.INFO,
            message=message,
            category=LogCategory.TRADING,
            audit_action=AuditAction.POSITION_OPENED,
            audit_severity=AuditSeverity.MEDIUM,
            metric_name="position_value",
            metric_value=quantity * entry_price,
            symbol=symbol,
            position_type=position_type,
            quantity=quantity,
            entry_price=entry_price,
            **kwargs
        )
    
    def position_closed(self, message: str, symbol: str, quantity: float,
                       exit_price: float, pnl: float, **kwargs):
        """Log de fermeture de position avec audit"""
        self.log(
            level=LogLevel.INFO,
            message=message,
            category=LogCategory.TRADING,
            audit_action=AuditAction.POSITION_CLOSED,
            audit_severity=AuditSeverity.MEDIUM,
            metric_name="realized_pnl",
            metric_value=pnl,
            symbol=symbol,
            quantity=quantity,
            exit_price=exit_price,
            pnl=pnl,
            **kwargs
        )
    
    def strategy_action(self, message: str, strategy_name: str, action: str,
                       symbol: str = None, **kwargs):
        """Log d'action de stratégie"""
        audit_action = AuditAction.STRATEGY_STARTED if action == "start" else AuditAction.STRATEGY_STOPPED
        
        self.log(
            level=LogLevel.INFO,
            message=message,
            category=LogCategory.STRATEGY,
            audit_action=audit_action,
            audit_severity=AuditSeverity.MEDIUM,
            strategy_name=strategy_name,
            action=action,
            symbol=symbol,
            **kwargs
        )
    
    def config_changed(self, message: str, config_key: str, old_value: Any,
                      new_value: Any, **kwargs):
        """Log de changement de configuration avec audit"""
        self.log(
            level=LogLevel.WARNING,
            message=message,
            category=LogCategory.SYSTEM,
            audit_action=AuditAction.CONFIG_CHANGED,
            audit_severity=AuditSeverity.HIGH,
            config_key=config_key,
            old_value=str(old_value),
            new_value=str(new_value),
            **kwargs
        )
    
    def emergency_stop(self, message: str, reason: str, **kwargs):
        """Log d'arrêt d'urgence avec audit critique"""
        self.log(
            level=LogLevel.CRITICAL,
            message=message,
            category=LogCategory.TRADING,
            audit_action=AuditAction.EMERGENCY_STOP,
            audit_severity=AuditSeverity.CRITICAL,
            reason=reason,
            **kwargs
        )
    
    def risk_alert(self, message: str, risk_type: str, risk_level: str,
                  symbol: str = None, **kwargs):
        """Log d'alerte de risque"""
        self.log(
            level=LogLevel.WARNING,
            message=message,
            category=LogCategory.RISK,
            metric_name=f"risk_alert_{risk_type}",
            metric_value=1,
            risk_type=risk_type,
            risk_level=risk_level,
            symbol=symbol,
            **kwargs
        )
    
    def performance_metric(self, metric_name: str, value: float, unit: str = "",
                          message: str = None, **kwargs):
        """Enregistre une métrique de performance avec log"""
        message = message or f"Métrique {metric_name}: {value} {unit}"
        
        self.log(
            level=LogLevel.INFO,
            message=message,
            category=LogCategory.PERFORMANCE,
            metric_name=metric_name,
            metric_value=value,
            unit=unit,
            **kwargs
        )
    
    @contextmanager
    def timed_operation(self, operation_name: str, category: LogCategory = LogCategory.PERFORMANCE,
                       audit_action: Optional[AuditAction] = None,
                       log_start: bool = True, log_end: bool = True):
        """Context manager pour mesurer et logger le temps d'opération"""
        start_time = time.perf_counter()
        
        if log_start:
            self.log(
                level=LogLevel.DEBUG,
                message=f"Début opération: {operation_name}",
                category=category,
                operation=operation_name,
                phase="start"
            )
        
        try:
            yield
            success = True
            error_message = None
        except Exception as e:
            success = False
            error_message = str(e)
            raise
        finally:
            end_time = time.perf_counter()
            execution_time = end_time - start_time
            
            if log_end:
                level = LogLevel.INFO if success else LogLevel.ERROR
                message = f"Fin opération: {operation_name} ({execution_time:.3f}s)"
                if not success:
                    message += f" - ERREUR: {error_message}"
                
                self.log(
                    level=level,
                    message=message,
                    category=category,
                    audit_action=audit_action,
                    metric_name=f"operation_time_{operation_name}",
                    metric_value=execution_time,
                    execution_time=execution_time,
                    operation=operation_name,
                    phase="end",
                    success=success,
                    error_message=error_message
                )
    
    def batch_log(self, entries: List[Dict[str, Any]]):
        """Log en lot pour optimiser les performances"""
        for entry in entries:
            self.log(**entry)
    
    def search_logs(self, query: str, category: Optional[LogCategory] = None,
                   start_date: Optional[datetime] = None,
                   end_date: Optional[datetime] = None,
                   max_results: int = 100) -> List[Dict[str, Any]]:
        """Recherche dans tous les logs"""
        return advanced_logger.search_logs(
            query=query,
            category=category,
            start_date=start_date,
            end_date=end_date,
            max_results=max_results
        )
    
    def search_audit(self, action: Optional[AuditAction] = None,
                    user_id: Optional[str] = None,
                    start_date: Optional[datetime] = None,
                    end_date: Optional[datetime] = None,
                    limit: int = 100) -> List[Dict[str, Any]]:
        """Recherche dans l'audit trail"""
        entries = audit_trail.search_audit_trail(
            action=action,
            user_id=user_id,
            start_date=start_date,
            end_date=end_date,
            limit=limit
        )
        
        return [
            {
                'id': entry.id,
                'timestamp': entry.timestamp.isoformat(),
                'action': entry.action.value,
                'severity': entry.severity.value,
                'user_id': entry.user_id,
                'success': entry.success,
                'additional_data': entry.additional_data
            }
            for entry in entries
        ]
    
    def get_comprehensive_report(self, hours: int = 24) -> Dict[str, Any]:
        """Génère un rapport complet logs + audit + métriques"""
        # Rapport de performance
        perf_report = performance_analytics.get_performance_report(hours)
        
        # Statistiques d'audit
        audit_stats = audit_trail.get_audit_statistics(hours // 24 or 1)
        
        # Statistiques de logs
        log_stats = advanced_logger.get_log_stats()
        
        return {
            'report_timestamp': datetime.now().isoformat(),
            'period_hours': hours,
            'session_id': self.session_id,
            'logging': {
                'stats': self.log_stats,
                'system_stats': log_stats
            },
            'audit': audit_stats,
            'performance': perf_report,
            'health_score': performance_analytics.get_system_health_score()
        }
    
    def export_all_data(self, start_date: Optional[datetime] = None,
                       end_date: Optional[datetime] = None) -> Dict[str, str]:
        """Exporte toutes les données (logs, audit, métriques)"""
        exports = {}
        
        # Export audit
        exports['audit'] = audit_trail.export_audit_trail(
            start_date=start_date,
            end_date=end_date,
            format="json"
        )
        
        # Export métriques
        hours = 24
        if start_date and end_date:
            hours = int((end_date - start_date).total_seconds() / 3600)
        
        exports['metrics'] = performance_analytics.export_metrics(
            hours=hours,
            format="json"
        )
        
        # Log de l'export
        self.log(
            level=LogLevel.INFO,
            message="Export complet des données effectué",
            category=LogCategory.AUDIT,
            audit_action=AuditAction.DATA_EXPORT,
            audit_severity=AuditSeverity.HIGH,
            start_date=start_date.isoformat() if start_date else None,
            end_date=end_date.isoformat() if end_date else None
        )
        
        return exports
    
    def cleanup_old_data(self, days: int = 30):
        """Nettoie les anciennes données"""
        # Archiver les logs
        advanced_logger.archive_old_logs(days)
        
        # Log du nettoyage
        self.log(
            level=LogLevel.INFO,
            message=f"Nettoyage des données anciennes ({days} jours)",
            category=LogCategory.SYSTEM,
            days=days
        )
    
    def shutdown(self):
        """Arrêt propre du système de logging"""
        # Arrêter le monitoring
        performance_analytics.stop_system_monitoring()
        
        # Audit de l'arrêt
        audit_trail.log_audit(
            action=AuditAction.SYSTEM_STOP,
            severity=AuditSeverity.MEDIUM,
            additional_data={
                'component': 'central_logger',
                'session_id': self.session_id,
                'total_logs': self.log_stats['total_logs']
            }
        )
        
        # Log final
        self.log(
            level=LogLevel.INFO,
            message="Arrêt du système de logging centralisé",
            category=LogCategory.SYSTEM
        )

# Instance globale
central_logger = CentralLogger()
