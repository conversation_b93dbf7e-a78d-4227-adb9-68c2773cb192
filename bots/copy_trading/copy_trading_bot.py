"""
👥 Bot de copy trading principal
Bot automatisé pour copier les trades de wallets performants avec gestion des risques
"""

import asyncio
import json
import time
from decimal import Decimal
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import logging
from pathlib import Path
import sys

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent.parent))

from bots.copy_trading.wallet_analyzer import WalletAnalyzer, WalletPerformance
from bots.copy_trading.signal_detector import SignalDetector, SignalConfig
from bots.copy_trading.copy_executor import CopyExecutor, CopyTradeConfig
from utils.error_handler import error_handler, ErrorCategory, ErrorSeverity
from logging_system.central_logger import central_logger, LogCategory
from risk_management.portfolio_manager import portfolio_manager
from utils.notifications import notifier

@dataclass
class CopyTradingBotConfig:
    """Configuration complète du bot de copy trading"""
    # Wallets à suivre
    tracked_wallets: List[Dict[str, Any]] = None
    
    # Configuration de détection
    min_signal_confidence: float = 0.75
    required_urgency_levels: List[str] = None
    
    # Configuration d'exécution
    default_copy_percentage: float = 1.5  # 1.5% du portefeuille
    max_copy_percentage: float = 3.0      # 3% maximum
    max_concurrent_copies: int = 8
    
    # Gestion des risques
    enable_stop_loss: bool = True
    default_stop_loss_percentage: float = 8.0
    enable_take_profit: bool = True
    default_take_profit_percentage: float = 15.0
    
    # Limites quotidiennes
    max_daily_copy_amount: float = 25000.0
    max_copies_per_wallet: int = 2
    
    # Monitoring
    analysis_interval_minutes: int = 60
    enable_notifications: bool = True
    notification_min_profit: float = 100.0

class CopyTradingBot:
    """Bot de copy trading principal"""
    
    def __init__(self, config: CopyTradingBotConfig = None):
        self.logger = logging.getLogger(__name__)
        self.config = config or CopyTradingBotConfig()
        
        # Initialiser les valeurs par défaut
        if self.config.tracked_wallets is None:
            self.config.tracked_wallets = []
        if self.config.required_urgency_levels is None:
            self.config.required_urgency_levels = ['high', 'medium']
        
        # État du bot
        self.is_running = False
        self.start_time = None
        self.last_analysis_time = 0
        
        # Composants principaux
        self.wallet_analyzer = WalletAnalyzer()
        
        # Configuration des sous-composants
        signal_config = SignalConfig(
            min_wallet_performance_score=0.7,
            min_trade_size_usd=500.0,
            max_trade_size_usd=50000.0
        )
        
        copy_config = CopyTradeConfig(
            default_copy_percentage=self.config.default_copy_percentage,
            max_copy_percentage=self.config.max_copy_percentage,
            max_concurrent_copies=self.config.max_concurrent_copies,
            min_signal_confidence=self.config.min_signal_confidence,
            required_urgency_levels=self.config.required_urgency_levels,
            enable_stop_loss=self.config.enable_stop_loss,
            default_stop_loss_percentage=self.config.default_stop_loss_percentage,
            enable_take_profit=self.config.enable_take_profit,
            default_take_profit_percentage=self.config.default_take_profit_percentage,
            max_daily_copy_amount=self.config.max_daily_copy_amount,
            max_copies_per_wallet=self.config.max_copies_per_wallet
        )
        
        self.signal_detector = SignalDetector(self.wallet_analyzer, signal_config)
        self.copy_executor = CopyExecutor(self.signal_detector, copy_config)
        
        # Métriques de performance
        self.daily_copies = 0
        self.daily_profit = 0.0
        self.last_reset_date = datetime.now().date()
        
        # Gestion des erreurs
        self.consecutive_errors = 0
        self.max_consecutive_errors = 5
        
        central_logger.log(
            level="INFO",
            message="Bot de copy trading initialisé",
            category=LogCategory.STRATEGY,
            tracked_wallets=len(self.config.tracked_wallets),
            copy_percentage=self.config.default_copy_percentage
        )
    
    async def start(self):
        """Démarre le bot de copy trading"""
        if self.is_running:
            self.logger.warning("Le bot est déjà en cours d'exécution")
            return
        
        try:
            self.is_running = True
            self.start_time = datetime.now()
            
            central_logger.strategy_action(
                message="Démarrage du bot de copy trading",
                strategy_name="Copy_Trading",
                action="start"
            )
            
            # Notification de démarrage
            if self.config.enable_notifications:
                await notifier.send_telegram(
                    f"👥 <b>Bot Copy Trading démarré</b>\n"
                    f"📊 Wallets suivis: {len(self.config.tracked_wallets)}\n"
                    f"💰 Copy %: {self.config.default_copy_percentage}%\n"
                    f"🛡️ Stop Loss: {self.config.default_stop_loss_percentage}%"
                )
            
            # Initialiser les wallets à suivre
            await self._initialize_tracked_wallets()
            
            # Démarrer les tâches de monitoring
            tasks = [
                asyncio.create_task(self._main_loop()),
                asyncio.create_task(self.signal_detector.monitor_wallets()),
                asyncio.create_task(self.copy_executor.start_monitoring())
            ]
            
            # Attendre que toutes les tâches se terminent
            await asyncio.gather(*tasks)
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.CRITICAL, {
                'function': 'start'
            })
            await self.stop()
    
    async def stop(self):
        """Arrête le bot de copy trading"""
        if not self.is_running:
            return
        
        self.is_running = False
        
        try:
            # Fermer toutes les positions ouvertes
            active_trades = self.copy_executor.get_active_copy_trades()
            for trade in active_trades:
                if trade.status.value == "COMPLETED":  # Position ouverte
                    await self.copy_executor._exit_position(trade, "bot_stop")
            
            # Générer le rapport final
            final_report = self._generate_performance_report()
            
            central_logger.strategy_action(
                message="Arrêt du bot de copy trading",
                strategy_name="Copy_Trading",
                action="stop",
                **final_report
            )
            
            # Notification d'arrêt
            if self.config.enable_notifications:
                await notifier.send_telegram(
                    f"🛑 <b>Bot Copy Trading arrêté</b>\n"
                    f"📊 Copies: {final_report.get('total_copies', 0)}\n"
                    f"💰 P&L: ${final_report.get('total_pnl', 0):.2f}\n"
                    f"📈 Taux de réussite: {final_report.get('success_rate', 0):.1f}%"
                )
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.MEDIUM, {
                'function': 'stop'
            })
    
    async def _initialize_tracked_wallets(self):
        """Initialise les wallets à suivre"""
        try:
            for wallet_config in self.config.tracked_wallets:
                wallet_address = wallet_config.get('address')
                label = wallet_config.get('label', '')
                min_trade_size = wallet_config.get('min_trade_size', 1000.0)
                
                if wallet_address:
                    success = self.wallet_analyzer.add_wallet_to_track(
                        wallet_address, label, min_trade_size
                    )
                    
                    if success:
                        # Analyser immédiatement le wallet
                        await self.wallet_analyzer.analyze_wallet_transactions(wallet_address)
            
            central_logger.log(
                level="INFO",
                message=f"{len(self.config.tracked_wallets)} wallets initialisés",
                category=LogCategory.SYSTEM
            )
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.MEDIUM, {
                'function': '_initialize_tracked_wallets'
            })
    
    async def _main_loop(self):
        """Boucle principale du bot"""
        while self.is_running:
            try:
                # Réinitialiser les compteurs quotidiens
                self._reset_daily_counters()
                
                # Vérifier les limites de sécurité
                if not self._check_safety_limits():
                    await self._emergency_stop("Limites de sécurité dépassées")
                    break
                
                # Analyse périodique des wallets
                current_time = time.time()
                if current_time - self.last_analysis_time > (self.config.analysis_interval_minutes * 60):
                    await self._periodic_wallet_analysis()
                    self.last_analysis_time = current_time
                
                # Monitoring des performances
                await self._monitor_performance()
                
                # Attendre avant le prochain cycle
                await asyncio.sleep(30)
                
            except Exception as e:
                self.consecutive_errors += 1
                error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.HIGH, {
                    'function': '_main_loop',
                    'consecutive_errors': self.consecutive_errors
                })
                
                # Arrêt d'urgence si trop d'erreurs consécutives
                if self.consecutive_errors >= self.max_consecutive_errors:
                    await self._emergency_stop("Trop d'erreurs consécutives")
                    break
                
                await asyncio.sleep(60)  # Pause plus longue en cas d'erreur
            else:
                self.consecutive_errors = 0  # Reset si pas d'erreur
    
    def _reset_daily_counters(self):
        """Réinitialise les compteurs quotidiens"""
        today = datetime.now().date()
        if today != self.last_reset_date:
            self.daily_copies = 0
            self.daily_profit = 0.0
            self.last_reset_date = today
            
            central_logger.log(
                level="INFO",
                message="Compteurs quotidiens réinitialisés",
                category=LogCategory.SYSTEM,
                date=today.isoformat()
            )
    
    def _check_safety_limits(self) -> bool:
        """Vérifie les limites de sécurité"""
        # Vérifier les pertes quotidiennes
        if self.daily_profit < -1000:  # Plus de $1000 de perte
            central_logger.log(
                level="WARNING",
                message="Limite quotidienne de perte atteinte",
                category=LogCategory.RISK,
                daily_profit=self.daily_profit
            )
            return False
        
        # Vérifier le nombre de copies quotidiennes
        if self.daily_copies > 50:  # Plus de 50 copies par jour
            central_logger.log(
                level="WARNING",
                message="Limite quotidienne de copies atteinte",
                category=LogCategory.RISK,
                daily_copies=self.daily_copies
            )
            return False
        
        return True
    
    async def _periodic_wallet_analysis(self):
        """Analyse périodique des wallets"""
        try:
            tracked_wallets = self.wallet_analyzer.get_tracked_wallets()
            
            for wallet_address in tracked_wallets.keys():
                # Re-analyser les performances
                await self.wallet_analyzer.analyze_wallet_transactions(wallet_address)
            
            # Identifier les top performers
            top_performers = self.wallet_analyzer.get_top_performers(limit=5)
            
            if top_performers:
                central_logger.log(
                    level="INFO",
                    message=f"Top performers identifiés: {len(top_performers)} wallets",
                    category=LogCategory.STRATEGY,
                    best_win_rate=max(p.win_rate for p in top_performers),
                    best_pnl=max(p.total_pnl for p in top_performers)
                )
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.MEDIUM, {
                'function': '_periodic_wallet_analysis'
            })
    
    async def _monitor_performance(self):
        """Surveille les performances"""
        try:
            # Mettre à jour les métriques quotidiennes
            executor_stats = self.copy_executor.get_performance_statistics()
            self.daily_copies = executor_stats.get('total_copies_executed', 0)
            self.daily_profit = executor_stats.get('total_realized_pnl', 0)
            
            # Vérifier les positions à risque
            active_trades = self.copy_executor.get_active_copy_trades()
            risky_positions = [
                trade for trade in active_trades
                if trade.unrealized_pnl and trade.unrealized_pnl < -500  # Perte > $500
            ]
            
            if risky_positions and self.config.enable_notifications:
                await notifier.send_telegram(
                    f"⚠️ <b>Positions à risque détectées</b>\n"
                    f"📊 Nombre: {len(risky_positions)}\n"
                    f"💸 Perte max: ${min(t.unrealized_pnl for t in risky_positions):.2f}"
                )
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.LOW, {
                'function': '_monitor_performance'
            })
    
    async def _emergency_stop(self, reason: str):
        """Arrêt d'urgence du bot"""
        central_logger.emergency_stop(
            message=f"Arrêt d'urgence du bot de copy trading: {reason}",
            reason=reason,
            daily_copies=self.daily_copies,
            daily_profit=self.daily_profit
        )
        
        if self.config.enable_notifications:
            await notifier.send_telegram(
                f"🚨 <b>ARRÊT D'URGENCE</b>\n"
                f"🤖 Bot: Copy Trading\n"
                f"⚠️ Raison: {reason}\n"
                f"📊 Copies: {self.daily_copies}\n"
                f"💰 P&L: ${self.daily_profit:.2f}"
            )
        
        await self.stop()
    
    def _generate_performance_report(self) -> Dict[str, Any]:
        """Génère un rapport de performance"""
        try:
            # Statistiques de l'analyseur
            analyzer_stats = self.wallet_analyzer.get_analyzer_statistics()
            
            # Statistiques du détecteur
            detector_stats = self.signal_detector.get_signal_statistics()
            
            # Statistiques de l'exécuteur
            executor_stats = self.copy_executor.get_performance_statistics()
            
            # Calcul de l'uptime
            uptime_hours = 0
            if self.start_time:
                uptime = datetime.now() - self.start_time
                uptime_hours = uptime.total_seconds() / 3600
            
            return {
                'uptime_hours': uptime_hours,
                'tracked_wallets': analyzer_stats.get('total_wallets_tracked', 0),
                'active_wallets': analyzer_stats.get('active_wallets', 0),
                'total_signals': detector_stats.get('total_signals_detected', 0),
                'total_copies': executor_stats.get('total_copies_executed', 0),
                'successful_copies': executor_stats.get('successful_copies', 0),
                'success_rate': executor_stats.get('success_rate', 0),
                'total_volume': executor_stats.get('total_copy_volume', 0),
                'total_pnl': executor_stats.get('total_realized_pnl', 0),
                'average_pnl_per_trade': executor_stats.get('average_pnl_per_trade', 0),
                'active_positions': executor_stats.get('active_positions', 0),
                'daily_copies': self.daily_copies,
                'daily_profit': self.daily_profit,
                'consecutive_errors': self.consecutive_errors,
                'is_running': self.is_running
            }
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.LOW, {
                'function': '_generate_performance_report'
            })
            return {}
    
    def get_status(self) -> Dict[str, Any]:
        """Retourne le statut actuel du bot"""
        return {
            'is_running': self.is_running,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'tracked_wallets_count': len(self.config.tracked_wallets),
            'daily_copies': self.daily_copies,
            'daily_profit': self.daily_profit,
            'consecutive_errors': self.consecutive_errors,
            'active_positions': len(self.copy_executor.get_active_copy_trades()) if hasattr(self, 'copy_executor') else 0,
            'last_analysis_time': self.last_analysis_time,
            'config': {
                'copy_percentage': self.config.default_copy_percentage,
                'max_concurrent': self.config.max_concurrent_copies,
                'stop_loss_enabled': self.config.enable_stop_loss,
                'take_profit_enabled': self.config.enable_take_profit
            }
        }
    
    async def add_wallet_to_follow(self, wallet_address: str, label: str = "",
                                 min_trade_size: float = 1000.0) -> bool:
        """Ajoute un wallet à suivre"""
        try:
            # Ajouter à l'analyseur
            success = self.wallet_analyzer.add_wallet_to_track(
                wallet_address, label, min_trade_size
            )
            
            if success:
                # Ajouter à la configuration
                wallet_config = {
                    'address': wallet_address,
                    'label': label,
                    'min_trade_size': min_trade_size
                }
                self.config.tracked_wallets.append(wallet_config)
                
                # Analyser immédiatement
                await self.wallet_analyzer.analyze_wallet_transactions(wallet_address)
                
                central_logger.log(
                    level="INFO",
                    message=f"Nouveau wallet ajouté: {label or wallet_address}",
                    category=LogCategory.STRATEGY,
                    wallet_address=wallet_address
                )
                
                return True
            
            return False
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.MEDIUM, {
                'function': 'add_wallet_to_follow',
                'wallet_address': wallet_address
            })
            return False
    
    def remove_wallet_from_following(self, wallet_address: str) -> bool:
        """Retire un wallet du suivi"""
        try:
            # Retirer de l'analyseur
            success = self.wallet_analyzer.remove_wallet_from_tracking(wallet_address)
            
            if success:
                # Retirer de la configuration
                self.config.tracked_wallets = [
                    w for w in self.config.tracked_wallets
                    if w.get('address', '').lower() != wallet_address.lower()
                ]
                
                return True
            
            return False
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.MEDIUM, {
                'function': 'remove_wallet_from_following',
                'wallet_address': wallet_address
            })
            return False
    
    def get_tracked_wallets_performance(self) -> List[Dict[str, Any]]:
        """Retourne les performances des wallets suivis"""
        try:
            performances = []
            
            for wallet_config in self.config.tracked_wallets:
                wallet_address = wallet_config.get('address')
                if wallet_address:
                    performance = self.wallet_analyzer.get_wallet_performance(wallet_address)
                    if performance:
                        perf_dict = asdict(performance)
                        perf_dict['label'] = wallet_config.get('label', '')
                        performances.append(perf_dict)
            
            # Trier par performance
            performances.sort(key=lambda x: x.get('total_pnl', 0), reverse=True)
            
            return performances
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.LOW, {
                'function': 'get_tracked_wallets_performance'
            })
            return []

# Fonction utilitaire pour créer et lancer le bot
async def create_and_run_bot(config_dict: Dict[str, Any] = None):
    """Crée et lance le bot avec la configuration donnée"""
    try:
        # Créer la configuration
        config = CopyTradingBotConfig(**config_dict) if config_dict else CopyTradingBotConfig()
        
        # Créer le bot
        bot = CopyTradingBot(config)
        
        # Démarrer
        await bot.start()
        
    except KeyboardInterrupt:
        central_logger.log(
            level="INFO",
            message="Arrêt du bot demandé par l'utilisateur",
            category=LogCategory.SYSTEM
        )
        if 'bot' in locals():
            await bot.stop()
    except Exception as e:
        error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.CRITICAL, {
            'function': 'create_and_run_bot'
        })
        if 'bot' in locals():
            await bot.stop()

if __name__ == "__main__":
    # Configuration d'exemple
    config = {
        'tracked_wallets': [
            {
                'address': '******************************************',
                'label': 'Whale Trader #1',
                'min_trade_size': 5000.0
            },
            {
                'address': '******************************************',
                'label': 'DeFi Expert',
                'min_trade_size': 2000.0
            }
        ],
        'default_copy_percentage': 2.0,
        'max_concurrent_copies': 5,
        'enable_stop_loss': True,
        'default_stop_loss_percentage': 10.0,
        'enable_notifications': True
    }
    
    asyncio.run(create_and_run_bot(config))
