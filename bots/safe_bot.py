#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🤖 Safe Bot - Bot de Trading Sécurisé
Supporte les modes testnet et production
"""

import os
import sys
import time
import hmac
import hashlib
import requests
import logging
import argparse
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv

class SafeBot:
    def __init__(self, mode="testnet"):
        self.mode = mode
        self.project_root = Path(__file__).parent.parent
        self.load_environment()
        self.setup_configuration()
        self.setup_logging()
        
    def load_environment(self):
        """Charge les variables d'environnement"""
        env_file = self.project_root / ".env.local"
        if env_file.exists():
            load_dotenv(env_file)
            print("✅ Configuration chargée")
        else:
            print("❌ Fichier .env.local manquant")
            sys.exit(1)
    
    def setup_configuration(self):
        """Configure les paramètres selon le mode"""
        if self.mode == "testnet":
            self.api_key = os.getenv("safe_bot_TEST_API_KEY")
            self.api_secret = os.getenv("safe_bot_TEST_API_SECRET")
            self.base_url = os.getenv("TEST_BASE_URL", "https://testnet.binance.vision")
            self.capital_max = 100  # USDT fictifs
            self.log_file = "safe_bot_testnet.log"
        else:  # production
            self.api_key = os.getenv("safe_bot_PROD_API_KEY")
            self.api_secret = os.getenv("safe_bot_PROD_API_SECRET")
            self.base_url = os.getenv("BASE_URL", "https://api.binance.com")
            self.capital_max = 10  # USDT réels - sécurisé
            self.log_file = "safe_bot_production.log"
        
        if not self.api_key or not self.api_secret:
            print(f"❌ Clés API {self.mode} manquantes")
            sys.exit(1)
        
        # Configuration du trading
        self.pair = "BTCUSDT"
        self.grid_size = 5
        self.grid_spacing = 50 if self.mode == "testnet" else 100
        self.order_size = 0.001
        self.stop_loss_percent = 10
        self.take_profit_percent = 15
        
        # Variables de trading
        self.open_orders = {}
        self.stats = {
            "trades_executed": 0,
            "profit_loss": 0.0,
            "start_time": datetime.now(),
            "start_balance": 0.0
        }
    
    def setup_logging(self):
        """Configure le logging"""
        logging.basicConfig(
            filename=self.log_file,
            level=logging.INFO,
            format="%(asctime)s - %(levelname)s - %(message)s"
        )
        self.logger = logging.getLogger(__name__)
    
    def get_server_time(self):
        """Récupère le temps du serveur"""
        try:
            response = requests.get(f"{self.base_url}/api/v3/time")
            return response.json()['serverTime']
        except Exception as e:
            self.logger.error(f"Erreur server time: {e}")
            return int(time.time() * 1000)
    
    def sign_request(self, query_string):
        """Signe une requête"""
        return hmac.new(
            self.api_secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    def get_account_info(self):
        """Récupère les informations du compte"""
        try:
            timestamp = self.get_server_time()
            query_string = f'timestamp={timestamp}'
            signature = self.sign_request(query_string)
            
            url = f"{self.base_url}/api/v3/account?{query_string}&signature={signature}"
            headers = {'X-MBX-APIKEY': self.api_key}
            
            response = requests.get(url, headers=headers)
            if response.status_code == 200:
                return response.json()
            else:
                self.logger.error(f"Erreur account info: {response.status_code}")
                return None
        except Exception as e:
            self.logger.error(f"Exception get_account_info: {e}")
            return None
    
    def get_current_price(self):
        """Récupère le prix actuel"""
        try:
            response = requests.get(f"{self.base_url}/api/v3/ticker/price?symbol={self.pair}")
            if response.status_code == 200:
                return float(response.json()['price'])
            return None
        except Exception as e:
            self.logger.error(f"Erreur prix: {e}")
            return None
    
    def get_balances(self):
        """Récupère les balances BTC et USDT"""
        account_info = self.get_account_info()
        if not account_info:
            return None, None
        
        balances = account_info.get('balances', [])
        btc_balance = 0.0
        usdt_balance = 0.0
        
        for balance in balances:
            if balance['asset'] == 'BTC':
                btc_balance = float(balance['free'])
            elif balance['asset'] == 'USDT':
                usdt_balance = float(balance['free'])
        
        return btc_balance, usdt_balance
    
    def place_order(self, side, quantity, price):
        """Place un ordre"""
        try:
            timestamp = self.get_server_time()
            
            params = {
                'symbol': self.pair,
                'side': side,
                'type': 'LIMIT',
                'timeInForce': 'GTC',
                'quantity': f"{quantity:.8f}",
                'price': f"{price:.2f}",
                'timestamp': timestamp
            }
            
            query_string = '&'.join([f"{k}={v}" for k, v in params.items()])
            signature = self.sign_request(query_string)
            query_string += f"&signature={signature}"
            
            url = f"{self.base_url}/api/v3/order"
            headers = {'X-MBX-APIKEY': self.api_key}
            
            response = requests.post(url, headers=headers, data=query_string)
            
            if response.status_code == 200:
                order_data = response.json()
                message = f"✅ Ordre {side}: {quantity:.8f} {self.pair} à {price:.2f} USDT"
                print(message)
                self.logger.info(message)
                self.stats["trades_executed"] += 1
                return order_data
            else:
                error_msg = f"❌ Erreur ordre {side}: {response.status_code}"
                print(error_msg)
                self.logger.error(error_msg)
                return None
                
        except Exception as e:
            error_msg = f"❌ Exception place_order: {e}"
            print(error_msg)
            self.logger.error(error_msg)
            return None
    
    def print_status(self):
        """Affiche le statut actuel"""
        current_price = self.get_current_price()
        btc_balance, usdt_balance = self.get_balances()
        
        if current_price and btc_balance is not None and usdt_balance is not None:
            total_value = usdt_balance + (btc_balance * current_price)
            profit_loss = total_value - self.stats["start_balance"]
            
            mode_icon = "🧪" if self.mode == "testnet" else "💰"
            print(f"\n📊 SAFE BOT {mode_icon} {self.mode.upper()} - {datetime.now().strftime('%H:%M:%S')}")
            print(f"💰 Prix BTC: {current_price:.2f} USDT")
            print(f"🏦 Balances:")
            print(f"   BTC: {btc_balance:.8f}")
            print(f"   USDT: {usdt_balance:.2f}")
            print(f"   Valeur totale: {total_value:.2f} USDT")
            if self.stats["start_balance"] > 0:
                print(f"📈 P&L: {profit_loss:+.2f} USDT ({profit_loss/self.stats['start_balance']*100:+.2f}%)")
            print(f"🎯 Trades: {self.stats['trades_executed']}")
            print(f"⏱️ Durée: {datetime.now() - self.stats['start_time']}")
    
    def trading_strategy(self):
        """Stratégie de trading simple"""
        current_price = self.get_current_price()
        btc_balance, usdt_balance = self.get_balances()
        
        if not current_price or btc_balance is None or usdt_balance is None:
            return
        
        btc_value = btc_balance * current_price
        
        # Acheter si plus d'USDT que de BTC en valeur
        if usdt_balance > btc_value and usdt_balance >= (self.order_size * current_price * 1.1):
            buy_price = current_price - self.grid_spacing
            self.place_order("BUY", self.order_size, buy_price)
        
        # Vendre si plus de BTC en valeur
        elif btc_value > usdt_balance and btc_balance >= self.order_size:
            sell_price = current_price + self.grid_spacing
            self.place_order("SELL", self.order_size, sell_price)
    
    def run(self):
        """Lance le bot"""
        mode_icon = "🧪" if self.mode == "testnet" else "💰"
        money_type = "fictif" if self.mode == "testnet" else "réel"
        
        print(f"{mode_icon} SAFE BOT - MODE {self.mode.upper()}")
        print("=" * 50)
        print(f"💰 Trading avec argent {money_type}")
        print(f"📈 Paire: {self.pair}")
        print(f"🎯 Capital max: {self.capital_max} USDT")
        print(f"📝 Logs: {self.log_file}")
        print("=" * 50)
        
        # Initialiser les stats
        btc_balance, usdt_balance = self.get_balances()
        if btc_balance is not None and usdt_balance is not None:
            current_price = self.get_current_price()
            if current_price:
                self.stats["start_balance"] = usdt_balance + (btc_balance * current_price)
                print(f"💰 Balance initiale: {self.stats['start_balance']:.2f} USDT")
        
        if self.mode == "production":
            print("\n⚠️ ATTENTION: TRADING AVEC ARGENT RÉEL !")
            confirm = input("Confirmer le démarrage ? (oui/non): ").lower()
            if confirm not in ['oui', 'o', 'yes', 'y']:
                print("❌ Démarrage annulé")
                return
        
        print("\n⏳ Démarrage dans 3 secondes...")
        time.sleep(3)
        
        try:
            iteration = 0
            while True:
                iteration += 1
                
                # Afficher le statut toutes les 10 itérations
                if iteration % 10 == 1:
                    self.print_status()
                
                # Exécuter la stratégie
                self.trading_strategy()
                
                # Attendre 30 secondes
                time.sleep(30)
                
        except KeyboardInterrupt:
            print("\n\n🛑 Arrêt demandé par l'utilisateur")
            self.print_status()
            
            duration = datetime.now() - self.stats["start_time"]
            print(f"\n📊 Session terminée après {duration}")
            print(f"🎯 {self.stats['trades_executed']} trades exécutés")
            print(f"📝 Logs sauvegardés: {self.log_file}")

def main():
    parser = argparse.ArgumentParser(description="Safe Bot - Trading sécurisé")
    parser.add_argument("--mode", choices=["testnet", "production"], default="testnet",
                       help="Mode de trading (testnet ou production)")
    
    args = parser.parse_args()
    
    bot = SafeBot(mode=args.mode)
    bot.run()

if __name__ == "__main__":
    main()
