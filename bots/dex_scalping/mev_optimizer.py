"""
🎯 Optimiseur MEV pour scalping DEX
Gestion avancée des frais et protection contre le MEV
"""

import asyncio
import time
import numpy as np
from decimal import Decimal, ROUND_DOWN
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
from collections import deque
import logging
from pathlib import Path
import sys

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent.parent))

from utils.error_handler import error_handler, ErrorCategory, ErrorSeverity
from logging_system.central_logger import central_logger, LogCategory

@dataclass
class GasStrategy:
    """Stratégie de gestion du gas"""
    base_gas_price: int
    priority_fee: int
    max_fee_per_gas: int
    gas_limit: int
    strategy_type: str  # 'conservative', 'aggressive', 'dynamic'

@dataclass
class MEVProtection:
    """Configuration de protection MEV"""
    use_private_mempool: bool = True
    max_slippage_protection: float = 0.001  # 0.1%
    sandwich_detection: bool = True
    frontrun_protection: bool = True
    deadline_buffer: int = 30  # secondes

@dataclass
class TransactionTiming:
    """Timing optimal pour les transactions"""
    optimal_block: int
    confidence: float
    expected_gas_price: int
    network_congestion: float
    mev_risk_score: float

class MEVOptimizer:
    """Optimiseur MEV et gestion des frais"""
    
    def __init__(self, web3_provider, network: str = "mainnet"):
        self.logger = logging.getLogger(__name__)
        self.network = network
        
        # Historique des prix du gas
        self.gas_price_history: deque = deque(maxlen=1000)
        self.block_history: deque = deque(maxlen=100)
        
        # Métriques MEV
        self.mev_attacks_detected = 0
        self.transactions_protected = 0
        self.gas_saved = 0
        
        # Configuration de protection
        self.mev_protection = MEVProtection()
        
        # Stratégies de gas prédéfinies
        self.gas_strategies = {
            'conservative': GasStrategy(
                base_gas_price=0,
                priority_fee=**********,  # 1 gwei
                max_fee_per_gas=0,
                gas_limit=300000,
                strategy_type='conservative'
            ),
            'aggressive': GasStrategy(
                base_gas_price=0,
                priority_fee=**********,  # 5 gwei
                max_fee_per_gas=0,
                gas_limit=400000,
                strategy_type='aggressive'
            ),
            'dynamic': GasStrategy(
                base_gas_price=0,
                priority_fee=0,
                max_fee_per_gas=0,
                gas_limit=350000,
                strategy_type='dynamic'
            )
        }
        
        central_logger.log(
            level="INFO",
            message="Optimiseur MEV initialisé",
            category=LogCategory.SYSTEM,
            network=network
        )
    
    async def optimize_transaction(self, transaction_data: Dict[str, Any],
                                 urgency: str = "normal") -> Dict[str, Any]:
        """Optimise une transaction pour minimiser les frais et le MEV"""
        try:
            # Analyser les conditions actuelles du réseau
            network_conditions = await self._analyze_network_conditions()
            
            # Détecter les risques MEV
            mev_risk = await self._assess_mev_risk(transaction_data)
            
            # Choisir la stratégie de gas optimale
            gas_strategy = await self._select_gas_strategy(
                network_conditions, mev_risk, urgency
            )
            
            # Calculer le timing optimal
            optimal_timing = await self._calculate_optimal_timing(
                network_conditions, mev_risk
            )
            
            # Appliquer les protections MEV
            protected_tx = await self._apply_mev_protection(
                transaction_data, mev_risk
            )
            
            # Optimiser les paramètres de transaction
            optimized_tx = self._optimize_transaction_params(
                protected_tx, gas_strategy, optimal_timing
            )
            
            central_logger.log(
                level="INFO",
                message="Transaction optimisée pour MEV",
                category=LogCategory.TRADING,
                mev_risk_score=mev_risk['score'],
                gas_strategy=gas_strategy.strategy_type,
                estimated_savings=optimized_tx.get('estimated_savings', 0)
            )
            
            return optimized_tx
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.MEDIUM, {
                'function': 'optimize_transaction'
            })
            return transaction_data
    
    async def _analyze_network_conditions(self) -> Dict[str, Any]:
        """Analyse les conditions actuelles du réseau"""
        try:
            # Simuler l'analyse du réseau (dans un vrai projet, utiliser des APIs)
            current_time = time.time()
            
            # Simuler la congestion du réseau
            network_congestion = np.random.uniform(0.1, 0.9)
            
            # Simuler les prix du gas
            base_fee = np.random.randint(20, 100) * 10**9  # 20-100 gwei
            priority_fee = np.random.randint(1, 10) * 10**9  # 1-10 gwei
            
            # Ajouter à l'historique
            gas_data = {
                'timestamp': current_time,
                'base_fee': base_fee,
                'priority_fee': priority_fee,
                'congestion': network_congestion
            }
            self.gas_price_history.append(gas_data)
            
            # Calculer les tendances
            gas_trend = self._calculate_gas_trend()
            
            return {
                'current_base_fee': base_fee,
                'current_priority_fee': priority_fee,
                'network_congestion': network_congestion,
                'gas_trend': gas_trend,
                'mempool_size': np.random.randint(50000, 200000),
                'block_utilization': network_congestion
            }
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.NETWORK, ErrorSeverity.LOW, {
                'function': '_analyze_network_conditions'
            })
            return {}
    
    def _calculate_gas_trend(self) -> str:
        """Calcule la tendance des prix du gas"""
        if len(self.gas_price_history) < 10:
            return 'stable'
        
        recent_fees = [g['base_fee'] for g in list(self.gas_price_history)[-10:]]
        
        # Calculer la pente
        x = np.arange(len(recent_fees))
        slope = np.polyfit(x, recent_fees, 1)[0]
        
        if slope > 10**9:  # 1 gwei de hausse
            return 'rising'
        elif slope < -10**9:  # 1 gwei de baisse
            return 'falling'
        else:
            return 'stable'
    
    async def _assess_mev_risk(self, transaction_data: Dict[str, Any]) -> Dict[str, Any]:
        """Évalue le risque MEV d'une transaction"""
        try:
            risk_score = 0.0
            risk_factors = []
            
            # Analyser le type de transaction
            if transaction_data.get('function_name') == 'exactInputSingle':
                risk_score += 0.3
                risk_factors.append('dex_swap')
            
            # Analyser la taille de la transaction
            amount_usd = transaction_data.get('amount_usd', 0)
            if amount_usd > 10000:  # Plus de 10k USD
                risk_score += 0.4
                risk_factors.append('large_amount')
            elif amount_usd > 1000:  # Plus de 1k USD
                risk_score += 0.2
                risk_factors.append('medium_amount')
            
            # Analyser la liquidité de la pool
            pool_liquidity = transaction_data.get('pool_liquidity', 0)
            if pool_liquidity > 0 and amount_usd / pool_liquidity > 0.01:  # Plus de 1% de la liquidité
                risk_score += 0.3
                risk_factors.append('high_impact')
            
            # Analyser les conditions de marché
            volatility = transaction_data.get('volatility', 0)
            if volatility > 0.05:  # Plus de 5% de volatilité
                risk_score += 0.2
                risk_factors.append('high_volatility')
            
            # Détecter les patterns de sandwich
            if await self._detect_sandwich_pattern():
                risk_score += 0.5
                risk_factors.append('sandwich_pattern')
            
            # Normaliser le score
            risk_score = min(1.0, risk_score)
            
            return {
                'score': risk_score,
                'level': self._get_risk_level(risk_score),
                'factors': risk_factors,
                'recommendations': self._get_mev_recommendations(risk_score, risk_factors)
            }
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.LOW, {
                'function': '_assess_mev_risk'
            })
            return {'score': 0.5, 'level': 'medium', 'factors': [], 'recommendations': []}
    
    def _get_risk_level(self, score: float) -> str:
        """Convertit le score de risque en niveau"""
        if score < 0.3:
            return 'low'
        elif score < 0.7:
            return 'medium'
        else:
            return 'high'
    
    def _get_mev_recommendations(self, score: float, factors: List[str]) -> List[str]:
        """Génère des recommandations basées sur le risque MEV"""
        recommendations = []
        
        if score > 0.7:
            recommendations.append('use_private_mempool')
            recommendations.append('increase_slippage_protection')
        
        if 'large_amount' in factors:
            recommendations.append('split_transaction')
        
        if 'sandwich_pattern' in factors:
            recommendations.append('delay_execution')
            recommendations.append('use_commit_reveal')
        
        if 'high_volatility' in factors:
            recommendations.append('tighter_deadline')
        
        return recommendations
    
    async def _detect_sandwich_pattern(self) -> bool:
        """Détecte les patterns de sandwich attack"""
        # Simplification: détection basique
        # Dans un vrai projet, analyser le mempool en temps réel
        return np.random.random() < 0.1  # 10% de chance de détecter un pattern
    
    async def _select_gas_strategy(self, network_conditions: Dict[str, Any],
                                 mev_risk: Dict[str, Any], urgency: str) -> GasStrategy:
        """Sélectionne la stratégie de gas optimale"""
        try:
            base_fee = network_conditions.get('current_base_fee', 50 * 10**9)
            congestion = network_conditions.get('network_congestion', 0.5)
            risk_score = mev_risk.get('score', 0.5)
            
            # Choisir la stratégie selon l'urgence et le risque
            if urgency == 'urgent' or risk_score > 0.7:
                strategy = self.gas_strategies['aggressive'].copy()
            elif urgency == 'low' and risk_score < 0.3:
                strategy = self.gas_strategies['conservative'].copy()
            else:
                strategy = self.gas_strategies['dynamic'].copy()
            
            # Ajuster les paramètres selon les conditions
            if strategy.strategy_type == 'dynamic':
                # Calculer les frais dynamiquement
                strategy.base_gas_price = base_fee
                strategy.priority_fee = max(
                    1 * 10**9,  # Minimum 1 gwei
                    int(base_fee * 0.1 * (1 + congestion))
                )
                strategy.max_fee_per_gas = base_fee + strategy.priority_fee * 2
            else:
                # Ajuster les stratégies fixes
                strategy.base_gas_price = base_fee
                strategy.max_fee_per_gas = base_fee + strategy.priority_fee * 2
            
            # Ajuster le gas limit selon le risque MEV
            if risk_score > 0.5:
                strategy.gas_limit = int(strategy.gas_limit * 1.2)  # 20% de marge
            
            return strategy
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.LOW, {
                'function': '_select_gas_strategy'
            })
            return self.gas_strategies['conservative']
    
    async def _calculate_optimal_timing(self, network_conditions: Dict[str, Any],
                                      mev_risk: Dict[str, Any]) -> TransactionTiming:
        """Calcule le timing optimal pour la transaction"""
        try:
            current_block = 18000000  # Simulé
            congestion = network_conditions.get('network_congestion', 0.5)
            risk_score = mev_risk.get('score', 0.5)
            
            # Calculer le délai optimal
            if risk_score > 0.7:
                # Risque élevé: attendre un bloc moins congestionné
                delay_blocks = np.random.randint(1, 3)
            elif congestion > 0.8:
                # Congestion élevée: attendre
                delay_blocks = np.random.randint(1, 2)
            else:
                # Conditions normales: exécuter immédiatement
                delay_blocks = 0
            
            optimal_block = current_block + delay_blocks
            
            # Calculer la confiance
            confidence = 1.0 - (congestion * 0.3 + risk_score * 0.4)
            confidence = max(0.1, min(1.0, confidence))
            
            # Estimer le prix du gas futur
            gas_trend = network_conditions.get('gas_trend', 'stable')
            current_gas = network_conditions.get('current_base_fee', 50 * 10**9)
            
            if gas_trend == 'rising':
                expected_gas = int(current_gas * 1.1)
            elif gas_trend == 'falling':
                expected_gas = int(current_gas * 0.9)
            else:
                expected_gas = current_gas
            
            return TransactionTiming(
                optimal_block=optimal_block,
                confidence=confidence,
                expected_gas_price=expected_gas,
                network_congestion=congestion,
                mev_risk_score=risk_score
            )
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.LOW, {
                'function': '_calculate_optimal_timing'
            })
            return TransactionTiming(
                optimal_block=18000000,
                confidence=0.5,
                expected_gas_price=50 * 10**9,
                network_congestion=0.5,
                mev_risk_score=0.5
            )
    
    async def _apply_mev_protection(self, transaction_data: Dict[str, Any],
                                  mev_risk: Dict[str, Any]) -> Dict[str, Any]:
        """Applique les protections MEV"""
        try:
            protected_tx = transaction_data.copy()
            risk_score = mev_risk.get('score', 0.5)
            recommendations = mev_risk.get('recommendations', [])
            
            # Protection contre le slippage
            if 'increase_slippage_protection' in recommendations:
                current_slippage = protected_tx.get('slippage_tolerance', 0.005)
                protected_tx['slippage_tolerance'] = max(
                    current_slippage * 0.5,  # Réduire le slippage de 50%
                    0.001  # Minimum 0.1%
                )
            
            # Deadline plus strict
            if 'tighter_deadline' in recommendations:
                current_deadline = protected_tx.get('deadline', 300)
                protected_tx['deadline'] = min(current_deadline, 60)  # Max 1 minute
            
            # Utilisation du mempool privé
            if 'use_private_mempool' in recommendations:
                protected_tx['use_private_mempool'] = True
                protected_tx['flashbots_bundle'] = True
            
            # Division de transaction
            if 'split_transaction' in recommendations:
                protected_tx['split_transaction'] = True
                protected_tx['split_count'] = min(3, max(2, int(risk_score * 5)))
            
            # Commit-reveal scheme
            if 'use_commit_reveal' in recommendations:
                protected_tx['commit_reveal'] = True
                protected_tx['commit_delay'] = np.random.randint(1, 3)  # 1-3 blocs
            
            # Ajouter des métadonnées de protection
            protected_tx['mev_protection'] = {
                'enabled': True,
                'risk_score': risk_score,
                'protections_applied': recommendations,
                'timestamp': datetime.now().isoformat()
            }
            
            self.transactions_protected += 1
            
            return protected_tx
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.MEDIUM, {
                'function': '_apply_mev_protection'
            })
            return transaction_data
    
    def _optimize_transaction_params(self, transaction_data: Dict[str, Any],
                                   gas_strategy: GasStrategy,
                                   timing: TransactionTiming) -> Dict[str, Any]:
        """Optimise les paramètres finaux de la transaction"""
        try:
            optimized_tx = transaction_data.copy()
            
            # Appliquer la stratégie de gas
            optimized_tx.update({
                'gasPrice': gas_strategy.base_gas_price,
                'maxFeePerGas': gas_strategy.max_fee_per_gas,
                'maxPriorityFeePerGas': gas_strategy.priority_fee,
                'gasLimit': gas_strategy.gas_limit
            })
            
            # Appliquer le timing optimal
            if timing.optimal_block > 18000000:  # Si délai nécessaire
                optimized_tx['target_block'] = timing.optimal_block
                optimized_tx['execution_delay'] = timing.optimal_block - 18000000
            
            # Calculer les économies estimées
            base_cost = 50 * 10**9 * 300000  # Coût de base
            optimized_cost = gas_strategy.max_fee_per_gas * gas_strategy.gas_limit
            estimated_savings = max(0, base_cost - optimized_cost)
            
            optimized_tx['estimated_savings'] = estimated_savings
            optimized_tx['optimization_confidence'] = timing.confidence
            
            # Ajouter les métadonnées d'optimisation
            optimized_tx['optimization'] = {
                'gas_strategy': gas_strategy.strategy_type,
                'timing_confidence': timing.confidence,
                'mev_risk_score': timing.mev_risk_score,
                'network_congestion': timing.network_congestion,
                'estimated_savings_wei': estimated_savings,
                'timestamp': datetime.now().isoformat()
            }
            
            return optimized_tx
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.LOW, {
                'function': '_optimize_transaction_params'
            })
            return transaction_data
    
    def get_mev_statistics(self) -> Dict[str, Any]:
        """Retourne les statistiques MEV"""
        total_gas_history = len(self.gas_price_history)
        
        if total_gas_history > 0:
            avg_gas_price = np.mean([g['base_fee'] for g in self.gas_price_history])
            gas_volatility = np.std([g['base_fee'] for g in self.gas_price_history])
        else:
            avg_gas_price = 0
            gas_volatility = 0
        
        return {
            'transactions_protected': self.transactions_protected,
            'mev_attacks_detected': self.mev_attacks_detected,
            'total_gas_saved': self.gas_saved,
            'average_gas_price': avg_gas_price,
            'gas_price_volatility': gas_volatility,
            'protection_rate': (self.transactions_protected / max(1, self.transactions_protected + self.mev_attacks_detected)) * 100
        }
    
    async def monitor_mempool(self):
        """Monitore le mempool pour détecter les attaques MEV"""
        # Simplification: monitoring basique
        # Dans un vrai projet, utiliser des outils comme Flashbots Protect
        while True:
            try:
                # Simuler la détection d'attaques
                if np.random.random() < 0.05:  # 5% de chance
                    self.mev_attacks_detected += 1
                    central_logger.log(
                        level="WARNING",
                        message="Attaque MEV potentielle détectée",
                        category=LogCategory.TRADING,
                        attack_type="sandwich",
                        timestamp=datetime.now().isoformat()
                    )
                
                await asyncio.sleep(1)  # Vérifier chaque seconde
                
            except Exception as e:
                error_handler.handle_error(e, ErrorCategory.NETWORK, ErrorSeverity.LOW, {
                    'function': 'monitor_mempool'
                })
                await asyncio.sleep(5)
