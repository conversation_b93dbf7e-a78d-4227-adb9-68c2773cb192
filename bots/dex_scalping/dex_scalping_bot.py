"""
🚀 Bot de scalping DEX principal
Bot de trading haute fréquence pour Uniswap v3 avec optimisation MEV
"""

import asyncio
import json
import time
from decimal import Decimal
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import logging
from pathlib import Path
import sys

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent.parent))

from bots.dex_scalping.uniswap_v3_connector import UniswapV3Connector
from bots.dex_scalping.scalping_engine import ScalpingEng<PERSON>, ScalpingConfig
from bots.dex_scalping.mev_optimizer import MEVOptimizer
from utils.error_handler import error_handler, ErrorCategory, ErrorSeverity
from logging_system.central_logger import central_logger, LogCategory
from risk_management.portfolio_manager import portfolio_manager
from utils.notifications import notifier

@dataclass
class BotConfig:
    """Configuration complète du bot"""
    # Connexion blockchain
    web3_provider: str
    private_key: str
    network: str = "mainnet"
    
    # Configuration de trading
    trading_pair: str = "ETH/WBTC"
    pool_fee: int = 3000  # 0.3%
    
    # Paramètres de scalping
    min_profit_threshold: float = 0.001  # 0.1%
    max_position_size: float = 0.05  # 5% du capital
    max_daily_trades: int = 100
    
    # Gestion des risques
    max_daily_loss: float = 0.02  # 2%
    emergency_stop_loss: float = 0.05  # 5%
    
    # Configuration MEV
    use_mev_protection: bool = True
    max_gas_price_gwei: int = 200
    
    # Monitoring
    enable_notifications: bool = True
    performance_report_interval: int = 3600  # 1 heure

class DEXScalpingBot:
    """Bot de scalping DEX principal"""
    
    def __init__(self, config: BotConfig):
        self.logger = logging.getLogger(__name__)
        self.config = config
        
        # État du bot
        self.is_running = False
        self.start_time = None
        self.last_performance_report = 0
        
        # Composants principaux
        self.connector = None
        self.scalping_engine = None
        self.mev_optimizer = None
        
        # Métriques de performance
        self.daily_trades = 0
        self.daily_pnl = 0.0
        self.total_gas_spent = 0.0
        self.last_reset_date = datetime.now().date()
        
        # Gestion des erreurs
        self.consecutive_errors = 0
        self.max_consecutive_errors = 5
        
        central_logger.log(
            level="INFO",
            message="Bot de scalping DEX initialisé",
            category=LogCategory.STRATEGY,
            trading_pair=config.trading_pair,
            network=config.network
        )
    
    async def initialize(self):
        """Initialise tous les composants du bot"""
        try:
            central_logger.log(
                level="INFO",
                message="Initialisation des composants du bot",
                category=LogCategory.SYSTEM
            )
            
            # Initialiser le connecteur Uniswap v3
            self.connector = UniswapV3Connector(
                web3_provider=self.config.web3_provider,
                private_key=self.config.private_key,
                network=self.config.network
            )
            
            # Initialiser l'optimiseur MEV
            if self.config.use_mev_protection:
                self.mev_optimizer = MEVOptimizer(
                    web3_provider=self.config.web3_provider,
                    network=self.config.network
                )
            
            # Configurer le moteur de scalping
            token_addresses = self._get_token_addresses()
            scalping_config = ScalpingConfig(
                token_in=token_addresses['token_in'],
                token_out=token_addresses['token_out'],
                pool_fee=self.config.pool_fee,
                min_profit_threshold=self.config.min_profit_threshold,
                max_position_size=self.config.max_position_size,
                max_gas_price_gwei=self.config.max_gas_price_gwei
            )
            
            self.scalping_engine = ScalpingEngine(self.connector, scalping_config)
            
            # Vérifier les balances
            await self._check_initial_balances()
            
            central_logger.log(
                level="INFO",
                message="Tous les composants initialisés avec succès",
                category=LogCategory.SYSTEM
            )
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.CRITICAL, {
                'function': 'initialize'
            })
            raise
    
    def _get_token_addresses(self) -> Dict[str, str]:
        """Récupère les adresses des tokens selon la paire"""
        if self.config.trading_pair == "ETH/WBTC":
            return {
                'token_in': self.connector.tokens['WETH'],
                'token_out': self.connector.tokens['WBTC']
            }
        else:
            raise ValueError(f"Paire non supportée: {self.config.trading_pair}")
    
    async def _check_initial_balances(self):
        """Vérifie les balances initiales"""
        try:
            token_addresses = self._get_token_addresses()
            
            # Vérifier les balances (simplification)
            eth_balance = self.connector.get_token_balance(token_addresses['token_in'])
            wbtc_balance = self.connector.get_token_balance(token_addresses['token_out'])
            
            central_logger.log(
                level="INFO",
                message="Balances initiales vérifiées",
                category=LogCategory.TRADING,
                eth_balance=eth_balance,
                wbtc_balance=wbtc_balance
            )
            
            # Vérifier qu'on a suffisamment de fonds
            if eth_balance < 10**16:  # Moins de 0.01 ETH
                raise Exception("Balance ETH insuffisante pour le trading")
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.HIGH, {
                'function': '_check_initial_balances'
            })
            raise
    
    async def start(self):
        """Démarre le bot de scalping"""
        if self.is_running:
            self.logger.warning("Le bot est déjà en cours d'exécution")
            return
        
        try:
            # Initialiser si pas encore fait
            if not self.connector:
                await self.initialize()
            
            self.is_running = True
            self.start_time = datetime.now()
            
            central_logger.strategy_action(
                message="Démarrage du bot de scalping DEX",
                strategy_name="DEX_Scalping_Bot",
                action="start",
                symbol=self.config.trading_pair
            )
            
            # Notification de démarrage
            if self.config.enable_notifications:
                await notifier.send_telegram(
                    f"🚀 <b>Bot DEX Scalping démarré</b>\n"
                    f"📊 Paire: {self.config.trading_pair}\n"
                    f"🌐 Réseau: {self.config.network}\n"
                    f"⚡ Protection MEV: {'✅' if self.config.use_mev_protection else '❌'}"
                )
            
            # Démarrer le monitoring MEV si activé
            if self.mev_optimizer:
                asyncio.create_task(self.mev_optimizer.monitor_mempool())
            
            # Boucle principale
            await self._main_loop()
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.CRITICAL, {
                'function': 'start'
            })
            await self.stop()
    
    async def stop(self):
        """Arrête le bot de scalping"""
        if not self.is_running:
            return
        
        self.is_running = False
        
        try:
            # Arrêter le moteur de scalping
            if self.scalping_engine:
                await self.scalping_engine.stop()
            
            # Générer le rapport final
            final_report = await self._generate_performance_report()
            
            central_logger.strategy_action(
                message="Arrêt du bot de scalping DEX",
                strategy_name="DEX_Scalping_Bot",
                action="stop",
                **final_report
            )
            
            # Notification d'arrêt
            if self.config.enable_notifications:
                await notifier.send_telegram(
                    f"🛑 <b>Bot DEX Scalping arrêté</b>\n"
                    f"📊 Trades: {final_report.get('total_trades', 0)}\n"
                    f"💰 P&L: {final_report.get('total_pnl', 0):.4f}\n"
                    f"📈 Taux de réussite: {final_report.get('win_rate', 0):.1f}%"
                )
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.MEDIUM, {
                'function': 'stop'
            })
    
    async def _main_loop(self):
        """Boucle principale du bot"""
        while self.is_running:
            try:
                # Réinitialiser les compteurs quotidiens si nécessaire
                self._reset_daily_counters()
                
                # Vérifier les limites de sécurité
                if not self._check_safety_limits():
                    await self._emergency_stop("Limites de sécurité dépassées")
                    break
                
                # Démarrer le moteur de scalping
                if self.scalping_engine and not self.scalping_engine.is_running:
                    scalping_task = asyncio.create_task(self.scalping_engine.start())
                
                # Monitoring et rapports
                await self._monitoring_cycle()
                
                # Attendre avant le prochain cycle
                await asyncio.sleep(1)
                
            except Exception as e:
                self.consecutive_errors += 1
                error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.HIGH, {
                    'function': '_main_loop',
                    'consecutive_errors': self.consecutive_errors
                })
                
                # Arrêt d'urgence si trop d'erreurs consécutives
                if self.consecutive_errors >= self.max_consecutive_errors:
                    await self._emergency_stop("Trop d'erreurs consécutives")
                    break
                
                await asyncio.sleep(5)  # Pause avant de réessayer
            else:
                self.consecutive_errors = 0  # Reset si pas d'erreur
    
    def _reset_daily_counters(self):
        """Réinitialise les compteurs quotidiens"""
        today = datetime.now().date()
        if today != self.last_reset_date:
            self.daily_trades = 0
            self.daily_pnl = 0.0
            self.total_gas_spent = 0.0
            self.last_reset_date = today
            
            central_logger.log(
                level="INFO",
                message="Compteurs quotidiens réinitialisés",
                category=LogCategory.SYSTEM,
                date=today.isoformat()
            )
    
    def _check_safety_limits(self) -> bool:
        """Vérifie les limites de sécurité"""
        # Vérifier les trades quotidiens
        if self.daily_trades >= self.config.max_daily_trades:
            central_logger.log(
                level="WARNING",
                message="Limite quotidienne de trades atteinte",
                category=LogCategory.RISK,
                daily_trades=self.daily_trades,
                limit=self.config.max_daily_trades
            )
            return False
        
        # Vérifier les pertes quotidiennes
        portfolio_value = portfolio_manager.get_portfolio_value()
        if portfolio_value > 0:
            daily_loss_pct = abs(self.daily_pnl) / portfolio_value
            if self.daily_pnl < 0 and daily_loss_pct > self.config.max_daily_loss:
                central_logger.log(
                    level="WARNING",
                    message="Limite quotidienne de perte atteinte",
                    category=LogCategory.RISK,
                    daily_pnl=self.daily_pnl,
                    loss_percentage=daily_loss_pct * 100
                )
                return False
        
        return True
    
    async def _monitoring_cycle(self):
        """Cycle de monitoring et rapports"""
        current_time = time.time()
        
        # Rapport de performance périodique
        if current_time - self.last_performance_report > self.config.performance_report_interval:
            await self._send_performance_report()
            self.last_performance_report = current_time
        
        # Mettre à jour les métriques
        await self._update_metrics()
    
    async def _update_metrics(self):
        """Met à jour les métriques du bot"""
        try:
            if self.scalping_engine:
                engine_stats = self.scalping_engine.get_performance_stats()
                self.daily_trades = engine_stats.get('total_trades', 0)
                self.daily_pnl = engine_stats.get('total_profit', 0.0)
            
            # Enregistrer les métriques
            central_logger.performance_metric(
                metric_name="daily_trades",
                value=self.daily_trades,
                unit="count"
            )
            
            central_logger.performance_metric(
                metric_name="daily_pnl",
                value=self.daily_pnl,
                unit="USD"
            )
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.LOW, {
                'function': '_update_metrics'
            })
    
    async def _send_performance_report(self):
        """Envoie un rapport de performance"""
        try:
            report = await self._generate_performance_report()
            
            if self.config.enable_notifications:
                message = (
                    f"📊 <b>Rapport de performance</b>\n"
                    f"⏱️ Uptime: {report['uptime_hours']:.1f}h\n"
                    f"📈 Trades: {report['total_trades']}\n"
                    f"💰 P&L: {report['total_pnl']:.4f}\n"
                    f"📊 Taux de réussite: {report['win_rate']:.1f}%\n"
                    f"⛽ Gas dépensé: {report['total_gas_spent']:.6f} ETH"
                )
                
                await notifier.send_telegram(message)
            
            central_logger.log(
                level="INFO",
                message="Rapport de performance généré",
                category=LogCategory.PERFORMANCE,
                **report
            )
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.LOW, {
                'function': '_send_performance_report'
            })
    
    async def _generate_performance_report(self) -> Dict[str, Any]:
        """Génère un rapport de performance complet"""
        try:
            # Statistiques du moteur de scalping
            engine_stats = {}
            if self.scalping_engine:
                engine_stats = self.scalping_engine.get_performance_stats()
            
            # Statistiques MEV
            mev_stats = {}
            if self.mev_optimizer:
                mev_stats = self.mev_optimizer.get_mev_statistics()
            
            # Calcul de l'uptime
            uptime_hours = 0
            if self.start_time:
                uptime = datetime.now() - self.start_time
                uptime_hours = uptime.total_seconds() / 3600
            
            return {
                'uptime_hours': uptime_hours,
                'total_trades': engine_stats.get('total_trades', 0),
                'successful_trades': engine_stats.get('successful_trades', 0),
                'win_rate': engine_stats.get('win_rate', 0),
                'total_pnl': engine_stats.get('total_profit', 0),
                'total_gas_spent': self.total_gas_spent,
                'daily_trades': self.daily_trades,
                'daily_pnl': self.daily_pnl,
                'mev_protection_rate': mev_stats.get('protection_rate', 0),
                'consecutive_errors': self.consecutive_errors,
                'is_running': self.is_running
            }
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.LOW, {
                'function': '_generate_performance_report'
            })
            return {}
    
    async def _emergency_stop(self, reason: str):
        """Arrêt d'urgence du bot"""
        central_logger.emergency_stop(
            message=f"Arrêt d'urgence du bot de scalping: {reason}",
            reason=reason,
            daily_trades=self.daily_trades,
            daily_pnl=self.daily_pnl
        )
        
        if self.config.enable_notifications:
            await notifier.send_telegram(
                f"🚨 <b>ARRÊT D'URGENCE</b>\n"
                f"🤖 Bot: DEX Scalping\n"
                f"⚠️ Raison: {reason}\n"
                f"📊 Trades: {self.daily_trades}\n"
                f"💰 P&L: {self.daily_pnl:.4f}"
            )
        
        await self.stop()
    
    def get_status(self) -> Dict[str, Any]:
        """Retourne le statut actuel du bot"""
        return {
            'is_running': self.is_running,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'trading_pair': self.config.trading_pair,
            'network': self.config.network,
            'daily_trades': self.daily_trades,
            'daily_pnl': self.daily_pnl,
            'consecutive_errors': self.consecutive_errors,
            'mev_protection_enabled': self.config.use_mev_protection,
            'current_position': self.scalping_engine.current_position is not None if self.scalping_engine else False
        }

# Fonction utilitaire pour créer et lancer le bot
async def create_and_run_bot(config_dict: Dict[str, Any]):
    """Crée et lance le bot avec la configuration donnée"""
    try:
        # Créer la configuration
        config = BotConfig(**config_dict)
        
        # Créer le bot
        bot = DEXScalpingBot(config)
        
        # Initialiser et démarrer
        await bot.initialize()
        await bot.start()
        
    except KeyboardInterrupt:
        central_logger.log(
            level="INFO",
            message="Arrêt du bot demandé par l'utilisateur",
            category=LogCategory.SYSTEM
        )
        if 'bot' in locals():
            await bot.stop()
    except Exception as e:
        error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.CRITICAL, {
            'function': 'create_and_run_bot'
        })
        if 'bot' in locals():
            await bot.stop()

if __name__ == "__main__":
    # Configuration d'exemple
    config = {
        'web3_provider': 'https://mainnet.infura.io/v3/YOUR_PROJECT_ID',
        'private_key': 'YOUR_PRIVATE_KEY',
        'network': 'mainnet',
        'trading_pair': 'ETH/WBTC',
        'min_profit_threshold': 0.001,
        'max_position_size': 0.05,
        'use_mev_protection': True,
        'enable_notifications': True
    }
    
    asyncio.run(create_and_run_bot(config))
