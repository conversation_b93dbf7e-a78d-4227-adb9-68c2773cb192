"""
🔗 Connecteur Uniswap v3 pour trading DEX
Interface avec les contrats Uniswap v3 pour le scalping
"""

import asyncio
import json
import time
from decimal import Decimal, ROUND_DOWN
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from web3 import Web3
from web3.contract import Contract
from eth_account import Account
import logging
from pathlib import Path
import sys

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent.parent))

from utils.error_handler import error_handler, ErrorCategory, ErrorSeverity
from logging_system.central_logger import central_logger, LogCategory

@dataclass
class PoolInfo:
    """Informations sur une pool Uniswap v3"""
    address: str
    token0: str
    token1: str
    fee: int
    tick_spacing: int
    liquidity: int
    sqrt_price_x96: int
    tick: int
    fee_growth_global0_x128: int
    fee_growth_global1_x128: int

@dataclass
class SwapParams:
    """Paramètres pour un swap"""
    token_in: str
    token_out: str
    amount_in: int
    amount_out_minimum: int
    sqrt_price_limit_x96: int
    deadline: int

@dataclass
class QuoteResult:
    """Résultat d'une cotation"""
    amount_out: int
    sqrt_price_x96_after: int
    tick_after: int
    gas_estimate: int

class UniswapV3Connector:
    """Connecteur pour Uniswap v3"""
    
    def __init__(self, web3_provider: str, private_key: str, network: str = "mainnet"):
        self.logger = logging.getLogger(__name__)
        
        # Configuration Web3
        self.w3 = Web3(Web3.HTTPProvider(web3_provider))
        self.account = Account.from_key(private_key)
        self.address = self.account.address
        
        # Configuration réseau
        self.network = network
        self.chain_id = 1 if network == "mainnet" else 5  # Goerli testnet
        
        # Adresses des contrats Uniswap v3
        self.contracts = self._get_contract_addresses()
        
        # Charger les ABIs
        self.abis = self._load_abis()
        
        # Initialiser les contrats
        self.router = self.w3.eth.contract(
            address=self.contracts['SwapRouter'],
            abi=self.abis['SwapRouter']
        )
        
        self.factory = self.w3.eth.contract(
            address=self.contracts['Factory'],
            abi=self.abis['Factory']
        )
        
        self.quoter = self.w3.eth.contract(
            address=self.contracts['Quoter'],
            abi=self.abis['Quoter']
        )
        
        # Cache des pools
        self.pools_cache: Dict[str, PoolInfo] = {}
        
        # Configuration des tokens
        self.tokens = self._get_token_addresses()
        
        central_logger.log(
            level="INFO",
            message=f"Connecteur Uniswap v3 initialisé pour {network}",
            category=LogCategory.SYSTEM,
            address=self.address,
            network=network
        )
    
    def _get_contract_addresses(self) -> Dict[str, str]:
        """Retourne les adresses des contrats selon le réseau"""
        if self.network == "mainnet":
            return {
                'Factory': '******************************************',
                'SwapRouter': '******************************************',
                'Quoter': '******************************************',
                'NonfungiblePositionManager': '******************************************'
            }
        else:  # Goerli testnet
            return {
                'Factory': '******************************************',
                'SwapRouter': '******************************************',
                'Quoter': '******************************************',
                'NonfungiblePositionManager': '******************************************'
            }
    
    def _get_token_addresses(self) -> Dict[str, str]:
        """Retourne les adresses des tokens selon le réseau"""
        if self.network == "mainnet":
            return {
                'WETH': '******************************************',
                'WBTC': '******************************************',
                'USDC': '******************************************',
                'USDT': '******************************************'
            }
        else:  # Goerli testnet
            return {
                'WETH': '0xB4FBF271143F4FBf88A4c4c6b4c4c6b4c4c6b4c4c',
                'WBTC': '******************************************',  # Placeholder
                'USDC': '******************************************',
                'USDT': '******************************************'   # Placeholder
            }
    
    def _load_abis(self) -> Dict[str, List]:
        """Charge les ABIs des contrats (version simplifiée)"""
        # Dans un vrai projet, ces ABIs seraient chargés depuis des fichiers
        return {
            'SwapRouter': [
                {
                    "inputs": [
                        {
                            "components": [
                                {"internalType": "address", "name": "tokenIn", "type": "address"},
                                {"internalType": "address", "name": "tokenOut", "type": "address"},
                                {"internalType": "uint24", "name": "fee", "type": "uint24"},
                                {"internalType": "address", "name": "recipient", "type": "address"},
                                {"internalType": "uint256", "name": "deadline", "type": "uint256"},
                                {"internalType": "uint256", "name": "amountIn", "type": "uint256"},
                                {"internalType": "uint256", "name": "amountOutMinimum", "type": "uint256"},
                                {"internalType": "uint160", "name": "sqrtPriceLimitX96", "type": "uint160"}
                            ],
                            "internalType": "struct ISwapRouter.ExactInputSingleParams",
                            "name": "params",
                            "type": "tuple"
                        }
                    ],
                    "name": "exactInputSingle",
                    "outputs": [{"internalType": "uint256", "name": "amountOut", "type": "uint256"}],
                    "stateMutability": "payable",
                    "type": "function"
                }
            ],
            'Factory': [
                {
                    "inputs": [
                        {"internalType": "address", "name": "tokenA", "type": "address"},
                        {"internalType": "address", "name": "tokenB", "type": "address"},
                        {"internalType": "uint24", "name": "fee", "type": "uint24"}
                    ],
                    "name": "getPool",
                    "outputs": [{"internalType": "address", "name": "pool", "type": "address"}],
                    "stateMutability": "view",
                    "type": "function"
                }
            ],
            'Quoter': [
                {
                    "inputs": [
                        {"internalType": "address", "name": "tokenIn", "type": "address"},
                        {"internalType": "address", "name": "tokenOut", "type": "address"},
                        {"internalType": "uint24", "name": "fee", "type": "uint24"},
                        {"internalType": "uint256", "name": "amountIn", "type": "uint256"},
                        {"internalType": "uint160", "name": "sqrtPriceLimitX96", "type": "uint160"}
                    ],
                    "name": "quoteExactInputSingle",
                    "outputs": [
                        {"internalType": "uint256", "name": "amountOut", "type": "uint256"},
                        {"internalType": "uint160", "name": "sqrtPriceX96After", "type": "uint160"},
                        {"internalType": "uint32", "name": "initializedTicksCrossed", "type": "uint32"},
                        {"internalType": "uint256", "name": "gasEstimate", "type": "uint256"}
                    ],
                    "stateMutability": "nonpayable",
                    "type": "function"
                }
            ],
            'Pool': [
                {
                    "inputs": [],
                    "name": "slot0",
                    "outputs": [
                        {"internalType": "uint160", "name": "sqrtPriceX96", "type": "uint160"},
                        {"internalType": "int24", "name": "tick", "type": "int24"},
                        {"internalType": "uint16", "name": "observationIndex", "type": "uint16"},
                        {"internalType": "uint16", "name": "observationCardinality", "type": "uint16"},
                        {"internalType": "uint16", "name": "observationCardinalityNext", "type": "uint16"},
                        {"internalType": "uint8", "name": "feeProtocol", "type": "uint8"},
                        {"internalType": "bool", "name": "unlocked", "type": "bool"}
                    ],
                    "stateMutability": "view",
                    "type": "function"
                },
                {
                    "inputs": [],
                    "name": "liquidity",
                    "outputs": [{"internalType": "uint128", "name": "", "type": "uint128"}],
                    "stateMutability": "view",
                    "type": "function"
                }
            ]
        }
    
    async def get_pool_info(self, token0: str, token1: str, fee: int) -> Optional[PoolInfo]:
        """Récupère les informations d'une pool"""
        try:
            # Obtenir l'adresse de la pool
            pool_address = self.factory.functions.getPool(token0, token1, fee).call()
            
            if pool_address == '******************************************':
                return None
            
            # Créer le contrat de la pool
            pool_contract = self.w3.eth.contract(
                address=pool_address,
                abi=self.abis['Pool']
            )
            
            # Récupérer les données de la pool
            slot0 = pool_contract.functions.slot0().call()
            liquidity = pool_contract.functions.liquidity().call()
            
            pool_info = PoolInfo(
                address=pool_address,
                token0=token0,
                token1=token1,
                fee=fee,
                tick_spacing=self._get_tick_spacing(fee),
                liquidity=liquidity,
                sqrt_price_x96=slot0[0],
                tick=slot0[1],
                fee_growth_global0_x128=0,  # Simplification
                fee_growth_global1_x128=0   # Simplification
            )
            
            # Mettre en cache
            cache_key = f"{token0}_{token1}_{fee}"
            self.pools_cache[cache_key] = pool_info
            
            return pool_info
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.NETWORK, ErrorSeverity.MEDIUM, {
                'function': 'get_pool_info',
                'token0': token0,
                'token1': token1,
                'fee': fee
            })
            return None
    
    def _get_tick_spacing(self, fee: int) -> int:
        """Retourne le tick spacing selon les frais"""
        spacing_map = {
            500: 10,     # 0.05%
            3000: 60,    # 0.3%
            10000: 200   # 1%
        }
        return spacing_map.get(fee, 60)
    
    async def get_quote(self, token_in: str, token_out: str, amount_in: int, 
                       fee: int = 3000) -> Optional[QuoteResult]:
        """Obtient une cotation pour un swap"""
        try:
            # Appel au quoter
            result = self.quoter.functions.quoteExactInputSingle(
                token_in,
                token_out,
                fee,
                amount_in,
                0  # sqrtPriceLimitX96 = 0 (pas de limite)
            ).call()
            
            return QuoteResult(
                amount_out=result[0],
                sqrt_price_x96_after=result[1],
                tick_after=0,  # Pas disponible dans cette version
                gas_estimate=result[3]
            )
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.NETWORK, ErrorSeverity.MEDIUM, {
                'function': 'get_quote',
                'token_in': token_in,
                'token_out': token_out,
                'amount_in': amount_in
            })
            return None
    
    async def execute_swap(self, token_in: str, token_out: str, amount_in: int,
                          amount_out_minimum: int, fee: int = 3000,
                          slippage_tolerance: float = 0.005) -> Optional[str]:
        """Exécute un swap"""
        try:
            # Calculer le deadline (5 minutes)
            deadline = int(time.time()) + 300
            
            # Paramètres du swap
            swap_params = {
                'tokenIn': token_in,
                'tokenOut': token_out,
                'fee': fee,
                'recipient': self.address,
                'deadline': deadline,
                'amountIn': amount_in,
                'amountOutMinimum': amount_out_minimum,
                'sqrtPriceLimitX96': 0
            }
            
            # Estimer le gas
            gas_estimate = self.router.functions.exactInputSingle(swap_params).estimateGas({
                'from': self.address,
                'value': amount_in if token_in == self.tokens['WETH'] else 0
            })
            
            # Ajouter une marge de sécurité au gas
            gas_limit = int(gas_estimate * 1.2)
            
            # Construire la transaction
            transaction = self.router.functions.exactInputSingle(swap_params).buildTransaction({
                'from': self.address,
                'gas': gas_limit,
                'gasPrice': self.w3.eth.gas_price,
                'nonce': self.w3.eth.get_transaction_count(self.address),
                'value': amount_in if token_in == self.tokens['WETH'] else 0
            })
            
            # Signer la transaction
            signed_txn = self.w3.eth.account.sign_transaction(transaction, self.account.key)
            
            # Envoyer la transaction
            tx_hash = self.w3.eth.send_raw_transaction(signed_txn.rawTransaction)
            
            central_logger.trade_executed(
                message=f"Swap exécuté: {amount_in} -> {amount_out_minimum}",
                symbol=f"{token_in}/{token_out}",
                side="swap",
                quantity=amount_in,
                price=amount_out_minimum / amount_in if amount_in > 0 else 0,
                trade_id=tx_hash.hex(),
                fee=fee,
                gas_used=gas_limit
            )
            
            return tx_hash.hex()
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.HIGH, {
                'function': 'execute_swap',
                'token_in': token_in,
                'token_out': token_out,
                'amount_in': amount_in
            })
            return None
    
    async def wait_for_transaction(self, tx_hash: str, timeout: int = 300) -> Optional[Dict]:
        """Attend la confirmation d'une transaction"""
        try:
            receipt = self.w3.eth.wait_for_transaction_receipt(tx_hash, timeout=timeout)
            
            central_logger.log(
                level="INFO",
                message=f"Transaction confirmée: {tx_hash}",
                category=LogCategory.TRADING,
                tx_hash=tx_hash,
                block_number=receipt.blockNumber,
                gas_used=receipt.gasUsed,
                status=receipt.status
            )
            
            return {
                'tx_hash': tx_hash,
                'block_number': receipt.blockNumber,
                'gas_used': receipt.gasUsed,
                'status': receipt.status,
                'success': receipt.status == 1
            }
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.NETWORK, ErrorSeverity.MEDIUM, {
                'function': 'wait_for_transaction',
                'tx_hash': tx_hash
            })
            return None
    
    def calculate_price_impact(self, amount_in: int, amount_out_before: int, 
                             amount_out_after: int) -> float:
        """Calcule l'impact sur le prix"""
        if amount_out_before == 0:
            return 0.0
        
        expected_rate = amount_out_before / amount_in
        actual_rate = amount_out_after / amount_in
        
        return (expected_rate - actual_rate) / expected_rate
    
    def get_token_balance(self, token_address: str) -> int:
        """Récupère le solde d'un token"""
        try:
            if token_address == self.tokens['WETH']:
                # Pour WETH, on peut aussi vérifier le solde ETH
                return self.w3.eth.get_balance(self.address)
            else:
                # Pour les autres tokens ERC20
                # Ici on devrait utiliser l'ABI ERC20, simplifié pour l'exemple
                return 0
                
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.NETWORK, ErrorSeverity.LOW, {
                'function': 'get_token_balance',
                'token_address': token_address
            })
            return 0
    
    def calculate_optimal_gas_price(self) -> int:
        """Calcule le prix du gas optimal"""
        try:
            # Prix du gas actuel
            current_gas_price = self.w3.eth.gas_price
            
            # Pour le scalping, on peut payer un peu plus pour être prioritaire
            optimal_gas_price = int(current_gas_price * 1.1)
            
            return optimal_gas_price
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.NETWORK, ErrorSeverity.LOW, {
                'function': 'calculate_optimal_gas_price'
            })
            return self.w3.eth.gas_price
