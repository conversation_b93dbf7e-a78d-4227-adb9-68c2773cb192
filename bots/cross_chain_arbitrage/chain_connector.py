"""
🔗 Connecteur multi-chaînes pour arbitrage
Interface unifiée pour interagir avec différentes blockchains
"""

import asyncio
import json
import time
from decimal import Decimal, ROUND_DOWN
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass
from web3 import Web3
from web3.contract import Contract
import logging
from pathlib import Path
import sys

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent.parent))

from utils.error_handler import error_handler, ErrorCategory, ErrorSeverity
from logging_system.central_logger import central_logger, LogCategory

@dataclass
class ChainConfig:
    """Configuration d'une blockchain"""
    name: str
    chain_id: int
    rpc_url: str
    native_token: str
    block_time: float  # secondes
    gas_price_gwei: int
    bridge_contracts: Dict[str, str]
    dex_routers: Dict[str, str]
    stablecoin_addresses: Dict[str, str]

@dataclass
class TokenInfo:
    """Informations sur un token"""
    address: str
    symbol: str
    decimals: int
    chain: str
    is_native: bool = False

@dataclass
class PriceData:
    """Données de prix d'un token"""
    token: TokenInfo
    price_usd: float
    liquidity_usd: float
    volume_24h: float
    timestamp: float
    source: str  # DEX source

@dataclass
class BridgeRoute:
    """Route de bridge entre chaînes"""
    from_chain: str
    to_chain: str
    token_symbol: str
    bridge_name: str
    min_amount: float
    max_amount: float
    fee_percentage: float
    estimated_time_minutes: int

class ChainConnector:
    """Connecteur pour interagir avec multiple blockchains"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Configuration des chaînes supportées
        self.chains = self._initialize_chains()
        
        # Connexions Web3
        self.web3_connections: Dict[str, Web3] = {}
        
        # Cache des contrats
        self.contracts_cache: Dict[str, Contract] = {}
        
        # Cache des prix
        self.price_cache: Dict[str, PriceData] = {}
        self.cache_ttl = 30  # 30 secondes
        
        # Routes de bridge disponibles
        self.bridge_routes = self._initialize_bridge_routes()
        
        # Initialiser les connexions
        self._initialize_connections()
        
        central_logger.log(
            level="INFO",
            message="Connecteur multi-chaînes initialisé",
            category=LogCategory.SYSTEM,
            chains_count=len(self.chains)
        )
    
    def _initialize_chains(self) -> Dict[str, ChainConfig]:
        """Initialise la configuration des chaînes"""
        return {
            'ethereum': ChainConfig(
                name='Ethereum',
                chain_id=1,
                rpc_url='https://mainnet.infura.io/v3/YOUR_PROJECT_ID',
                native_token='ETH',
                block_time=12.0,
                gas_price_gwei=50,
                bridge_contracts={
                    'polygon': '******************************************',
                    'bsc': '******************************************',
                    'arbitrum': '******************************************'
                },
                dex_routers={
                    'uniswap_v2': '******************************************',
                    'uniswap_v3': '******************************************',
                    'sushiswap': '******************************************'
                },
                stablecoin_addresses={
                    'USDT': '******************************************',
                    'USDC': '******************************************',
                    'DAI': '******************************************',
                    'BUSD': '******************************************'
                }
            ),
            'bsc': ChainConfig(
                name='Binance Smart Chain',
                chain_id=56,
                rpc_url='https://bsc-dataseed1.binance.org/',
                native_token='BNB',
                block_time=3.0,
                gas_price_gwei=5,
                bridge_contracts={
                    'ethereum': '******************************************',
                    'polygon': '******************************************'
                },
                dex_routers={
                    'pancakeswap_v2': '******************************************',
                    'pancakeswap_v3': '******************************************',
                    'biswap': '******************************************'
                },
                stablecoin_addresses={
                    'USDT': '******************************************',
                    'USDC': '******************************************',
                    'BUSD': '******************************************',
                    'DAI': '******************************************'
                }
            ),
            'polygon': ChainConfig(
                name='Polygon',
                chain_id=137,
                rpc_url='https://polygon-rpc.com/',
                native_token='MATIC',
                block_time=2.0,
                gas_price_gwei=30,
                bridge_contracts={
                    'ethereum': '******************************************',
                    'bsc': '******************************************'
                },
                dex_routers={
                    'quickswap': '******************************************',
                    'sushiswap': '******************************************',
                    'uniswap_v3': '******************************************'
                },
                stablecoin_addresses={
                    'USDT': '******************************************',
                    'USDC': '******************************************',
                    'DAI': '******************************************'
                }
            ),
            'arbitrum': ChainConfig(
                name='Arbitrum One',
                chain_id=42161,
                rpc_url='https://arb1.arbitrum.io/rpc',
                native_token='ETH',
                block_time=0.25,
                gas_price_gwei=1,
                bridge_contracts={
                    'ethereum': '******************************************'
                },
                dex_routers={
                    'uniswap_v3': '******************************************',
                    'sushiswap': '******************************************',
                    'camelot': '******************************************'
                },
                stablecoin_addresses={
                    'USDT': '******************************************',
                    'USDC': '******************************************',
                    'DAI': '******************************************'
                }
            )
        }
    
    def _initialize_bridge_routes(self) -> List[BridgeRoute]:
        """Initialise les routes de bridge disponibles"""
        routes = []
        
        # Routes Ethereum <-> Polygon (PoS Bridge)
        for token in ['USDT', 'USDC', 'DAI']:
            routes.extend([
                BridgeRoute(
                    from_chain='ethereum',
                    to_chain='polygon',
                    token_symbol=token,
                    bridge_name='Polygon PoS Bridge',
                    min_amount=10,
                    max_amount=1000000,
                    fee_percentage=0.0,  # Pas de frais, juste gas
                    estimated_time_minutes=7
                ),
                BridgeRoute(
                    from_chain='polygon',
                    to_chain='ethereum',
                    token_symbol=token,
                    bridge_name='Polygon PoS Bridge',
                    min_amount=10,
                    max_amount=1000000,
                    fee_percentage=0.0,
                    estimated_time_minutes=30  # Challenge period
                )
            ])
        
        # Routes Ethereum <-> BSC (Binance Bridge)
        for token in ['USDT', 'USDC', 'BUSD']:
            routes.extend([
                BridgeRoute(
                    from_chain='ethereum',
                    to_chain='bsc',
                    token_symbol=token,
                    bridge_name='Binance Bridge',
                    min_amount=50,
                    max_amount=500000,
                    fee_percentage=0.1,  # 0.1% de frais
                    estimated_time_minutes=15
                ),
                BridgeRoute(
                    from_chain='bsc',
                    to_chain='ethereum',
                    token_symbol=token,
                    bridge_name='Binance Bridge',
                    min_amount=50,
                    max_amount=500000,
                    fee_percentage=0.1,
                    estimated_time_minutes=15
                )
            ])
        
        # Routes Ethereum <-> Arbitrum (Native Bridge)
        for token in ['USDT', 'USDC', 'DAI']:
            routes.extend([
                BridgeRoute(
                    from_chain='ethereum',
                    to_chain='arbitrum',
                    token_symbol=token,
                    bridge_name='Arbitrum Bridge',
                    min_amount=20,
                    max_amount=2000000,
                    fee_percentage=0.0,
                    estimated_time_minutes=10
                ),
                BridgeRoute(
                    from_chain='arbitrum',
                    to_chain='ethereum',
                    token_symbol=token,
                    bridge_name='Arbitrum Bridge',
                    min_amount=20,
                    max_amount=2000000,
                    fee_percentage=0.0,
                    estimated_time_minutes=10080  # 7 jours challenge
                )
            ])
        
        return routes
    
    def _initialize_connections(self):
        """Initialise les connexions Web3 pour chaque chaîne"""
        for chain_name, config in self.chains.items():
            try:
                # Remplacer YOUR_PROJECT_ID par une vraie clé en production
                rpc_url = config.rpc_url.replace('YOUR_PROJECT_ID', 'demo')
                
                # Créer la connexion Web3
                w3 = Web3(Web3.HTTPProvider(rpc_url))
                
                # Vérifier la connexion (simulation)
                # En production, on ferait: w3.isConnected()
                self.web3_connections[chain_name] = w3
                
                central_logger.log(
                    level="INFO",
                    message=f"Connexion établie avec {config.name}",
                    category=LogCategory.NETWORK,
                    chain=chain_name,
                    chain_id=config.chain_id
                )
                
            except Exception as e:
                error_handler.handle_error(e, ErrorCategory.NETWORK, ErrorSeverity.HIGH, {
                    'function': '_initialize_connections',
                    'chain': chain_name
                })
    
    async def get_token_price(self, chain: str, token_symbol: str, 
                            dex: str = None) -> Optional[PriceData]:
        """Récupère le prix d'un token sur une chaîne donnée"""
        try:
            cache_key = f"{chain}_{token_symbol}_{dex or 'default'}"
            
            # Vérifier le cache
            if cache_key in self.price_cache:
                cached_data = self.price_cache[cache_key]
                if time.time() - cached_data.timestamp < self.cache_ttl:
                    return cached_data
            
            # Récupérer le prix (simulation pour l'exemple)
            price_data = await self._fetch_token_price(chain, token_symbol, dex)
            
            if price_data:
                self.price_cache[cache_key] = price_data
            
            return price_data
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.DATA, ErrorSeverity.MEDIUM, {
                'function': 'get_token_price',
                'chain': chain,
                'token': token_symbol
            })
            return None
    
    async def _fetch_token_price(self, chain: str, token_symbol: str, 
                                dex: str = None) -> Optional[PriceData]:
        """Récupère le prix réel d'un token"""
        try:
            # Simulation de récupération de prix
            # En production, on interrogerait les DEX ou des APIs comme CoinGecko
            
            chain_config = self.chains.get(chain)
            if not chain_config:
                return None
            
            token_address = chain_config.stablecoin_addresses.get(token_symbol)
            if not token_address:
                return None
            
            # Simuler des prix légèrement différents selon la chaîne
            base_price = 1.0  # Prix de base pour les stablecoins
            
            # Ajouter de la variance selon la chaîne
            price_variance = {
                'ethereum': 0.0,      # Prix de référence
                'bsc': 0.001,         # +0.1%
                'polygon': -0.0005,   # -0.05%
                'arbitrum': 0.0002    # +0.02%
            }
            
            variance = price_variance.get(chain, 0.0)
            simulated_price = base_price + variance
            
            # Simuler la liquidité selon la chaîne
            liquidity_multipliers = {
                'ethereum': 1.0,
                'bsc': 0.6,
                'polygon': 0.4,
                'arbitrum': 0.3
            }
            
            base_liquidity = 10000000  # 10M USD
            liquidity = base_liquidity * liquidity_multipliers.get(chain, 0.5)
            
            token_info = TokenInfo(
                address=token_address,
                symbol=token_symbol,
                decimals=6 if token_symbol == 'USDC' else 18,
                chain=chain
            )
            
            return PriceData(
                token=token_info,
                price_usd=simulated_price,
                liquidity_usd=liquidity,
                volume_24h=liquidity * 0.1,  # 10% de la liquidité
                timestamp=time.time(),
                source=dex or 'aggregated'
            )
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.DATA, ErrorSeverity.MEDIUM, {
                'function': '_fetch_token_price',
                'chain': chain,
                'token': token_symbol
            })
            return None
    
    async def get_bridge_quote(self, from_chain: str, to_chain: str, 
                             token_symbol: str, amount: float) -> Optional[Dict[str, Any]]:
        """Obtient une cotation pour un bridge"""
        try:
            # Trouver la route de bridge
            route = self._find_bridge_route(from_chain, to_chain, token_symbol)
            if not route:
                return None
            
            # Vérifier les limites
            if amount < route.min_amount or amount > route.max_amount:
                return None
            
            # Calculer les frais
            bridge_fee = amount * (route.fee_percentage / 100)
            
            # Estimer les frais de gas
            gas_fee_usd = await self._estimate_bridge_gas_fee(from_chain, to_chain)
            
            # Montant net reçu
            net_amount = amount - bridge_fee
            
            return {
                'from_chain': from_chain,
                'to_chain': to_chain,
                'token_symbol': token_symbol,
                'input_amount': amount,
                'output_amount': net_amount,
                'bridge_fee': bridge_fee,
                'gas_fee_usd': gas_fee_usd,
                'total_fee_usd': bridge_fee + gas_fee_usd,
                'estimated_time_minutes': route.estimated_time_minutes,
                'bridge_name': route.bridge_name,
                'route': route
            }
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.MEDIUM, {
                'function': 'get_bridge_quote',
                'from_chain': from_chain,
                'to_chain': to_chain
            })
            return None
    
    def _find_bridge_route(self, from_chain: str, to_chain: str, 
                          token_symbol: str) -> Optional[BridgeRoute]:
        """Trouve la meilleure route de bridge"""
        matching_routes = [
            route for route in self.bridge_routes
            if (route.from_chain == from_chain and 
                route.to_chain == to_chain and 
                route.token_symbol == token_symbol)
        ]
        
        if not matching_routes:
            return None
        
        # Retourner la route avec les frais les plus bas
        return min(matching_routes, key=lambda r: r.fee_percentage)
    
    async def _estimate_bridge_gas_fee(self, from_chain: str, to_chain: str) -> float:
        """Estime les frais de gas pour un bridge"""
        try:
            from_config = self.chains.get(from_chain)
            if not from_config:
                return 0.0
            
            # Estimation basique des frais de gas
            gas_estimates = {
                'ethereum': 150000,  # Gas units
                'bsc': 100000,
                'polygon': 80000,
                'arbitrum': 200000
            }
            
            gas_units = gas_estimates.get(from_chain, 100000)
            gas_price_wei = from_config.gas_price_gwei * 10**9
            gas_cost_native = (gas_units * gas_price_wei) / 10**18
            
            # Convertir en USD (prix simulé)
            native_token_prices = {
                'ETH': 2000,
                'BNB': 300,
                'MATIC': 0.8
            }
            
            native_price = native_token_prices.get(from_config.native_token, 100)
            gas_cost_usd = gas_cost_native * native_price
            
            return gas_cost_usd
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.LOW, {
                'function': '_estimate_bridge_gas_fee',
                'from_chain': from_chain
            })
            return 10.0  # Fallback
    
    async def execute_bridge(self, quote: Dict[str, Any]) -> Optional[str]:
        """Exécute un bridge (simulation)"""
        try:
            # En production, ceci interagirait avec les vrais contrats de bridge
            
            central_logger.log(
                level="INFO",
                message="Bridge exécuté (simulation)",
                category=LogCategory.TRADING,
                from_chain=quote['from_chain'],
                to_chain=quote['to_chain'],
                amount=quote['input_amount'],
                bridge_name=quote['bridge_name']
            )
            
            # Simuler un hash de transaction
            import hashlib
            tx_data = f"{quote['from_chain']}{quote['to_chain']}{time.time()}"
            tx_hash = hashlib.sha256(tx_data.encode()).hexdigest()
            
            return f"0x{tx_hash[:64]}"
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.HIGH, {
                'function': 'execute_bridge',
                'quote': quote
            })
            return None
    
    async def get_all_stablecoin_prices(self) -> Dict[str, Dict[str, PriceData]]:
        """Récupère les prix de tous les stablecoins sur toutes les chaînes"""
        all_prices = {}
        
        for chain_name in self.chains.keys():
            chain_prices = {}
            
            for token_symbol in ['USDT', 'USDC', 'DAI', 'BUSD']:
                price_data = await self.get_token_price(chain_name, token_symbol)
                if price_data:
                    chain_prices[token_symbol] = price_data
            
            if chain_prices:
                all_prices[chain_name] = chain_prices
        
        return all_prices
    
    def get_supported_chains(self) -> List[str]:
        """Retourne la liste des chaînes supportées"""
        return list(self.chains.keys())
    
    def get_supported_tokens(self, chain: str) -> List[str]:
        """Retourne la liste des tokens supportés sur une chaîne"""
        chain_config = self.chains.get(chain)
        if not chain_config:
            return []
        
        return list(chain_config.stablecoin_addresses.keys())
    
    def get_chain_info(self, chain: str) -> Optional[ChainConfig]:
        """Retourne les informations d'une chaîne"""
        return self.chains.get(chain)
    
    async def check_chain_health(self, chain: str) -> Dict[str, Any]:
        """Vérifie la santé d'une chaîne"""
        try:
            w3 = self.web3_connections.get(chain)
            if not w3:
                return {'healthy': False, 'error': 'No connection'}
            
            # Simulation de vérification de santé
            # En production: vérifier la connectivité, le dernier bloc, etc.
            
            chain_config = self.chains[chain]
            
            return {
                'healthy': True,
                'chain_name': chain_config.name,
                'chain_id': chain_config.chain_id,
                'latest_block': 18000000,  # Simulé
                'gas_price_gwei': chain_config.gas_price_gwei,
                'response_time_ms': 150,  # Simulé
                'last_check': time.time()
            }
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.NETWORK, ErrorSeverity.MEDIUM, {
                'function': 'check_chain_health',
                'chain': chain
            })
            return {'healthy': False, 'error': str(e)}
