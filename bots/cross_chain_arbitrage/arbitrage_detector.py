"""
🔍 Détecteur d'opportunités d'arbitrage cross-chain
Analyse les écarts de prix entre chaînes et calcule la rentabilité
"""

import asyncio
import time
import numpy as np
from decimal import Decimal, ROUND_DOWN
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import defaultdict
import logging
from pathlib import Path
import sys

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent.parent))

from bots.cross_chain_arbitrage.chain_connector import ChainConnector, PriceData, BridgeRoute
from utils.error_handler import error_handler, ErrorCategory, ErrorSeverity
from logging_system.central_logger import central_logger, LogCategory

@dataclass
class ArbitrageOpportunity:
    """Opportunité d'arbitrage détectée"""
    id: str
    timestamp: datetime
    
    # Chaînes et token
    buy_chain: str
    sell_chain: str
    token_symbol: str
    
    # Prix
    buy_price: float
    sell_price: float
    price_difference: float
    price_difference_pct: float
    
    # Montants
    optimal_amount: float
    max_amount: float
    min_amount: float
    
    # Rentabilité
    gross_profit: float
    bridge_fees: float
    gas_fees: float
    net_profit: float
    roi_percentage: float
    
    # Timing
    execution_time_minutes: int
    urgency_score: float  # 0-1, 1 = très urgent
    
    # Métadonnées
    liquidity_buy: float
    liquidity_sell: float
    confidence_score: float
    risk_level: str
    
    # Routes
    bridge_quote: Optional[Dict[str, Any]] = None

@dataclass
class ArbitrageConfig:
    """Configuration du détecteur d'arbitrage"""
    # Seuils de rentabilité
    min_profit_usd: float = 10.0
    min_roi_percentage: float = 0.5  # 0.5%
    
    # Limites de montant
    min_trade_amount: float = 100.0
    max_trade_amount: float = 50000.0
    
    # Filtres de qualité
    min_liquidity_usd: float = 10000.0
    min_confidence_score: float = 0.7
    max_execution_time_minutes: int = 60
    
    # Tokens à surveiller
    monitored_tokens: List[str] = field(default_factory=lambda: ['USDT', 'USDC', 'DAI', 'BUSD'])
    
    # Chaînes à surveiller
    monitored_chains: List[str] = field(default_factory=lambda: ['ethereum', 'bsc', 'polygon', 'arbitrum'])

class ArbitrageDetector:
    """Détecteur d'opportunités d'arbitrage cross-chain"""
    
    def __init__(self, connector: ChainConnector, config: ArbitrageConfig = None):
        self.logger = logging.getLogger(__name__)
        self.connector = connector
        self.config = config or ArbitrageConfig()
        
        # Historique des prix
        self.price_history: Dict[str, List[PriceData]] = defaultdict(list)
        self.max_history_length = 100
        
        # Opportunités détectées
        self.current_opportunities: Dict[str, ArbitrageOpportunity] = {}
        self.opportunity_history: List[ArbitrageOpportunity] = []
        
        # Statistiques
        self.total_opportunities_found = 0
        self.total_opportunities_executed = 0
        self.total_profit_realized = 0.0
        
        # Cache des calculs
        self.calculation_cache: Dict[str, Any] = {}
        self.cache_ttl = 10  # 10 secondes
        
        central_logger.log(
            level="INFO",
            message="Détecteur d'arbitrage initialisé",
            category=LogCategory.STRATEGY,
            monitored_tokens=len(self.config.monitored_tokens),
            monitored_chains=len(self.config.monitored_chains)
        )
    
    async def scan_opportunities(self) -> List[ArbitrageOpportunity]:
        """Scanne toutes les opportunités d'arbitrage disponibles"""
        try:
            opportunities = []
            
            # Récupérer tous les prix
            all_prices = await self.connector.get_all_stablecoin_prices()
            
            # Mettre à jour l'historique des prix
            self._update_price_history(all_prices)
            
            # Analyser chaque paire de chaînes pour chaque token
            for token in self.config.monitored_tokens:
                token_opportunities = await self._analyze_token_arbitrage(token, all_prices)
                opportunities.extend(token_opportunities)
            
            # Filtrer et trier les opportunités
            filtered_opportunities = self._filter_opportunities(opportunities)
            sorted_opportunities = self._sort_opportunities(filtered_opportunities)
            
            # Mettre à jour le cache des opportunités
            self._update_opportunities_cache(sorted_opportunities)
            
            central_logger.log(
                level="INFO",
                message=f"Scan terminé: {len(sorted_opportunities)} opportunités trouvées",
                category=LogCategory.STRATEGY,
                total_found=len(opportunities),
                after_filter=len(sorted_opportunities)
            )
            
            return sorted_opportunities
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.MEDIUM, {
                'function': 'scan_opportunities'
            })
            return []
    
    def _update_price_history(self, all_prices: Dict[str, Dict[str, PriceData]]):
        """Met à jour l'historique des prix"""
        for chain, chain_prices in all_prices.items():
            for token, price_data in chain_prices.items():
                key = f"{chain}_{token}"
                
                # Ajouter le nouveau prix
                self.price_history[key].append(price_data)
                
                # Limiter la taille de l'historique
                if len(self.price_history[key]) > self.max_history_length:
                    self.price_history[key] = self.price_history[key][-self.max_history_length:]
    
    async def _analyze_token_arbitrage(self, token: str, 
                                     all_prices: Dict[str, Dict[str, PriceData]]) -> List[ArbitrageOpportunity]:
        """Analyse les opportunités d'arbitrage pour un token donné"""
        opportunities = []
        
        # Récupérer les prix du token sur toutes les chaînes
        token_prices = {}
        for chain in self.config.monitored_chains:
            if chain in all_prices and token in all_prices[chain]:
                token_prices[chain] = all_prices[chain][token]
        
        if len(token_prices) < 2:
            return opportunities
        
        # Analyser chaque paire de chaînes
        chains = list(token_prices.keys())
        for i, buy_chain in enumerate(chains):
            for sell_chain in chains[i+1:]:
                # Analyser dans les deux directions
                opp1 = await self._analyze_chain_pair(
                    token, buy_chain, sell_chain, token_prices
                )
                if opp1:
                    opportunities.append(opp1)
                
                opp2 = await self._analyze_chain_pair(
                    token, sell_chain, buy_chain, token_prices
                )
                if opp2:
                    opportunities.append(opp2)
        
        return opportunities
    
    async def _analyze_chain_pair(self, token: str, buy_chain: str, sell_chain: str,
                                token_prices: Dict[str, PriceData]) -> Optional[ArbitrageOpportunity]:
        """Analyse une paire de chaînes pour un token"""
        try:
            buy_price_data = token_prices[buy_chain]
            sell_price_data = token_prices[sell_chain]
            
            # Vérifier s'il y a un écart de prix favorable
            price_diff = sell_price_data.price_usd - buy_price_data.price_usd
            if price_diff <= 0:
                return None
            
            price_diff_pct = (price_diff / buy_price_data.price_usd) * 100
            
            # Vérifier le seuil minimum
            if price_diff_pct < self.config.min_roi_percentage:
                return None
            
            # Calculer le montant optimal
            optimal_amount = self._calculate_optimal_amount(
                buy_price_data, sell_price_data, price_diff_pct
            )
            
            if optimal_amount < self.config.min_trade_amount:
                return None
            
            # Obtenir une cotation de bridge
            bridge_quote = await self.connector.get_bridge_quote(
                buy_chain, sell_chain, token, optimal_amount
            )
            
            if not bridge_quote:
                return None
            
            # Calculer la rentabilité
            profitability = self._calculate_profitability(
                optimal_amount, price_diff, bridge_quote
            )
            
            if profitability['net_profit'] < self.config.min_profit_usd:
                return None
            
            # Calculer les scores de confiance et d'urgence
            confidence_score = self._calculate_confidence_score(
                buy_price_data, sell_price_data, price_diff_pct
            )
            
            urgency_score = self._calculate_urgency_score(
                price_diff_pct, confidence_score, bridge_quote['estimated_time_minutes']
            )
            
            # Créer l'opportunité
            opportunity_id = f"{token}_{buy_chain}_{sell_chain}_{int(time.time())}"
            
            opportunity = ArbitrageOpportunity(
                id=opportunity_id,
                timestamp=datetime.now(),
                buy_chain=buy_chain,
                sell_chain=sell_chain,
                token_symbol=token,
                buy_price=buy_price_data.price_usd,
                sell_price=sell_price_data.price_usd,
                price_difference=price_diff,
                price_difference_pct=price_diff_pct,
                optimal_amount=optimal_amount,
                max_amount=min(optimal_amount * 2, self.config.max_trade_amount),
                min_amount=max(optimal_amount * 0.5, self.config.min_trade_amount),
                gross_profit=profitability['gross_profit'],
                bridge_fees=profitability['bridge_fees'],
                gas_fees=profitability['gas_fees'],
                net_profit=profitability['net_profit'],
                roi_percentage=profitability['roi_percentage'],
                execution_time_minutes=bridge_quote['estimated_time_minutes'],
                urgency_score=urgency_score,
                liquidity_buy=buy_price_data.liquidity_usd,
                liquidity_sell=sell_price_data.liquidity_usd,
                confidence_score=confidence_score,
                risk_level=self._assess_risk_level(confidence_score, bridge_quote),
                bridge_quote=bridge_quote
            )
            
            return opportunity
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.MEDIUM, {
                'function': '_analyze_chain_pair',
                'token': token,
                'buy_chain': buy_chain,
                'sell_chain': sell_chain
            })
            return None
    
    def _calculate_optimal_amount(self, buy_price_data: PriceData, 
                                sell_price_data: PriceData, price_diff_pct: float) -> float:
        """Calcule le montant optimal pour l'arbitrage"""
        # Limiter par la liquidité disponible
        max_by_buy_liquidity = buy_price_data.liquidity_usd * 0.05  # 5% max
        max_by_sell_liquidity = sell_price_data.liquidity_usd * 0.05
        
        liquidity_limit = min(max_by_buy_liquidity, max_by_sell_liquidity)
        
        # Ajuster selon l'écart de prix (plus l'écart est grand, plus on peut trader)
        price_factor = min(2.0, 1.0 + (price_diff_pct / 2.0))
        
        optimal = min(
            liquidity_limit * price_factor,
            self.config.max_trade_amount
        )
        
        return max(optimal, self.config.min_trade_amount)
    
    def _calculate_profitability(self, amount: float, price_diff: float, 
                               bridge_quote: Dict[str, Any]) -> Dict[str, float]:
        """Calcule la rentabilité détaillée"""
        # Profit brut
        gross_profit = amount * price_diff
        
        # Frais de bridge
        bridge_fees = bridge_quote['total_fee_usd']
        
        # Frais de gas (estimation pour les trades sur les DEX)
        estimated_dex_gas = 20.0  # USD, estimation
        total_gas_fees = bridge_quote['gas_fee_usd'] + estimated_dex_gas
        
        # Profit net
        net_profit = gross_profit - bridge_fees - total_gas_fees
        
        # ROI
        total_cost = amount + bridge_fees + total_gas_fees
        roi_percentage = (net_profit / total_cost) * 100 if total_cost > 0 else 0
        
        return {
            'gross_profit': gross_profit,
            'bridge_fees': bridge_fees,
            'gas_fees': total_gas_fees,
            'net_profit': net_profit,
            'roi_percentage': roi_percentage
        }
    
    def _calculate_confidence_score(self, buy_price_data: PriceData, 
                                  sell_price_data: PriceData, price_diff_pct: float) -> float:
        """Calcule un score de confiance pour l'opportunité"""
        score = 1.0
        
        # Pénaliser si liquidité faible
        min_liquidity = min(buy_price_data.liquidity_usd, sell_price_data.liquidity_usd)
        if min_liquidity < 50000:  # Moins de 50k USD
            score *= 0.7
        elif min_liquidity < 100000:  # Moins de 100k USD
            score *= 0.85
        
        # Pénaliser si écart de prix trop important (peut être une erreur)
        if price_diff_pct > 5.0:  # Plus de 5%
            score *= 0.6
        elif price_diff_pct > 2.0:  # Plus de 2%
            score *= 0.8
        
        # Bonus pour volume élevé
        min_volume = min(buy_price_data.volume_24h, sell_price_data.volume_24h)
        if min_volume > 1000000:  # Plus de 1M USD de volume
            score *= 1.1
        
        # Vérifier la fraîcheur des données
        current_time = time.time()
        max_age = max(
            current_time - buy_price_data.timestamp,
            current_time - sell_price_data.timestamp
        )
        
        if max_age > 60:  # Plus de 1 minute
            score *= 0.9
        elif max_age > 30:  # Plus de 30 secondes
            score *= 0.95
        
        return min(1.0, max(0.0, score))
    
    def _calculate_urgency_score(self, price_diff_pct: float, confidence_score: float,
                               execution_time_minutes: int) -> float:
        """Calcule un score d'urgence"""
        # Base sur l'écart de prix
        urgency = min(1.0, price_diff_pct / 5.0)  # Normaliser sur 5%
        
        # Ajuster selon la confiance
        urgency *= confidence_score
        
        # Pénaliser les longs temps d'exécution
        if execution_time_minutes > 30:
            urgency *= 0.5
        elif execution_time_minutes > 15:
            urgency *= 0.7
        elif execution_time_minutes > 5:
            urgency *= 0.9
        
        return min(1.0, max(0.0, urgency))
    
    def _assess_risk_level(self, confidence_score: float, 
                         bridge_quote: Dict[str, Any]) -> str:
        """Évalue le niveau de risque"""
        if confidence_score >= 0.8 and bridge_quote['estimated_time_minutes'] <= 15:
            return 'LOW'
        elif confidence_score >= 0.6 and bridge_quote['estimated_time_minutes'] <= 30:
            return 'MEDIUM'
        else:
            return 'HIGH'
    
    def _filter_opportunities(self, opportunities: List[ArbitrageOpportunity]) -> List[ArbitrageOpportunity]:
        """Filtre les opportunités selon les critères de qualité"""
        filtered = []
        
        for opp in opportunities:
            # Filtres de base
            if (opp.net_profit >= self.config.min_profit_usd and
                opp.roi_percentage >= self.config.min_roi_percentage and
                opp.confidence_score >= self.config.min_confidence_score and
                opp.execution_time_minutes <= self.config.max_execution_time_minutes and
                min(opp.liquidity_buy, opp.liquidity_sell) >= self.config.min_liquidity_usd):
                
                filtered.append(opp)
        
        return filtered
    
    def _sort_opportunities(self, opportunities: List[ArbitrageOpportunity]) -> List[ArbitrageOpportunity]:
        """Trie les opportunités par attractivité"""
        # Score composite: ROI * confiance * urgence
        def score_function(opp):
            return opp.roi_percentage * opp.confidence_score * opp.urgency_score
        
        return sorted(opportunities, key=score_function, reverse=True)
    
    def _update_opportunities_cache(self, opportunities: List[ArbitrageOpportunity]):
        """Met à jour le cache des opportunités"""
        # Nettoyer les anciennes opportunités
        self.current_opportunities.clear()
        
        # Ajouter les nouvelles
        for opp in opportunities:
            self.current_opportunities[opp.id] = opp
        
        # Ajouter à l'historique
        self.opportunity_history.extend(opportunities)
        
        # Limiter l'historique
        if len(self.opportunity_history) > 1000:
            self.opportunity_history = self.opportunity_history[-500:]
        
        # Mettre à jour les statistiques
        self.total_opportunities_found += len(opportunities)
    
    def get_best_opportunities(self, limit: int = 5) -> List[ArbitrageOpportunity]:
        """Retourne les meilleures opportunités"""
        opportunities = list(self.current_opportunities.values())
        sorted_opportunities = self._sort_opportunities(opportunities)
        return sorted_opportunities[:limit]
    
    def get_opportunity_by_id(self, opportunity_id: str) -> Optional[ArbitrageOpportunity]:
        """Récupère une opportunité par son ID"""
        return self.current_opportunities.get(opportunity_id)
    
    def get_statistics(self) -> Dict[str, Any]:
        """Retourne les statistiques du détecteur"""
        current_count = len(self.current_opportunities)
        
        if self.opportunity_history:
            avg_roi = np.mean([opp.roi_percentage for opp in self.opportunity_history[-100:]])
            avg_profit = np.mean([opp.net_profit for opp in self.opportunity_history[-100:]])
        else:
            avg_roi = 0.0
            avg_profit = 0.0
        
        return {
            'total_opportunities_found': self.total_opportunities_found,
            'current_opportunities': current_count,
            'opportunities_executed': self.total_opportunities_executed,
            'total_profit_realized': self.total_profit_realized,
            'average_roi_percentage': avg_roi,
            'average_profit_usd': avg_profit,
            'success_rate': (self.total_opportunities_executed / max(1, self.total_opportunities_found)) * 100
        }
    
    async def monitor_opportunity(self, opportunity_id: str, 
                                duration_seconds: int = 300) -> Dict[str, Any]:
        """Surveille une opportunité pendant une durée donnée"""
        try:
            start_time = time.time()
            monitoring_data = []
            
            while time.time() - start_time < duration_seconds:
                # Re-scanner pour cette opportunité spécifique
                current_opportunities = await self.scan_opportunities()
                
                # Trouver l'opportunité mise à jour
                updated_opp = None
                for opp in current_opportunities:
                    if (opp.token_symbol == opportunity_id.split('_')[0] and
                        opp.buy_chain == opportunity_id.split('_')[1] and
                        opp.sell_chain == opportunity_id.split('_')[2]):
                        updated_opp = opp
                        break
                
                monitoring_data.append({
                    'timestamp': time.time(),
                    'opportunity_exists': updated_opp is not None,
                    'roi_percentage': updated_opp.roi_percentage if updated_opp else 0,
                    'net_profit': updated_opp.net_profit if updated_opp else 0,
                    'urgency_score': updated_opp.urgency_score if updated_opp else 0
                })
                
                await asyncio.sleep(10)  # Vérifier toutes les 10 secondes
            
            return {
                'opportunity_id': opportunity_id,
                'monitoring_duration': duration_seconds,
                'data_points': len(monitoring_data),
                'monitoring_data': monitoring_data,
                'opportunity_stable': len([d for d in monitoring_data if d['opportunity_exists']]) > len(monitoring_data) * 0.8
            }
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.MEDIUM, {
                'function': 'monitor_opportunity',
                'opportunity_id': opportunity_id
            })
            return {'error': str(e)}
