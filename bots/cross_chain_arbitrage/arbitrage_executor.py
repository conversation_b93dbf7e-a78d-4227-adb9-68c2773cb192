"""
⚡ Exécuteur d'arbitrage cross-chain
Gestion de l'exécution des opportunités d'arbitrage avec gestion des risques
"""

import asyncio
import time
import uuid
from decimal import Decimal, ROUND_DOWN
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from enum import Enum
import logging
from pathlib import Path
import sys

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent.parent))

from bots.cross_chain_arbitrage.chain_connector import ChainConnector
from bots.cross_chain_arbitrage.arbitrage_detector import ArbitrageOpportunity, ArbitrageDetector
from utils.error_handler import error_handler, ErrorCategory, ErrorSeverity
from logging_system.central_logger import central_logger, LogCategory
from risk_management.portfolio_manager import portfolio_manager

class ExecutionStatus(Enum):
    """Statuts d'exécution"""
    PENDING = "PENDING"
    BUYING = "BUYING"
    BRIDGING = "BRIDGING"
    SELLING = "SELLING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    CANCELLED = "CANCELLED"

@dataclass
class ExecutionStep:
    """Étape d'exécution"""
    step_name: str
    status: ExecutionStatus
    transaction_hash: Optional[str]
    start_time: Optional[datetime]
    end_time: Optional[datetime]
    gas_used: Optional[int]
    error_message: Optional[str]

@dataclass
class ArbitrageExecution:
    """Exécution d'un arbitrage"""
    id: str
    opportunity: ArbitrageOpportunity
    status: ExecutionStatus
    start_time: datetime
    end_time: Optional[datetime]
    
    # Étapes d'exécution
    steps: List[ExecutionStep]
    
    # Résultats financiers
    actual_buy_price: Optional[float]
    actual_sell_price: Optional[float]
    actual_amount: Optional[float]
    total_fees_paid: Optional[float]
    realized_profit: Optional[float]
    realized_roi: Optional[float]
    
    # Métadonnées
    slippage_buy: Optional[float]
    slippage_sell: Optional[float]
    execution_time_seconds: Optional[float]

@dataclass
class ExecutionConfig:
    """Configuration de l'exécuteur"""
    # Limites de risque
    max_slippage_percentage: float = 2.0
    max_execution_time_minutes: int = 30
    max_concurrent_executions: int = 3
    
    # Gestion du capital
    max_capital_per_trade: float = 10000.0
    reserve_capital_percentage: float = 20.0  # 20% en réserve
    
    # Seuils d'exécution
    min_confidence_score: float = 0.8
    min_roi_percentage: float = 1.0
    
    # Timeouts
    transaction_timeout_seconds: int = 300
    bridge_timeout_minutes: int = 60

class ArbitrageExecutor:
    """Exécuteur d'arbitrage cross-chain"""
    
    def __init__(self, connector: ChainConnector, detector: ArbitrageDetector, 
                 config: ExecutionConfig = None):
        self.logger = logging.getLogger(__name__)
        self.connector = connector
        self.detector = detector
        self.config = config or ExecutionConfig()
        
        # Exécutions en cours
        self.active_executions: Dict[str, ArbitrageExecution] = {}
        self.execution_history: List[ArbitrageExecution] = []
        
        # Statistiques
        self.total_executions = 0
        self.successful_executions = 0
        self.total_profit = 0.0
        self.total_fees_paid = 0.0
        
        # Gestion des ressources
        self.capital_allocated = 0.0
        self.max_capital = 0.0
        
        central_logger.log(
            level="INFO",
            message="Exécuteur d'arbitrage initialisé",
            category=LogCategory.STRATEGY,
            max_concurrent=self.config.max_concurrent_executions
        )
    
    async def execute_opportunity(self, opportunity: ArbitrageOpportunity,
                                amount: Optional[float] = None) -> Optional[ArbitrageExecution]:
        """Exécute une opportunité d'arbitrage"""
        try:
            # Vérifier les conditions préalables
            if not self._can_execute(opportunity):
                return None
            
            # Déterminer le montant à trader
            trade_amount = amount or opportunity.optimal_amount
            trade_amount = min(trade_amount, self.config.max_capital_per_trade)
            
            # Créer l'exécution
            execution_id = str(uuid.uuid4())
            execution = ArbitrageExecution(
                id=execution_id,
                opportunity=opportunity,
                status=ExecutionStatus.PENDING,
                start_time=datetime.now(),
                end_time=None,
                steps=[],
                actual_buy_price=None,
                actual_sell_price=None,
                actual_amount=trade_amount,
                total_fees_paid=None,
                realized_profit=None,
                realized_roi=None,
                slippage_buy=None,
                slippage_sell=None,
                execution_time_seconds=None
            )
            
            # Ajouter aux exécutions actives
            self.active_executions[execution_id] = execution
            self.capital_allocated += trade_amount
            
            central_logger.log(
                level="INFO",
                message="Démarrage exécution arbitrage",
                category=LogCategory.TRADING,
                execution_id=execution_id,
                token=opportunity.token_symbol,
                buy_chain=opportunity.buy_chain,
                sell_chain=opportunity.sell_chain,
                amount=trade_amount
            )
            
            # Exécuter les étapes
            success = await self._execute_arbitrage_steps(execution)
            
            if success:
                execution.status = ExecutionStatus.COMPLETED
                self.successful_executions += 1
                
                central_logger.log(
                    level="INFO",
                    message="Arbitrage exécuté avec succès",
                    category=LogCategory.TRADING,
                    execution_id=execution_id,
                    realized_profit=execution.realized_profit,
                    realized_roi=execution.realized_roi
                )
            else:
                execution.status = ExecutionStatus.FAILED
                
                central_logger.log(
                    level="ERROR",
                    message="Échec de l'exécution d'arbitrage",
                    category=LogCategory.TRADING,
                    execution_id=execution_id
                )
            
            # Finaliser l'exécution
            execution.end_time = datetime.now()
            execution.execution_time_seconds = (
                execution.end_time - execution.start_time
            ).total_seconds()
            
            # Nettoyer et archiver
            self._finalize_execution(execution)
            
            return execution
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.HIGH, {
                'function': 'execute_opportunity',
                'opportunity_id': opportunity.id
            })
            return None
    
    def _can_execute(self, opportunity: ArbitrageOpportunity) -> bool:
        """Vérifie si une opportunité peut être exécutée"""
        # Vérifier les limites de concurrent
        if len(self.active_executions) >= self.config.max_concurrent_executions:
            return False
        
        # Vérifier la confiance
        if opportunity.confidence_score < self.config.min_confidence_score:
            return False
        
        # Vérifier le ROI
        if opportunity.roi_percentage < self.config.min_roi_percentage:
            return False
        
        # Vérifier le capital disponible
        available_capital = self._get_available_capital()
        if opportunity.optimal_amount > available_capital:
            return False
        
        # Vérifier que l'opportunité est encore valide
        if self._is_opportunity_stale(opportunity):
            return False
        
        return True
    
    def _get_available_capital(self) -> float:
        """Calcule le capital disponible pour trading"""
        total_portfolio = portfolio_manager.get_portfolio_value()
        
        # Garder une réserve
        reserve = total_portfolio * (self.config.reserve_capital_percentage / 100)
        
        # Capital disponible = total - réserve - capital déjà alloué
        available = total_portfolio - reserve - self.capital_allocated
        
        return max(0, available)
    
    def _is_opportunity_stale(self, opportunity: ArbitrageOpportunity) -> bool:
        """Vérifie si une opportunité est périmée"""
        age_seconds = (datetime.now() - opportunity.timestamp).total_seconds()
        return age_seconds > 60  # Plus de 1 minute
    
    async def _execute_arbitrage_steps(self, execution: ArbitrageExecution) -> bool:
        """Exécute les étapes de l'arbitrage"""
        try:
            opportunity = execution.opportunity
            
            # Étape 1: Acheter sur la chaîne source
            buy_step = await self._execute_buy_step(execution)
            if not buy_step:
                return False
            
            # Étape 2: Bridge vers la chaîne de destination
            bridge_step = await self._execute_bridge_step(execution)
            if not bridge_step:
                return False
            
            # Étape 3: Vendre sur la chaîne de destination
            sell_step = await self._execute_sell_step(execution)
            if not sell_step:
                return False
            
            # Calculer les résultats finaux
            self._calculate_final_results(execution)
            
            return True
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.HIGH, {
                'function': '_execute_arbitrage_steps',
                'execution_id': execution.id
            })
            return False
    
    async def _execute_buy_step(self, execution: ArbitrageExecution) -> bool:
        """Exécute l'étape d'achat"""
        try:
            execution.status = ExecutionStatus.BUYING
            
            step = ExecutionStep(
                step_name="buy",
                status=ExecutionStatus.PENDING,
                transaction_hash=None,
                start_time=datetime.now(),
                end_time=None,
                gas_used=None,
                error_message=None
            )
            execution.steps.append(step)
            
            opportunity = execution.opportunity
            
            # Simuler l'achat (en production, utiliser les vrais DEX)
            await asyncio.sleep(2)  # Simuler le temps de transaction
            
            # Simuler un hash de transaction
            import hashlib
            tx_data = f"buy_{execution.id}_{time.time()}"
            tx_hash = f"0x{hashlib.sha256(tx_data.encode()).hexdigest()[:64]}"
            
            # Simuler un léger slippage
            slippage = 0.001  # 0.1%
            actual_price = opportunity.buy_price * (1 + slippage)
            
            # Mettre à jour l'exécution
            execution.actual_buy_price = actual_price
            execution.slippage_buy = slippage
            
            # Mettre à jour l'étape
            step.status = ExecutionStatus.COMPLETED
            step.transaction_hash = tx_hash
            step.end_time = datetime.now()
            step.gas_used = 150000  # Simulé
            
            central_logger.trade_executed(
                message=f"Achat exécuté: {opportunity.token_symbol}",
                symbol=f"{opportunity.token_symbol}/{opportunity.buy_chain}",
                side="buy",
                quantity=execution.actual_amount,
                price=actual_price,
                trade_id=tx_hash
            )
            
            return True
            
        except Exception as e:
            step.status = ExecutionStatus.FAILED
            step.error_message = str(e)
            step.end_time = datetime.now()
            
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.HIGH, {
                'function': '_execute_buy_step',
                'execution_id': execution.id
            })
            return False
    
    async def _execute_bridge_step(self, execution: ArbitrageExecution) -> bool:
        """Exécute l'étape de bridge"""
        try:
            execution.status = ExecutionStatus.BRIDGING
            
            step = ExecutionStep(
                step_name="bridge",
                status=ExecutionStatus.PENDING,
                start_time=datetime.now(),
                end_time=None,
                transaction_hash=None,
                gas_used=None,
                error_message=None
            )
            execution.steps.append(step)
            
            opportunity = execution.opportunity
            
            # Exécuter le bridge
            bridge_quote = opportunity.bridge_quote
            if not bridge_quote:
                # Obtenir une nouvelle cotation
                bridge_quote = await self.connector.get_bridge_quote(
                    opportunity.buy_chain,
                    opportunity.sell_chain,
                    opportunity.token_symbol,
                    execution.actual_amount
                )
            
            if not bridge_quote:
                raise Exception("Impossible d'obtenir une cotation de bridge")
            
            # Exécuter le bridge
            tx_hash = await self.connector.execute_bridge(bridge_quote)
            
            if not tx_hash:
                raise Exception("Échec de l'exécution du bridge")
            
            # Attendre la confirmation (simulation)
            await asyncio.sleep(min(bridge_quote['estimated_time_minutes'] * 0.1, 5))  # Simuler le temps (réduit pour demo)
            
            # Mettre à jour l'étape
            step.status = ExecutionStatus.COMPLETED
            step.transaction_hash = tx_hash
            step.end_time = datetime.now()
            step.gas_used = 200000  # Simulé
            
            central_logger.log(
                level="INFO",
                message="Bridge exécuté avec succès",
                category=LogCategory.TRADING,
                execution_id=execution.id,
                from_chain=opportunity.buy_chain,
                to_chain=opportunity.sell_chain,
                tx_hash=tx_hash
            )
            
            return True
            
        except Exception as e:
            step.status = ExecutionStatus.FAILED
            step.error_message = str(e)
            step.end_time = datetime.now()
            
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.HIGH, {
                'function': '_execute_bridge_step',
                'execution_id': execution.id
            })
            return False
    
    async def _execute_sell_step(self, execution: ArbitrageExecution) -> bool:
        """Exécute l'étape de vente"""
        try:
            execution.status = ExecutionStatus.SELLING
            
            step = ExecutionStep(
                step_name="sell",
                status=ExecutionStatus.PENDING,
                start_time=datetime.now(),
                end_time=None,
                transaction_hash=None,
                gas_used=None,
                error_message=None
            )
            execution.steps.append(step)
            
            opportunity = execution.opportunity
            
            # Simuler la vente
            await asyncio.sleep(2)  # Simuler le temps de transaction
            
            # Simuler un hash de transaction
            import hashlib
            tx_data = f"sell_{execution.id}_{time.time()}"
            tx_hash = f"0x{hashlib.sha256(tx_data.encode()).hexdigest()[:64]}"
            
            # Simuler un léger slippage
            slippage = 0.0015  # 0.15%
            actual_price = opportunity.sell_price * (1 - slippage)
            
            # Mettre à jour l'exécution
            execution.actual_sell_price = actual_price
            execution.slippage_sell = slippage
            
            # Mettre à jour l'étape
            step.status = ExecutionStatus.COMPLETED
            step.transaction_hash = tx_hash
            step.end_time = datetime.now()
            step.gas_used = 150000  # Simulé
            
            central_logger.trade_executed(
                message=f"Vente exécutée: {opportunity.token_symbol}",
                symbol=f"{opportunity.token_symbol}/{opportunity.sell_chain}",
                side="sell",
                quantity=execution.actual_amount,
                price=actual_price,
                trade_id=tx_hash
            )
            
            return True
            
        except Exception as e:
            step.status = ExecutionStatus.FAILED
            step.error_message = str(e)
            step.end_time = datetime.now()
            
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.HIGH, {
                'function': '_execute_sell_step',
                'execution_id': execution.id
            })
            return False
    
    def _calculate_final_results(self, execution: ArbitrageExecution):
        """Calcule les résultats finaux de l'exécution"""
        try:
            if not execution.actual_buy_price or not execution.actual_sell_price:
                return
            
            # Revenus de la vente
            gross_revenue = execution.actual_amount * execution.actual_sell_price
            
            # Coût d'achat
            purchase_cost = execution.actual_amount * execution.actual_buy_price
            
            # Frais totaux (bridge + gas)
            bridge_fees = execution.opportunity.bridge_quote.get('total_fee_usd', 0) if execution.opportunity.bridge_quote else 0
            gas_fees = sum(step.gas_used or 0 for step in execution.steps) * 0.00001  # Estimation
            total_fees = bridge_fees + gas_fees
            
            # Profit réalisé
            realized_profit = gross_revenue - purchase_cost - total_fees
            
            # ROI réalisé
            total_investment = purchase_cost + total_fees
            realized_roi = (realized_profit / total_investment) * 100 if total_investment > 0 else 0
            
            # Mettre à jour l'exécution
            execution.total_fees_paid = total_fees
            execution.realized_profit = realized_profit
            execution.realized_roi = realized_roi
            
            # Mettre à jour les statistiques globales
            self.total_profit += realized_profit
            self.total_fees_paid += total_fees
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.MEDIUM, {
                'function': '_calculate_final_results',
                'execution_id': execution.id
            })
    
    def _finalize_execution(self, execution: ArbitrageExecution):
        """Finalise une exécution"""
        # Libérer le capital alloué
        self.capital_allocated -= execution.actual_amount or 0
        
        # Retirer des exécutions actives
        if execution.id in self.active_executions:
            del self.active_executions[execution.id]
        
        # Ajouter à l'historique
        self.execution_history.append(execution)
        
        # Limiter l'historique
        if len(self.execution_history) > 100:
            self.execution_history = self.execution_history[-50:]
        
        # Mettre à jour les statistiques
        self.total_executions += 1
    
    async def cancel_execution(self, execution_id: str) -> bool:
        """Annule une exécution en cours"""
        try:
            execution = self.active_executions.get(execution_id)
            if not execution:
                return False
            
            # Marquer comme annulée
            execution.status = ExecutionStatus.CANCELLED
            execution.end_time = datetime.now()
            
            central_logger.log(
                level="WARNING",
                message="Exécution d'arbitrage annulée",
                category=LogCategory.TRADING,
                execution_id=execution_id
            )
            
            # Finaliser
            self._finalize_execution(execution)
            
            return True
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.MEDIUM, {
                'function': 'cancel_execution',
                'execution_id': execution_id
            })
            return False
    
    def get_active_executions(self) -> List[ArbitrageExecution]:
        """Retourne les exécutions actives"""
        return list(self.active_executions.values())
    
    def get_execution_by_id(self, execution_id: str) -> Optional[ArbitrageExecution]:
        """Récupère une exécution par son ID"""
        return self.active_executions.get(execution_id) or next(
            (ex for ex in self.execution_history if ex.id == execution_id), None
        )
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Retourne les statistiques de performance"""
        success_rate = (self.successful_executions / max(1, self.total_executions)) * 100
        
        avg_profit = 0.0
        avg_roi = 0.0
        avg_execution_time = 0.0
        
        if self.execution_history:
            completed_executions = [
                ex for ex in self.execution_history 
                if ex.status == ExecutionStatus.COMPLETED and ex.realized_profit is not None
            ]
            
            if completed_executions:
                avg_profit = sum(ex.realized_profit for ex in completed_executions) / len(completed_executions)
                avg_roi = sum(ex.realized_roi for ex in completed_executions) / len(completed_executions)
                avg_execution_time = sum(
                    ex.execution_time_seconds for ex in completed_executions 
                    if ex.execution_time_seconds
                ) / len(completed_executions)
        
        return {
            'total_executions': self.total_executions,
            'successful_executions': self.successful_executions,
            'success_rate': success_rate,
            'total_profit': self.total_profit,
            'total_fees_paid': self.total_fees_paid,
            'net_profit': self.total_profit - self.total_fees_paid,
            'average_profit_per_trade': avg_profit,
            'average_roi_percentage': avg_roi,
            'average_execution_time_seconds': avg_execution_time,
            'active_executions_count': len(self.active_executions),
            'capital_allocated': self.capital_allocated,
            'available_capital': self._get_available_capital()
        }
