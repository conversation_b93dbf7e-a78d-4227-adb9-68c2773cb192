#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
⏰ Test Longue Durée avec Notifications Telegram
Script pour lancer des tests de 6h+ avec suivi automatique
"""

import os
import sys
import time
import signal
import argparse
from pathlib import Path
from datetime import datetime, timedelta
import threading
import json

# Ajouter le répertoire du projet au path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from bots.optimized_safe_bot import OptimizedSafeBot
from utils.telegram_notifier import setup_telegram_notifications
from utils.performance_analyzer import PerformanceAnalyzer

class LongTermTestManager:
    """Gestionnaire de tests longue durée avec notifications"""
    
    def __init__(self, duration_hours: int, enable_telegram: bool = True):
        self.duration_hours = duration_hours
        self.enable_telegram = enable_telegram
        self.bot = None
        self.notifier = None
        self.start_time = datetime.now()
        self.end_time = self.start_time + timedelta(hours=duration_hours)
        self.running = False
        
        # Configuration du gestionnaire de signaux pour arrêt propre
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
    def setup_notifications(self):
        """Configure les notifications Telegram"""
        if self.enable_telegram:
            self.notifier = setup_telegram_notifications()
            if self.notifier:
                print("✅ Notifications Telegram activées")
                # Configurer l'intervalle pour tests > 1h
                if self.duration_hours > 1:
                    self.notifier.set_notification_interval(30)  # 30 minutes
                else:
                    self.notifier.set_notification_interval(60)  # 1 heure pour tests courts
            else:
                print("⚠️ Notifications Telegram désactivées")
        else:
            print("📱 Notifications Telegram désactivées par l'utilisateur")
    
    def print_test_header(self):
        """Affiche l'en-tête du test"""
        print("=" * 80)
        print(f"⏰ TEST LONGUE DURÉE - {self.duration_hours}H")
        print("=" * 80)
        print(f"🎯 Objectif: Test de rentabilité sur {self.duration_hours} heures")
        print(f"📅 Début: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📅 Fin prévue: {self.end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"💰 Mode: Testnet (argent fictif)")
        print(f"📱 Notifications: {'✅ Activées' if self.notifier else '❌ Désactivées'}")
        print(f"🔔 Fréquence: Toutes les 30min (tests > 1h)")
        print("=" * 80)
        print()
    
    def start_test(self):
        """Démarre le test longue durée"""
        try:
            self.setup_notifications()
            self.print_test_header()
            
            # Initialiser le bot
            print("🚀 Initialisation du bot optimisé...")
            self.bot = OptimizedSafeBot(mode="testnet")
            
            # Récupérer la configuration initiale
            initial_config = {
                'duration_hours': self.duration_hours,
                'mode': 'testnet',
                'grid_size': self.bot.grid_size,
                'grid_spacing': self.bot.grid_spacing,
                'order_size': self.bot.order_size,
                'stop_loss_percent': self.bot.stop_loss_percent,
                'start_balance': getattr(self.bot, 'initial_balance', 0)
            }
            
            # Envoyer notification de démarrage
            if self.notifier:
                self.notifier.send_startup_notification(initial_config)
            
            print("✅ Bot initialisé avec succès")
            print(f"📊 Balance initiale: {initial_config['start_balance']} USDT")
            print()
            print("🔄 Démarrage du trading automatique...")
            print("⏸️ Appuyez sur Ctrl+C pour arrêter le test à tout moment")
            print("-" * 80)
            
            # Démarrer le monitoring en arrière-plan
            self.running = True
            monitoring_thread = threading.Thread(target=self.monitoring_loop)
            monitoring_thread.daemon = True
            monitoring_thread.start()
            
            # Démarrer le bot
            self.bot.run()
            
        except KeyboardInterrupt:
            print("\n⏸️ Arrêt demandé par l'utilisateur...")
            self.stop_test()
        except Exception as e:
            error_msg = f"Erreur critique: {str(e)}"
            print(f"❌ {error_msg}")
            if self.notifier:
                self.notifier.send_error_notification(error_msg)
            self.stop_test()
    
    def monitoring_loop(self):
        """Boucle de monitoring en arrière-plan"""
        while self.running:
            try:
                # Vérifier si le test doit se terminer
                if datetime.now() >= self.end_time:
                    print(f"\n⏰ Durée de test atteinte ({self.duration_hours}h)")
                    self.stop_test()
                    break
                
                # Envoyer mise à jour périodique si notifications activées
                if self.notifier and self.bot:
                    current_stats = self.get_current_stats()
                    self.notifier.send_periodic_update(current_stats)
                
                # Attendre 5 minutes avant la prochaine vérification
                time.sleep(300)
                
            except Exception as e:
                print(f"⚠️ Erreur monitoring: {e}")
                time.sleep(60)  # Attendre 1 minute en cas d'erreur
    
    def get_current_stats(self) -> dict:
        """Récupère les statistiques actuelles du bot"""
        try:
            if hasattr(self.bot, 'performance_metrics'):
                return self.bot.performance_metrics
            else:
                # Statistiques basiques si pas de métriques avancées
                return {
                    'trades_executed': getattr(self.bot, 'trades_count', 0),
                    'current_balance': getattr(self.bot, 'current_balance', 0),
                    'win_rate': 0.0,
                    'best_trade': 0.0,
                    'worst_trade': 0.0
                }
        except Exception as e:
            print(f"⚠️ Erreur récupération stats: {e}")
            return {}
    
    def stop_test(self):
        """Arrête le test proprement"""
        self.running = False
        
        if self.bot:
            print("🛑 Arrêt du bot...")
            try:
                # Sauvegarder la configuration finale
                final_config_path = f"test_results/configs/final_config_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                if hasattr(self.bot, 'save_strategy_config'):
                    self.bot.save_strategy_config(final_config_path)
                
                # Récupérer les statistiques finales
                final_stats = self.get_current_stats()
                
                # Envoyer notification de fin
                if self.notifier:
                    self.notifier.send_completion_notification(final_stats)
                
                # Afficher résumé final
                self.print_final_summary(final_stats)
                
            except Exception as e:
                print(f"⚠️ Erreur lors de l'arrêt: {e}")
        
        print("✅ Test terminé proprement")
        sys.exit(0)
    
    def print_final_summary(self, final_stats: dict):
        """Affiche le résumé final du test"""
        elapsed = datetime.now() - self.start_time
        hours = int(elapsed.total_seconds() // 3600)
        minutes = int((elapsed.total_seconds() % 3600) // 60)
        
        print("\n" + "=" * 80)
        print(f"📊 RÉSUMÉ FINAL - TEST DE {hours}h{minutes:02d}m")
        print("=" * 80)
        print(f"🎯 Trades exécutés: {final_stats.get('trades_executed', 0)}")
        print(f"💰 P&L Total: {final_stats.get('total_pnl', 0):+.2f} USDT")
        print(f"📈 Win Rate: {final_stats.get('win_rate', 0):.1f}%")
        print(f"⚡ Temps d'exécution moyen: {final_stats.get('avg_execution_time', 0):.2f}s")
        
        # Recommandation
        pnl = final_stats.get('total_pnl', 0)
        if pnl > 0:
            print("🎉 RÉSULTAT: Stratégie rentable - Prête pour la production")
        elif pnl > -50:  # Perte acceptable
            print("⚠️ RÉSULTAT: Performance mitigée - Optimisation recommandée")
        else:
            print("❌ RÉSULTAT: Stratégie non rentable - Révision nécessaire")
        
        print("=" * 80)
    
    def signal_handler(self, signum, frame):
        """Gestionnaire de signaux pour arrêt propre"""
        print(f"\n🛑 Signal reçu ({signum}), arrêt en cours...")
        self.stop_test()


def main():
    """Fonction principale"""
    parser = argparse.ArgumentParser(description="Test longue durée avec notifications")
    parser.add_argument('--hours', type=int, default=6, help='Durée du test en heures (défaut: 6)')
    parser.add_argument('--no-telegram', action='store_true', help='Désactiver les notifications Telegram')
    
    args = parser.parse_args()
    
    # Vérifier la durée minimale
    if args.hours < 1:
        print("❌ Erreur: La durée doit être d'au moins 1 heure")
        sys.exit(1)
    
    # Créer et démarrer le gestionnaire de test
    test_manager = LongTermTestManager(
        duration_hours=args.hours,
        enable_telegram=not args.no_telegram
    )
    
    test_manager.start_test()


if __name__ == "__main__":
    main()
