"""
🚀 Moteur de backtesting pour botCrypto
Orchestrateur principal pour tester les stratégies de trading
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime, timedelta
import logging
from pathlib import Path
import sys
import copy

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from backtesting.data_provider import data_provider
from backtesting.metrics import performance_metrics

class BacktestEngine:
    """Moteur de backtesting pour les stratégies de trading"""
    
    def __init__(self, initial_capital: float = 1000):
        self.logger = logging.getLogger(__name__)
        self.initial_capital = initial_capital
        self.reset()
    
    def reset(self):
        """Remet à zéro l'état du backtesting"""
        self.current_capital = self.initial_capital
        self.positions = {}  # symbol -> quantity
        self.trades = []
        self.equity_curve = []
        self.current_timestamp = None
        self.current_prices = {}
        self.fees_paid = 0
        self.slippage_cost = 0
        
        # Configuration
        self.trading_fee = 0.001  # 0.1% par trade
        self.slippage = 0.0005   # 0.05% de slippage
    
    def set_fees(self, trading_fee: float = 0.001, slippage: float = 0.0005):
        """Configure les frais de trading et slippage"""
        self.trading_fee = trading_fee
        self.slippage = slippage
    
    def get_portfolio_value(self) -> float:
        """Calcule la valeur actuelle du portefeuille"""
        total_value = self.current_capital
        
        for symbol, quantity in self.positions.items():
            if symbol in self.current_prices and quantity != 0:
                total_value += quantity * self.current_prices[symbol]
        
        return total_value
    
    def execute_trade(self, symbol: str, side: str, quantity: float, 
                     price: float, timestamp: datetime, strategy_name: str = "Unknown") -> bool:
        """
        Exécute un trade dans le backtesting
        
        Args:
            symbol: Symbole tradé
            side: 'buy' ou 'sell'
            quantity: Quantité (positive)
            price: Prix d'exécution
            timestamp: Timestamp du trade
            strategy_name: Nom de la stratégie
            
        Returns:
            bool: True si le trade a été exécuté, False sinon
        """
        if quantity <= 0:
            return False
        
        # Appliquer le slippage
        if side.lower() == 'buy':
            execution_price = price * (1 + self.slippage)
            trade_quantity = quantity
        else:  # sell
            execution_price = price * (1 - self.slippage)
            trade_quantity = -quantity
        
        # Calculer le coût total
        trade_value = abs(trade_quantity) * execution_price
        fees = trade_value * self.trading_fee
        total_cost = trade_value + fees
        
        # Vérifier si on a assez de capital pour un achat
        if side.lower() == 'buy' and total_cost > self.current_capital:
            self.logger.warning(f"⚠️ Capital insuffisant pour {symbol}: {total_cost:.2f} > {self.current_capital:.2f}")
            return False
        
        # Vérifier si on a assez de position pour une vente
        current_position = self.positions.get(symbol, 0)
        if side.lower() == 'sell' and quantity > current_position:
            self.logger.warning(f"⚠️ Position insuffisante pour {symbol}: {quantity} > {current_position}")
            return False
        
        # Exécuter le trade
        if side.lower() == 'buy':
            self.current_capital -= total_cost
            self.positions[symbol] = self.positions.get(symbol, 0) + quantity
        else:  # sell
            self.current_capital += trade_value - fees
            self.positions[symbol] = self.positions.get(symbol, 0) - quantity
        
        # Enregistrer les frais
        self.fees_paid += fees
        self.slippage_cost += abs(trade_quantity) * price * self.slippage
        
        # Calculer le P&L pour ce trade
        pnl = 0
        if side.lower() == 'sell':
            # Pour une vente, calculer le P&L basé sur le prix d'achat moyen
            # Simplification: on suppose FIFO
            pnl = (execution_price - price) * quantity - fees
        
        # Enregistrer le trade
        trade_record = {
            'timestamp': timestamp,
            'symbol': symbol,
            'side': side.lower(),
            'quantity': quantity,
            'price': execution_price,
            'fees': fees,
            'pnl': pnl,
            'strategy': strategy_name,
            'portfolio_value': self.get_portfolio_value()
        }
        
        self.trades.append(trade_record)
        
        self.logger.debug(f"📊 Trade exécuté: {side.upper()} {quantity} {symbol} @ {execution_price:.4f}")
        return True
    
    def update_prices(self, prices: Dict[str, float], timestamp: datetime):
        """Met à jour les prix actuels et l'equity curve"""
        self.current_prices = prices.copy()
        self.current_timestamp = timestamp
        
        # Calculer la valeur du portefeuille
        portfolio_value = self.get_portfolio_value()
        
        # Ajouter à l'equity curve
        self.equity_curve.append({
            'timestamp': timestamp,
            'portfolio_value': portfolio_value,
            'cash': self.current_capital,
            'positions_value': portfolio_value - self.current_capital
        })
    
    def run_strategy(self, strategy_func: Callable, data: pd.DataFrame, 
                    symbol: str = 'BTC/USDT', strategy_name: str = "Custom") -> Dict:
        """
        Exécute une stratégie de trading sur des données historiques
        
        Args:
            strategy_func: Fonction de stratégie qui prend (engine, data, index) et retourne les signaux
            data: DataFrame avec les données OHLCV
            symbol: Symbole tradé
            strategy_name: Nom de la stratégie
            
        Returns:
            Dict avec les résultats du backtesting
        """
        self.reset()
        
        self.logger.info(f"🚀 Démarrage backtesting {strategy_name} sur {symbol}")
        self.logger.info(f"📊 Période: {data.index[0]} à {data.index[-1]} ({len(data)} barres)")
        self.logger.info(f"💰 Capital initial: {self.initial_capital}")
        
        # Itérer sur chaque barre de données
        for i in range(len(data)):
            current_bar = data.iloc[i]
            timestamp = data.index[i]
            
            # Mettre à jour les prix
            self.update_prices({symbol: current_bar['close']}, timestamp)
            
            # Appeler la stratégie
            try:
                strategy_func(self, data, i, symbol)
            except Exception as e:
                self.logger.error(f"❌ Erreur stratégie à l'index {i}: {e}")
                continue
        
        # Calculer les métriques finales
        equity_df = pd.DataFrame(self.equity_curve)
        equity_df.set_index('timestamp', inplace=True)
        
        # Générer le rapport de performance
        report = performance_metrics.generate_comprehensive_report(
            equity_df['portfolio_value'], 
            self.trades, 
            self.initial_capital
        )
        
        # Ajouter des informations spécifiques au backtesting
        report['backtest_info'] = {
            'strategy_name': strategy_name,
            'symbol': symbol,
            'initial_capital': self.initial_capital,
            'final_capital': self.current_capital,
            'total_fees_paid': self.fees_paid,
            'total_slippage_cost': self.slippage_cost,
            'positions_final': self.positions.copy(),
            'trading_fee_percent': self.trading_fee * 100,
            'slippage_percent': self.slippage * 100
        }
        
        # Ajouter les données pour la visualisation
        report['equity_curve'] = equity_df
        report['trades_df'] = pd.DataFrame(self.trades) if self.trades else pd.DataFrame()
        
        self.logger.info(f"✅ Backtesting terminé: {report['performance']['total_return_percent']:.2f}% de rendement")
        
        return report
    
    def run_multiple_strategies(self, strategies: Dict[str, Callable], 
                              data: pd.DataFrame, symbol: str = 'BTC/USDT') -> Dict[str, Dict]:
        """
        Exécute plusieurs stratégies et compare les résultats
        
        Args:
            strategies: Dict avec nom_stratégie -> fonction_stratégie
            data: DataFrame avec les données
            symbol: Symbole tradé
            
        Returns:
            Dict avec les résultats de chaque stratégie
        """
        results = {}
        
        for strategy_name, strategy_func in strategies.items():
            self.logger.info(f"🔄 Test de la stratégie: {strategy_name}")
            
            try:
                result = self.run_strategy(strategy_func, data, symbol, strategy_name)
                results[strategy_name] = result
            except Exception as e:
                self.logger.error(f"❌ Erreur stratégie {strategy_name}: {e}")
                results[strategy_name] = {'error': str(e)}
        
        # Générer un tableau de comparaison
        comparison_df = performance_metrics.compare_strategies(results)
        
        return {
            'individual_results': results,
            'comparison': comparison_df,
            'summary': {
                'total_strategies': len(strategies),
                'successful_strategies': len([r for r in results.values() if 'error' not in r]),
                'best_strategy': comparison_df.iloc[0]['Strategy'] if not comparison_df.empty else None
            }
        }

# Instance globale
backtest_engine = BacktestEngine()
