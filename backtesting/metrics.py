"""
📊 Métriques de performance pour le backtesting
Calcul des indicateurs de performance des stratégies
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import logging

class PerformanceMetrics:
    """Calculateur de métriques de performance pour le backtesting"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def calculate_returns(self, prices: pd.Series) -> pd.Series:
        """Calcule les rendements"""
        return prices.pct_change().dropna()
    
    def calculate_cumulative_returns(self, returns: pd.Series) -> pd.Series:
        """Calcule les rendements cumulés"""
        return (1 + returns).cumprod() - 1
    
    def calculate_total_return(self, initial_value: float, final_value: float) -> float:
        """Calcule le rendement total"""
        return (final_value / initial_value - 1) * 100
    
    def calculate_annualized_return(self, total_return: float, days: int) -> float:
        """Calcule le rendement annualisé"""
        return ((1 + total_return / 100) ** (365 / days) - 1) * 100
    
    def calculate_volatility(self, returns: pd.Series, annualized: bool = True) -> float:
        """Calcule la volatilité"""
        vol = returns.std()
        if annualized:
            # Supposer des données horaires (24h * 365j)
            vol *= np.sqrt(24 * 365)
        return vol * 100
    
    def calculate_sharpe_ratio(self, returns: pd.Series, risk_free_rate: float = 0.02) -> float:
        """Calcule le ratio de Sharpe"""
        excess_returns = returns.mean() * 24 * 365 - risk_free_rate
        volatility = self.calculate_volatility(returns, annualized=True) / 100
        
        if volatility == 0:
            return 0
        
        return excess_returns / volatility
    
    def calculate_max_drawdown(self, equity_curve: pd.Series) -> Dict[str, float]:
        """
        Calcule le drawdown maximum
        
        Returns:
            Dict avec max_drawdown, max_drawdown_percent, drawdown_duration
        """
        # Calculer les pics cumulés
        peak = equity_curve.expanding().max()
        
        # Calculer le drawdown
        drawdown = equity_curve - peak
        drawdown_pct = (drawdown / peak) * 100
        
        # Trouver le drawdown maximum
        max_dd = drawdown.min()
        max_dd_pct = drawdown_pct.min()
        
        # Calculer la durée du drawdown maximum
        max_dd_idx = drawdown_pct.idxmin()
        
        # Trouver le début du drawdown (dernier pic avant le minimum)
        peak_before = peak.loc[:max_dd_idx]
        start_idx = peak_before[peak_before == peak_before.iloc[-1]].index[0]
        
        # Trouver la fin du drawdown (retour au niveau du pic)
        recovery_mask = equity_curve.loc[max_dd_idx:] >= peak.loc[max_dd_idx]
        if recovery_mask.any():
            end_idx = recovery_mask[recovery_mask].index[0]
            duration_days = (end_idx - start_idx).days
        else:
            duration_days = (equity_curve.index[-1] - start_idx).days
        
        return {
            'max_drawdown': abs(max_dd),
            'max_drawdown_percent': abs(max_dd_pct),
            'drawdown_duration_days': duration_days,
            'drawdown_start': start_idx,
            'drawdown_end': max_dd_idx
        }
    
    def calculate_win_rate(self, trades: List[Dict]) -> Dict[str, float]:
        """Calcule le taux de réussite des trades"""
        if not trades:
            return {'win_rate': 0, 'total_trades': 0, 'winning_trades': 0, 'losing_trades': 0}
        
        winning_trades = sum(1 for trade in trades if trade.get('pnl', 0) > 0)
        losing_trades = sum(1 for trade in trades if trade.get('pnl', 0) < 0)
        total_trades = len(trades)
        
        return {
            'win_rate': (winning_trades / total_trades) * 100 if total_trades > 0 else 0,
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades
        }
    
    def calculate_profit_factor(self, trades: List[Dict]) -> float:
        """Calcule le profit factor (gains totaux / pertes totales)"""
        if not trades:
            return 0
        
        total_gains = sum(trade.get('pnl', 0) for trade in trades if trade.get('pnl', 0) > 0)
        total_losses = abs(sum(trade.get('pnl', 0) for trade in trades if trade.get('pnl', 0) < 0))
        
        if total_losses == 0:
            return float('inf') if total_gains > 0 else 0
        
        return total_gains / total_losses
    
    def calculate_average_trade(self, trades: List[Dict]) -> Dict[str, float]:
        """Calcule les statistiques moyennes des trades"""
        if not trades:
            return {'avg_trade': 0, 'avg_win': 0, 'avg_loss': 0}
        
        pnls = [trade.get('pnl', 0) for trade in trades]
        wins = [pnl for pnl in pnls if pnl > 0]
        losses = [pnl for pnl in pnls if pnl < 0]
        
        return {
            'avg_trade': np.mean(pnls) if pnls else 0,
            'avg_win': np.mean(wins) if wins else 0,
            'avg_loss': np.mean(losses) if losses else 0
        }
    
    def calculate_calmar_ratio(self, total_return: float, max_drawdown_pct: float) -> float:
        """Calcule le ratio de Calmar (rendement annualisé / drawdown max)"""
        if max_drawdown_pct == 0:
            return float('inf') if total_return > 0 else 0
        
        return total_return / max_drawdown_pct
    
    def calculate_sortino_ratio(self, returns: pd.Series, risk_free_rate: float = 0.02) -> float:
        """Calcule le ratio de Sortino (comme Sharpe mais avec downside deviation)"""
        excess_returns = returns.mean() * 24 * 365 - risk_free_rate
        
        # Calculer la downside deviation
        negative_returns = returns[returns < 0]
        if len(negative_returns) == 0:
            return float('inf') if excess_returns > 0 else 0
        
        downside_deviation = negative_returns.std() * np.sqrt(24 * 365)
        
        if downside_deviation == 0:
            return float('inf') if excess_returns > 0 else 0
        
        return excess_returns / downside_deviation
    
    def generate_comprehensive_report(self, equity_curve: pd.Series, trades: List[Dict], 
                                    initial_capital: float = 1000) -> Dict:
        """
        Génère un rapport complet de performance
        
        Args:
            equity_curve: Série temporelle de la valeur du portefeuille
            trades: Liste des trades exécutés
            initial_capital: Capital initial
            
        Returns:
            Dict avec toutes les métriques de performance
        """
        if len(equity_curve) < 2:
            return {'error': 'Données insuffisantes pour le calcul des métriques'}
        
        # Calculs de base
        returns = self.calculate_returns(equity_curve)
        final_value = equity_curve.iloc[-1]
        total_return = self.calculate_total_return(initial_capital, final_value)
        
        # Période de backtesting
        start_date = equity_curve.index[0]
        end_date = equity_curve.index[-1]
        duration_days = (end_date - start_date).days
        
        # Métriques de rendement
        annualized_return = self.calculate_annualized_return(total_return, duration_days)
        volatility = self.calculate_volatility(returns)
        
        # Métriques de risque
        sharpe_ratio = self.calculate_sharpe_ratio(returns)
        sortino_ratio = self.calculate_sortino_ratio(returns)
        max_dd_info = self.calculate_max_drawdown(equity_curve)
        calmar_ratio = self.calculate_calmar_ratio(annualized_return, max_dd_info['max_drawdown_percent'])
        
        # Métriques de trading
        win_rate_info = self.calculate_win_rate(trades)
        profit_factor = self.calculate_profit_factor(trades)
        avg_trade_info = self.calculate_average_trade(trades)
        
        # Compilation du rapport
        report = {
            # Informations générales
            'backtest_period': {
                'start_date': start_date.strftime('%Y-%m-%d %H:%M:%S'),
                'end_date': end_date.strftime('%Y-%m-%d %H:%M:%S'),
                'duration_days': duration_days,
                'total_bars': len(equity_curve)
            },
            
            # Performance globale
            'performance': {
                'initial_capital': initial_capital,
                'final_value': final_value,
                'total_return_percent': total_return,
                'annualized_return_percent': annualized_return,
                'volatility_percent': volatility,
                'sharpe_ratio': sharpe_ratio,
                'sortino_ratio': sortino_ratio,
                'calmar_ratio': calmar_ratio
            },
            
            # Métriques de risque
            'risk_metrics': {
                'max_drawdown': max_dd_info['max_drawdown'],
                'max_drawdown_percent': max_dd_info['max_drawdown_percent'],
                'drawdown_duration_days': max_dd_info['drawdown_duration_days'],
                'var_95': np.percentile(returns, 5) * 100,  # Value at Risk 95%
                'cvar_95': returns[returns <= np.percentile(returns, 5)].mean() * 100  # Conditional VaR
            },
            
            # Métriques de trading
            'trading_metrics': {
                'total_trades': win_rate_info['total_trades'],
                'winning_trades': win_rate_info['winning_trades'],
                'losing_trades': win_rate_info['losing_trades'],
                'win_rate_percent': win_rate_info['win_rate'],
                'profit_factor': profit_factor,
                'avg_trade': avg_trade_info['avg_trade'],
                'avg_winning_trade': avg_trade_info['avg_win'],
                'avg_losing_trade': avg_trade_info['avg_loss']
            }
        }
        
        return report
    
    def compare_strategies(self, reports: Dict[str, Dict]) -> pd.DataFrame:
        """
        Compare plusieurs stratégies
        
        Args:
            reports: Dict avec nom_stratégie -> rapport de performance
            
        Returns:
            DataFrame de comparaison
        """
        comparison_data = []
        
        for strategy_name, report in reports.items():
            if 'error' in report:
                continue
                
            comparison_data.append({
                'Strategy': strategy_name,
                'Total Return (%)': report['performance']['total_return_percent'],
                'Annualized Return (%)': report['performance']['annualized_return_percent'],
                'Volatility (%)': report['performance']['volatility_percent'],
                'Sharpe Ratio': report['performance']['sharpe_ratio'],
                'Max Drawdown (%)': report['risk_metrics']['max_drawdown_percent'],
                'Win Rate (%)': report['trading_metrics']['win_rate_percent'],
                'Profit Factor': report['trading_metrics']['profit_factor'],
                'Total Trades': report['trading_metrics']['total_trades']
            })
        
        df = pd.DataFrame(comparison_data)
        
        if not df.empty:
            # Trier par Sharpe Ratio décroissant
            df = df.sort_values('Sharpe Ratio', ascending=False)
        
        return df

# Instance globale
performance_metrics = PerformanceMetrics()
