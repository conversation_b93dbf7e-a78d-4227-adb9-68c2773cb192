"""
📈 Fournisseur de données historiques pour le backtesting
Récupère et prépare les données de différentes sources
"""

import ccxt
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging
import time
from pathlib import Path
import sys

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from config.settings import config

class DataProvider:
    """Fournisseur de données historiques pour le backtesting"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.exchanges = {}
        self._init_exchanges()
        
        # Cache pour éviter les appels API répétés
        self.data_cache = {}
    
    def _init_exchanges(self):
        """Initialise les connexions aux exchanges"""
        try:
            # Binance (principal)
            self.exchanges['binance'] = ccxt.binance({
                'apiKey': config.binance_prod_api_key,
                'secret': config.binance_prod_api_secret,
                'sandbox': False,
                'enableRateLimit': True,
            })
            
            # Autres exchanges pour l'arbitrage
            self.exchanges['kucoin'] = ccxt.kucoin({'enableRateLimit': True})
            self.exchanges['okx'] = ccxt.okx({'enableRateLimit': True})
            
        except Exception as e:
            self.logger.error(f"❌ Erreur initialisation exchanges: {e}")
    
    def get_historical_data(self, symbol: str, timeframe: str = '1h', 
                          days: int = 30, exchange: str = 'binance') -> pd.DataFrame:
        """
        Récupère les données historiques d'un symbole
        
        Args:
            symbol: Symbole de trading (ex: 'BTC/USDT')
            timeframe: Timeframe ('1m', '5m', '1h', '1d')
            days: Nombre de jours d'historique
            exchange: Exchange à utiliser
            
        Returns:
            DataFrame avec colonnes: timestamp, open, high, low, close, volume
        """
        cache_key = f"{exchange}_{symbol}_{timeframe}_{days}"
        
        # Vérifier le cache
        if cache_key in self.data_cache:
            cached_data, cache_time = self.data_cache[cache_key]
            if time.time() - cache_time < 3600:  # Cache valide 1h
                self.logger.info(f"📊 Données {symbol} récupérées du cache")
                return cached_data
        
        try:
            exchange_obj = self.exchanges.get(exchange)
            if not exchange_obj:
                raise ValueError(f"Exchange {exchange} non disponible")
            
            # Calculer la date de début
            since = exchange_obj.parse8601(
                (datetime.now() - timedelta(days=days)).isoformat()
            )
            
            self.logger.info(f"📊 Récupération données {symbol} sur {days} jours...")
            
            # Récupérer les données OHLCV
            ohlcv = exchange_obj.fetch_ohlcv(symbol, timeframe, since=since)
            
            if not ohlcv:
                raise ValueError(f"Aucune donnée trouvée pour {symbol}")
            
            # Convertir en DataFrame
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            
            # Nettoyer les données
            df = self._clean_data(df)
            
            # Mettre en cache
            self.data_cache[cache_key] = (df.copy(), time.time())
            
            self.logger.info(f"✅ {len(df)} barres récupérées pour {symbol}")
            return df
            
        except Exception as e:
            self.logger.error(f"❌ Erreur récupération données {symbol}: {e}")
            raise
    
    def _clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Nettoie et valide les données"""
        # Supprimer les valeurs nulles
        df = df.dropna()
        
        # Supprimer les doublons
        df = df[~df.index.duplicated(keep='first')]
        
        # Trier par timestamp
        df = df.sort_index()
        
        # Valider que OHLC est cohérent
        invalid_rows = (df['high'] < df['low']) | (df['high'] < df['open']) | (df['high'] < df['close']) | \
                      (df['low'] > df['open']) | (df['low'] > df['close'])
        
        if invalid_rows.any():
            self.logger.warning(f"⚠️ {invalid_rows.sum()} barres invalides supprimées")
            df = df[~invalid_rows]
        
        return df
    
    def get_multiple_symbols_data(self, symbols: List[str], timeframe: str = '1h', 
                                days: int = 30, exchange: str = 'binance') -> Dict[str, pd.DataFrame]:
        """
        Récupère les données pour plusieurs symboles
        
        Args:
            symbols: Liste des symboles
            timeframe: Timeframe
            days: Nombre de jours
            exchange: Exchange
            
        Returns:
            Dict avec les DataFrames pour chaque symbole
        """
        data = {}
        
        for symbol in symbols:
            try:
                data[symbol] = self.get_historical_data(symbol, timeframe, days, exchange)
                time.sleep(0.1)  # Rate limiting
            except Exception as e:
                self.logger.error(f"❌ Erreur pour {symbol}: {e}")
                continue
        
        return data
    
    def get_cross_exchange_data(self, symbol: str, exchanges: List[str], 
                              timeframe: str = '1h', days: int = 7) -> Dict[str, pd.DataFrame]:
        """
        Récupère les données du même symbole sur plusieurs exchanges
        Utile pour le backtesting d'arbitrage
        """
        data = {}
        
        for exchange in exchanges:
            try:
                if exchange in self.exchanges:
                    data[exchange] = self.get_historical_data(symbol, timeframe, days, exchange)
                    time.sleep(0.2)  # Rate limiting plus strict
            except Exception as e:
                self.logger.error(f"❌ Erreur {exchange} pour {symbol}: {e}")
                continue
        
        return data
    
    def add_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Ajoute des indicateurs techniques aux données
        
        Args:
            df: DataFrame avec OHLCV
            
        Returns:
            DataFrame enrichi avec indicateurs
        """
        df = df.copy()
        
        # Moyennes mobiles
        df['sma_20'] = df['close'].rolling(window=20).mean()
        df['sma_50'] = df['close'].rolling(window=50).mean()
        df['ema_12'] = df['close'].ewm(span=12).mean()
        df['ema_26'] = df['close'].ewm(span=26).mean()
        
        # MACD
        df['macd'] = df['ema_12'] - df['ema_26']
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_histogram'] = df['macd'] - df['macd_signal']
        
        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # Bollinger Bands
        df['bb_middle'] = df['close'].rolling(window=20).mean()
        bb_std = df['close'].rolling(window=20).std()
        df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
        df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
        
        # Volume indicators
        df['volume_sma'] = df['volume'].rolling(window=20).mean()
        df['volume_ratio'] = df['volume'] / df['volume_sma']
        
        # Volatilité
        df['volatility'] = df['close'].pct_change().rolling(window=20).std() * np.sqrt(24)
        
        return df
    
    def get_market_data_summary(self, symbol: str, days: int = 30) -> Dict:
        """
        Récupère un résumé des données de marché
        """
        try:
            df = self.get_historical_data(symbol, '1h', days)
            df = self.add_technical_indicators(df)
            
            return {
                'symbol': symbol,
                'period_days': days,
                'total_bars': len(df),
                'price_start': df['close'].iloc[0],
                'price_end': df['close'].iloc[-1],
                'price_min': df['low'].min(),
                'price_max': df['high'].max(),
                'total_return': ((df['close'].iloc[-1] / df['close'].iloc[0]) - 1) * 100,
                'volatility_avg': df['volatility'].mean() * 100,
                'volume_avg': df['volume'].mean(),
                'rsi_avg': df['rsi'].mean(),
                'data_quality': 'Good' if len(df) > days * 20 else 'Limited'
            }
            
        except Exception as e:
            self.logger.error(f"❌ Erreur résumé marché {symbol}: {e}")
            return {}

# Instance globale
data_provider = DataProvider()
