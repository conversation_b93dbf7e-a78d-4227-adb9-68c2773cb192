"""
📈 Stratégies de trading pour le backtesting
Collection de stratégies prêtes à tester
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional
import logging

class TradingStrategies:
    """Collection de stratégies de trading pour le backtesting"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    @staticmethod
    def grid_trading_strategy(engine, data: pd.DataFrame, index: int, symbol: str):
        """
        Stratégie de grid trading
        Place des ordres d'achat et de vente à intervalles réguliers
        """
        if index < 20:  # Attendre un peu de données
            return
        
        current_price = data.iloc[index]['close']
        
        # Paramètres de la grille
        grid_spacing = 0.02  # 2% d'espacement
        max_position = 5     # Position maximale
        order_size = 0.1     # Taille de chaque ordre (en fraction du capital)
        
        # Position actuelle
        current_position = engine.positions.get(symbol, 0)
        
        # Prix de référence (moyenne mobile 20)
        if index >= 20:
            reference_price = data.iloc[index-19:index+1]['close'].mean()
        else:
            reference_price = current_price
        
        # Calculer les niveaux de grille
        buy_level = reference_price * (1 - grid_spacing)
        sell_level = reference_price * (1 + grid_spacing)
        
        # Signal d'achat si le prix descend sous le niveau d'achat
        if current_price <= buy_level and current_position < max_position:
            order_value = engine.current_capital * order_size
            quantity = order_value / current_price
            engine.execute_trade(symbol, 'buy', quantity, current_price, 
                               data.index[index], 'GridTrading')
        
        # Signal de vente si le prix monte au-dessus du niveau de vente
        elif current_price >= sell_level and current_position > 0:
            quantity = min(current_position, engine.current_capital * order_size / current_price)
            engine.execute_trade(symbol, 'sell', quantity, current_price, 
                               data.index[index], 'GridTrading')
    
    @staticmethod
    def sma_crossover_strategy(engine, data: pd.DataFrame, index: int, symbol: str):
        """
        Stratégie de croisement de moyennes mobiles
        Achat quand SMA courte > SMA longue, vente dans le cas contraire
        """
        if index < 50:  # Besoin de 50 barres pour SMA 50
            return
        
        # Calculer les moyennes mobiles
        sma_short = data.iloc[index-19:index+1]['close'].mean()  # SMA 20
        sma_long = data.iloc[index-49:index+1]['close'].mean()   # SMA 50
        
        # Moyennes mobiles précédentes
        sma_short_prev = data.iloc[index-20:index]['close'].mean()
        sma_long_prev = data.iloc[index-50:index]['close'].mean()
        
        current_price = data.iloc[index]['close']
        current_position = engine.positions.get(symbol, 0)
        
        # Signal d'achat : croisement haussier
        if (sma_short > sma_long and sma_short_prev <= sma_long_prev and 
            current_position == 0):
            
            # Investir 80% du capital disponible
            order_value = engine.current_capital * 0.8
            quantity = order_value / current_price
            engine.execute_trade(symbol, 'buy', quantity, current_price, 
                               data.index[index], 'SMA_Crossover')
        
        # Signal de vente : croisement baissier
        elif (sma_short < sma_long and sma_short_prev >= sma_long_prev and 
              current_position > 0):
            
            engine.execute_trade(symbol, 'sell', current_position, current_price, 
                               data.index[index], 'SMA_Crossover')
    
    @staticmethod
    def rsi_mean_reversion_strategy(engine, data: pd.DataFrame, index: int, symbol: str):
        """
        Stratégie de retour à la moyenne basée sur RSI
        Achat en zone de survente, vente en zone de surachat
        """
        if index < 14:  # Besoin de 14 barres pour RSI
            return
        
        # Calculer RSI
        closes = data.iloc[index-13:index+1]['close']
        delta = closes.diff()
        gain = (delta.where(delta > 0, 0)).mean()
        loss = (-delta.where(delta < 0, 0)).mean()
        
        if loss == 0:
            rsi = 100
        else:
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
        
        current_price = data.iloc[index]['close']
        current_position = engine.positions.get(symbol, 0)
        
        # Paramètres RSI
        oversold_level = 30
        overbought_level = 70
        
        # Signal d'achat : RSI en zone de survente
        if rsi < oversold_level and current_position == 0:
            order_value = engine.current_capital * 0.5
            quantity = order_value / current_price
            engine.execute_trade(symbol, 'buy', quantity, current_price, 
                               data.index[index], 'RSI_MeanReversion')
        
        # Signal de vente : RSI en zone de surachat
        elif rsi > overbought_level and current_position > 0:
            engine.execute_trade(symbol, 'sell', current_position, current_price, 
                               data.index[index], 'RSI_MeanReversion')
    
    @staticmethod
    def bollinger_bands_strategy(engine, data: pd.DataFrame, index: int, symbol: str):
        """
        Stratégie basée sur les bandes de Bollinger
        Achat près de la bande inférieure, vente près de la bande supérieure
        """
        if index < 20:  # Besoin de 20 barres
            return
        
        # Calculer les bandes de Bollinger
        closes = data.iloc[index-19:index+1]['close']
        sma = closes.mean()
        std = closes.std()
        
        upper_band = sma + (2 * std)
        lower_band = sma - (2 * std)
        
        current_price = data.iloc[index]['close']
        current_position = engine.positions.get(symbol, 0)
        
        # Signal d'achat : prix proche de la bande inférieure
        if current_price <= lower_band * 1.01 and current_position == 0:
            order_value = engine.current_capital * 0.6
            quantity = order_value / current_price
            engine.execute_trade(symbol, 'buy', quantity, current_price, 
                               data.index[index], 'BollingerBands')
        
        # Signal de vente : prix proche de la bande supérieure
        elif current_price >= upper_band * 0.99 and current_position > 0:
            engine.execute_trade(symbol, 'sell', current_position, current_price, 
                               data.index[index], 'BollingerBands')
    
    @staticmethod
    def macd_strategy(engine, data: pd.DataFrame, index: int, symbol: str):
        """
        Stratégie basée sur MACD
        Achat sur croisement haussier, vente sur croisement baissier
        """
        if index < 26:  # Besoin de 26 barres pour MACD
            return
        
        # Calculer MACD
        closes = data.iloc[index-25:index+1]['close']
        ema_12 = closes.ewm(span=12).mean().iloc[-1]
        ema_26 = closes.ewm(span=26).mean().iloc[-1]
        macd = ema_12 - ema_26
        
        # Signal line (EMA 9 du MACD)
        if index >= 34:  # 26 + 9 - 1
            macd_values = []
            for i in range(max(0, index-8), index+1):
                if i >= 25:
                    closes_temp = data.iloc[i-25:i+1]['close']
                    ema_12_temp = closes_temp.ewm(span=12).mean().iloc[-1]
                    ema_26_temp = closes_temp.ewm(span=26).mean().iloc[-1]
                    macd_values.append(ema_12_temp - ema_26_temp)
            
            if len(macd_values) >= 2:
                signal_line = pd.Series(macd_values).ewm(span=9).mean().iloc[-1]
                signal_line_prev = pd.Series(macd_values[:-1]).ewm(span=9).mean().iloc[-1]
                
                current_price = data.iloc[index]['close']
                current_position = engine.positions.get(symbol, 0)
                
                # Calculer MACD précédent
                closes_prev = data.iloc[index-26:index]['close']
                ema_12_prev = closes_prev.ewm(span=12).mean().iloc[-1]
                ema_26_prev = closes_prev.ewm(span=26).mean().iloc[-1]
                macd_prev = ema_12_prev - ema_26_prev
                
                # Signal d'achat : croisement haussier MACD > Signal
                if (macd > signal_line and macd_prev <= signal_line_prev and 
                    current_position == 0):
                    
                    order_value = engine.current_capital * 0.7
                    quantity = order_value / current_price
                    engine.execute_trade(symbol, 'buy', quantity, current_price, 
                                       data.index[index], 'MACD')
                
                # Signal de vente : croisement baissier MACD < Signal
                elif (macd < signal_line and macd_prev >= signal_line_prev and 
                      current_position > 0):
                    
                    engine.execute_trade(symbol, 'sell', current_position, current_price, 
                                       data.index[index], 'MACD')
    
    @staticmethod
    def buy_and_hold_strategy(engine, data: pd.DataFrame, index: int, symbol: str):
        """
        Stratégie Buy & Hold (référence)
        Achète au début et garde jusqu'à la fin
        """
        if index == 20:  # Acheter après 20 barres
            current_price = data.iloc[index]['close']
            order_value = engine.current_capital * 0.95  # Investir 95%
            quantity = order_value / current_price
            engine.execute_trade(symbol, 'buy', quantity, current_price, 
                               data.index[index], 'BuyAndHold')
    
    @classmethod
    def get_all_strategies(cls) -> Dict[str, callable]:
        """Retourne toutes les stratégies disponibles"""
        return {
            'Grid Trading': cls.grid_trading_strategy,
            'SMA Crossover': cls.sma_crossover_strategy,
            'RSI Mean Reversion': cls.rsi_mean_reversion_strategy,
            'Bollinger Bands': cls.bollinger_bands_strategy,
            'MACD': cls.macd_strategy,
            'Buy & Hold': cls.buy_and_hold_strategy
        }
    
    @classmethod
    def get_strategy_descriptions(cls) -> Dict[str, str]:
        """Retourne les descriptions des stratégies"""
        return {
            'Grid Trading': 'Place des ordres à intervalles réguliers autour du prix de référence',
            'SMA Crossover': 'Achat/vente basé sur le croisement de moyennes mobiles (20/50)',
            'RSI Mean Reversion': 'Achat en survente (RSI<30), vente en surachat (RSI>70)',
            'Bollinger Bands': 'Trading basé sur les bandes de Bollinger (2 écarts-types)',
            'MACD': 'Signaux basés sur les croisements MACD/Signal',
            'Buy & Hold': 'Stratégie de référence : acheter et garder'
        }

# Instance globale
trading_strategies = TradingStrategies()
