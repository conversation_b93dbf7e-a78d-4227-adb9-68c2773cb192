# 📁 Scripts de Lancement

Ce dossier contient les scripts de lancement et interfaces pour les bots de trading.

## 🚀 Scripts Disponibles

### ⚡ `quick_test.py` - Tests Rapides
Interface simple pour lancer des tests express du Safe Bot v2.0.

**Utilisation :**
```bash
# Tests prédéfinis
python scripts/quick_test.py --test-1h    # Test de 1 heure
python scripts/quick_test.py --test-6h    # Test de 6 heures (avec Telegram)
python scripts/quick_test.py --test-24h   # Test de 24 heures
python scripts/quick_test.py --test-week  # Test d'une semaine

# Déploiement rapide
python scripts/quick_test.py --deploy     # Déploiement en production

# Statut
python scripts/quick_test.py --status     # Afficher le statut
```

**Fonctionnalités :**
- ✅ Tests prédéfinis (1h, 6h, 24h, semaine)
- ✅ Support Telegram pour tests > 1h
- ✅ Déploiement rapide en production
- ✅ Interface simple et directe

### 🎛️ `run_safe_bot.py` - Interface Avancée
Interface complète pour la gestion avancée du Safe Bot v2.0.

**Utilisation :**
```bash
python scripts/run_safe_bot.py
```

**Fonctionnalités :**
- ✅ Menu interactif complet
- ✅ Configuration avancée des paramètres
- ✅ Analyse de performance détaillée
- ✅ Gestion des configurations sauvegardées
- ✅ Optimisation automatique des paramètres
- ✅ Statistiques en temps réel

## 🎯 Quelle Interface Choisir ?

### ⚡ Utilisez `quick_test.py` si :
- Vous voulez lancer un test rapidement
- Vous connaissez déjà la durée souhaitée
- Vous préférez une interface simple
- Vous voulez des tests avec notifications Telegram

### 🎛️ Utilisez `run_safe_bot.py` si :
- Vous voulez configurer finement les paramètres
- Vous avez besoin d'analyses détaillées
- Vous gérez plusieurs configurations
- Vous voulez optimiser automatiquement

## 📱 Support Telegram

Les deux interfaces supportent les notifications Telegram pour les tests longue durée :

1. **Configuration** : Voir `docs/TELEGRAM_SETUP.md`
2. **Activation automatique** : Pour tests ≥ 1h
3. **Notifications** : Toutes les 30 minutes
4. **Alertes** : Erreurs en temps réel

## 🔧 Configuration Requise

Assurez-vous d'avoir configuré votre fichier `.env.local` :

```bash
# Clés API Binance
safe_bot_TEST_API_KEY=votre_cle_testnet
safe_bot_TEST_API_SECRET=votre_secret_testnet
safe_bot_PROD_API_KEY=votre_cle_production
safe_bot_PROD_API_SECRET=votre_secret_production

# Notifications Telegram (optionnel)
TELEGRAM_BOT_TOKEN=votre_token_bot
TELEGRAM_CHAT_ID=votre_chat_id
```

## 📊 Exemples d'Utilisation

### Test Rapide de 6h avec Telegram
```bash
python scripts/quick_test.py --test-6h
# → Interface simple, notifications automatiques
```

### Configuration Avancée
```bash
python scripts/run_safe_bot.py
# → Menu interactif, options avancées
```

### Déploiement Express en Production
```bash
python scripts/quick_test.py --deploy
# → Déploiement rapide avec validation
```

## 🗂️ Organisation

```
scripts/
├── README.md           # Ce fichier
├── quick_test.py       # Interface simple et rapide
└── run_safe_bot.py     # Interface avancée complète
```

## 🚀 Lancement depuis la Racine

Vous pouvez aussi utiliser le launcher principal :

```bash
python launcher.py
# → Menu principal avec accès à tous les scripts
```

---

**💡 Conseil :** Commencez par `quick_test.py` pour vos premiers tests, puis utilisez `run_safe_bot.py` pour une gestion plus avancée.
