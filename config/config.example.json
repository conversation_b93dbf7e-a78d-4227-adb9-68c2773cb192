{"_comment": "Configuration d'exemple pour BotCrypto - Copiez vers config.json et personnalisez", "environment": "development", "debug": true, "trading": {"max_position_size_pct": 10.0, "max_daily_loss_pct": 5.0, "default_slippage_tolerance": 2.0, "enable_paper_trading": true, "require_confirmation": false, "emergency_stop_loss_pct": 15.0, "max_concurrent_positions": 5, "position_sizing_method": "fixed_percentage", "rebalance_frequency_hours": 24}, "risk_management": {"max_correlation": 0.8, "max_drawdown_pct": 20.0, "diversification_min_assets": 3, "var_confidence_level": 0.95, "stress_test_scenarios": ["bear_market", "high_volatility", "low_liquidity"], "circuit_breaker_enabled": true, "max_consecutive_losses": 10}, "monitoring": {"dashboard_port": 8080, "enable_notifications": true, "log_level": "INFO", "metrics_retention_hours": 168, "alert_cooldown_minutes": 15, "websocket_update_interval_seconds": 10, "health_check_interval_seconds": 30}, "database": {"url": "sqlite:///data/botcrypto.db", "echo": false, "pool_size": 10, "max_overflow": 20, "pool_timeout": 30, "backup_enabled": true, "backup_interval_hours": 6}, "blockchain": {"default_chain": "ethereum", "networks": {"ethereum": {"rpc_url": "https://mainnet.infura.io/v3/YOUR_INFURA_KEY", "chain_id": 1, "gas_price_strategy": "fast", "max_gas_price_gwei": 150, "confirmation_blocks": 1}, "bsc": {"rpc_url": "https://bsc-dataseed.binance.org/", "chain_id": 56, "gas_price_gwei": 5, "confirmation_blocks": 3}, "polygon": {"rpc_url": "https://polygon-rpc.com/", "chain_id": 137, "gas_price_gwei": 30, "confirmation_blocks": 10}}}, "bots": {"dex_scalping": {"enabled": false, "target_pairs": ["ETH/USDC", "WBTC/USDC"], "min_profit_usd": 20.0, "max_position_size_usd": 5000.0, "max_slippage_percentage": 2.0, "scan_interval_seconds": 5, "max_concurrent_trades": 3, "monitored_dexes": ["uniswap_v3", "sushiswap"], "min_liquidity_usd": 100000.0, "gas_price_multiplier": 1.1}, "cross_chain_arbitrage": {"enabled": false, "min_profit_usd": 50.0, "monitored_chains": ["ethereum", "bsc", "polygon"], "monitored_tokens": ["USDT", "USDC", "DAI"], "max_concurrent_trades": 2, "scan_interval_seconds": 15, "max_bridge_time_minutes": 30, "min_liquidity_usd": 500000.0}, "copy_trading": {"enabled": false, "tracked_wallets": [{"address": "******************************************", "label": "Example Whale", "min_trade_size": 1000.0, "copy_percentage": 2.0, "max_position_size": 5000.0, "enabled": false}], "default_copy_percentage": 1.0, "max_concurrent_copies": 5, "analysis_interval_minutes": 5, "min_wallet_balance": 100000.0, "min_success_rate": 60.0}}, "backtesting": {"data_source": "local", "default_timeframe": "1h", "max_backtest_duration_days": 365, "enable_slippage_simulation": true, "enable_fee_simulation": true, "default_initial_capital": 10000.0, "benchmark_symbol": "ETH/USDC"}, "paper_trading": {"enabled": true, "initial_balance": 10000.0, "simulate_slippage": true, "simulate_fees": true, "simulate_latency": true, "max_latency_ms": 500, "fee_percentage": 0.3}, "notifications": {"telegram": {"enabled": false, "bot_token": "YOUR_TELEGRAM_BOT_TOKEN", "chat_id": "YOUR_TELEGRAM_CHAT_ID", "alert_levels": ["WARNING", "CRITICAL", "EMERGENCY"], "rate_limit_messages_per_hour": 10}, "email": {"enabled": false, "smtp_server": "smtp.gmail.com", "smtp_port": 587, "username": "<EMAIL>", "password": "your_app_password", "to_email": "<EMAIL>", "from_email": "<EMAIL>"}, "webhook": {"enabled": false, "url": "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK", "format": "slack", "timeout_seconds": 10}}, "api": {"enable_auth": false, "api_keys": ["your-secure-api-key-here"], "rate_limit_per_minute": 60, "cors_origins": ["http://localhost:3000", "https://yourdomain.com"], "enable_docs": true, "docs_url": "/docs"}, "logging": {"level": "INFO", "format": "structured", "file_enabled": true, "file_path": "logs/botcrypto.log", "file_max_size_mb": 100, "file_backup_count": 5, "console_enabled": true, "sentry_enabled": false, "sentry_dsn": "https://<EMAIL>/project_id"}, "security": {"encrypt_private_keys": true, "key_rotation_days": 90, "session_timeout_minutes": 60, "max_login_attempts": 5, "lockout_duration_minutes": 30, "require_2fa": false}, "performance": {"cache_enabled": true, "cache_ttl_seconds": 300, "max_memory_usage_mb": 1024, "gc_threshold": 0.8, "connection_pool_size": 10, "request_timeout_seconds": 30}, "alerts": {"rules": [{"id": "high_cpu_usage", "name": "High CPU Usage", "description": "CPU usage above threshold", "type": "system", "level": "warning", "condition": "cpu_usage > 80", "threshold": 80.0, "cooldown_minutes": 15, "enabled": true}, {"id": "daily_loss_limit", "name": "Daily Loss Limit", "description": "Daily loss exceeds limit", "type": "trading", "level": "critical", "condition": "daily_pnl < -500", "threshold": -500.0, "cooldown_minutes": 60, "enabled": true}, {"id": "bot_stopped", "name": "Bot Stopped", "description": "Trading bot has stopped", "type": "bot", "level": "warning", "condition": "bot_status != 'running'", "cooldown_minutes": 5, "enabled": true}]}, "features": {"enable_backtesting": true, "enable_paper_trading": true, "enable_live_trading": false, "enable_copy_trading": true, "enable_arbitrage": true, "enable_scalping": true, "enable_ml_predictions": false, "enable_advanced_analytics": true}, "maintenance": {"auto_restart_on_error": true, "max_restart_attempts": 3, "restart_delay_seconds": 60, "cleanup_old_logs_days": 30, "cleanup_old_data_days": 90, "health_check_url": "/api/health"}}