#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧹 Cleanup Tool
Nettoie les fichiers temporaires et inutiles du projet
"""

import os
import glob
from pathlib import Path

def cleanup_project():
    """Nettoie le projet des fichiers temporaires"""
    
    project_root = Path(__file__).parent.parent
    os.chdir(project_root)
    
    print("🧹 NETTOYAGE DU PROJET")
    print("=" * 40)
    
    # Fichiers temporaires à supprimer
    temp_patterns = [
        "fix_*.py",
        "*_backup.py", 
        "*_temp.py",
        "test_*.py",  # Sauf ceux dans tests/
        "quick_*.py",
        "install_*.py",  # Scripts d'installation temporaires
        "requirements_*.txt",  # Sauf requirements.txt principal
        "*.tmp",
        "*.temp"
    ]
    
    # Logs temporaires à supprimer
    log_patterns = [
        "*_testnet.log",
        "*_performance.log",
        "debug_*.log",
        "temp_*.log"
    ]
    
    # Répertoires à nettoyer
    cache_dirs = [
        "__pycache__",
        ".pytest_cache",
        "*.egg-info",
        ".coverage"
    ]
    
    files_removed = 0
    
    # Supprimer les fichiers temporaires
    print("🗑️ Suppression des fichiers temporaires...")
    for pattern in temp_patterns:
        for file_path in glob.glob(pattern):
            # Ne pas supprimer les fichiers dans tests/
            if not file_path.startswith("tests/"):
                try:
                    os.remove(file_path)
                    print(f"  ✅ Supprimé: {file_path}")
                    files_removed += 1
                except Exception as e:
                    print(f"  ❌ Erreur: {file_path} - {e}")
    
    # Supprimer les logs temporaires
    print("📝 Suppression des logs temporaires...")
    for pattern in log_patterns:
        for file_path in glob.glob(pattern):
            try:
                os.remove(file_path)
                print(f"  ✅ Supprimé: {file_path}")
                files_removed += 1
            except Exception as e:
                print(f"  ❌ Erreur: {file_path} - {e}")
    
    # Nettoyer les caches Python
    print("🐍 Nettoyage des caches Python...")
    for root, dirs, files in os.walk("."):
        for dir_name in dirs[:]:  # Copie pour modification sûre
            if dir_name == "__pycache__":
                cache_path = Path(root) / dir_name
                try:
                    import shutil
                    shutil.rmtree(cache_path)
                    print(f"  ✅ Supprimé: {cache_path}")
                    files_removed += 1
                except Exception as e:
                    print(f"  ❌ Erreur: {cache_path} - {e}")
    
    print(f"\n✅ NETTOYAGE TERMINÉ")
    print(f"📊 {files_removed} fichiers/dossiers supprimés")
    
    # Afficher l'état final
    print("\n📋 FICHIERS PRINCIPAUX CONSERVÉS:")
    important_files = [
        "launcher.py",
        "requirements.txt", 
        "bots/safe_bot.py",
        "bots/optimized_safe_bot.py",
        ".env.local"
    ]
    
    for file_path in important_files:
        if os.path.exists(file_path):
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path} (manquant)")

def main():
    """Fonction principale"""
    cleanup_project()

if __name__ == "__main__":
    main()
