# 📦 Archive des Versions Précédentes

Ce dossier contient les fichiers des versions antérieures du projet, conservés pour référence historique.

## 📁 Structure

```
archive/
├── v1/                 # Version 1.0 du projet
│   └── launcher.py     # Ancien launcher général
└── README.md           # Ce fichier
```

## 🗂️ Contenu des Archives

### 📂 v1/ - Version 1.0
Contient les fichiers de la première version du système de lancement :

- **`launcher.py`** - Ancien launcher général qui gérait tous les bots
  - Fonctionnalités : Lancement de safe_bot.py, sniper_bot_v2.py, grid_bot_v2.py
  - Remplacé par : Le nouveau `launcher.py` à la racine + interfaces spécialisées
  - Raison de l'archivage : Interface obsolète, remplacée par un système plus modulaire

## 🔄 Évolution du Projet

### Version 1.0 → Version 2.0

**Changements principaux :**
- ✅ **Modularisation** : Séparation des interfaces par fonction
- ✅ **Spécialisation** : Scripts dédiés pour chaque type d'usage
- ✅ **Organisation** : Dossier `scripts/` pour les lanceurs
- ✅ **Fonctionnalités** : Support Telegram, tests longue durée
- ✅ **Simplicité** : Interface rapide vs interface avancée

**Fichiers migrés :**
- `launcher.py` (v1) → `launcher.py` (v2) + `scripts/quick_test.py` + `scripts/run_safe_bot.py`

## 🚫 Utilisation

**⚠️ Les fichiers archivés ne doivent PAS être utilisés en production.**

Ils sont conservés uniquement pour :
- 📚 Référence historique
- 🔍 Comparaison des versions
- 🛠️ Récupération de code spécifique si nécessaire

## 🗑️ Politique de Nettoyage

Les archives sont conservées selon cette politique :
- **v1** : Conservée indéfiniment (version de référence)
- **Versions futures** : Conservées pendant 6 mois après remplacement
- **Nettoyage automatique** : Versions > 1 an supprimées automatiquement

---

**💡 Pour utiliser la version actuelle, utilisez les fichiers à la racine du projet et dans le dossier `scripts/`.**
