aiodns==3.2.0
aiohappyeyeballs==2.4.6
aiohttp==3.10.11
aiosignal==1.3.2
annotated-types==0.7.0
anyio==4.8.0
attrs==23.2.0
backtrader==1.9.78.123
beautifulsoup4==4.12.3
bitarray==3.1.0
blinker==1.8.2
cachetools==5.5.1
ccxt==4.4.63
certifi==2024.7.4
cffi==1.16.0
charset-normalizer==3.3.2
ckzg==2.0.1
click==8.1.7
colorama==0.4.6
contourpy==1.3.1
cryptography==44.0.1
cycler==0.12.1
cytoolz==1.0.1
defusedxml==0.7.1
distro==1.9.0
eth-account==0.13.5
eth-hash==0.7.1
eth-keyfile==0.8.1
eth-keys==0.6.1
eth-rlp==2.2.0
eth-typing==5.2.0
eth-utils==5.2.0
eth_abi==5.2.0
Flask==3.0.3
fonttools==4.56.0
frozenlist==1.5.0
google-api-core==2.24.1
google-api-python-client==2.108.0
google-auth==2.38.0
google-auth-httplib2==0.2.0
googleapis-common-protos==1.67.0
h11==0.14.0
hexbytes==1.3.0
httpcore==1.0.7
httplib2==0.22.0
httpx==0.28.1
idna==3.7
itsdangerous==2.2.0
Jinja2==3.1.4
jiter==0.8.2
kiwisolver==1.4.8
MarkupSafe==2.1.5
matplotlib==3.10.0
multidict==6.1.0
numpy==2.0.0
plotly==5.17.0
seaborn==0.12.2
openai==1.63.2
outcome==1.3.0.post0
packaging==24.2
pandas==2.2.2
parsimonious==0.10.0
pillow==11.1.0
propcache==0.3.0
proto-plus==1.26.0
protobuf==5.29.3
pyasn1==0.6.1
pyasn1_modules==0.4.1
pycares==4.5.0
pycparser==2.22
pycryptodome==3.21.0
pydantic==2.10.6
pydantic_core==2.27.2
pyparsing==3.2.1
PySocks==1.7.1
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
pytz==2024.1
pyunormalize==16.0.0
pywin32==308
regex==2024.11.6
requests==2.32.3
rlp==4.1.0
rsa==4.9
selenium==4.22.0
setuptools==75.8.2
six==1.16.0
sniffio==1.3.1
sortedcontainers==2.4.0
soupsieve==2.6
toolz==1.0.0
tqdm==4.67.1
trio==0.26.0
trio-websocket==0.11.1
types-requests==2.32.0.20241016
typing_extensions==4.12.2
tzdata==2024.1
uritemplate==4.1.1
urllib3==2.2.2
web3==7.8.0
websocket-client==1.8.0
websockets==13.1
Werkzeug==3.0.4
wheel==0.43.0
wsproto==1.2.0
yarl==1.18.3
youtube-transcript-api==0.6.1

# ===== ADDITIONAL DEPENDENCIES FOR BOTCRYPTO =====

# API et Web Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Base de données
sqlalchemy==2.0.23
alembic==1.13.1
psycopg2-binary==2.9.9
redis==5.0.1

# Calculs et analyse
scipy==1.11.4
ta-lib==0.4.28
pandas-ta==0.3.14b0
scikit-learn==1.3.2

# Logging et monitoring
structlog==23.2.0
python-json-logger==2.0.7
prometheus-client==0.19.0
psutil==5.9.6

# Notifications
python-telegram-bot==20.7

# Configuration et utilitaires
pyyaml==6.0.1
toml==0.10.2
marshmallow==3.20.2
cerberus==1.3.5

# Tests et développement
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0
flake8==6.1.0
black==23.11.0
isort==5.12.0
mypy==1.7.1

# Production
gunicorn==21.2.0
hypercorn==0.15.0

# Optionnel
openpyxl==3.1.2
xlsxwriter==3.1.9
lz4==4.3.2
zstandard==0.22.0
