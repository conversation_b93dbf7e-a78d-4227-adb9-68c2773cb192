#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 Configurateur de Mode Test
Ajuste automatiquement les paramètres pour différents modes de test
"""

import os
from pathlib import Path

def configure_aggressive_test():
    """Configure le mode test agressif (plus de trades)"""
    print("🔧 Configuration du mode TEST AGRESSIF...")
    
    config = """# Configuration optimisée pour rentabilité maximale
GRID_SIZE=15                 # Augmentation pour plus d'opportunités
GRID_SPACING=50             # Réduction pour des trades plus fréquents
ORDER_SIZE=0.002            # Augmentation du volume par trade
CAPITAL_MAX=1000           # Inchangé pour la gestion du risque
STOP_LOSS_PERCENT=2        # Plus serré pour limiter les pertes
TAKE_PROFIT_PERCENT=3      # Plus petit mais plus fréquent

# Nouvelles métriques de trading avancées (MODE TEST AGRESSIF)
MIN_VOLUME_24H=50000        # Volume minimum très bas pour testnet
MIN_PRICE_CHANGE=0.05       # Variation minimum très faible
MAX_SPREAD=2.0              # Spread maximum très permissif
MIN_PROFIT_THRESHOLD=0.01   # Profit minimum très bas

# Configuration de logging optimisée
LOG_ROTATION_SIZE=10        # Taille max des logs en MB
LOG_RETENTION_DAYS=7        # Rétention des logs en jours
DETAILED_LOGGING=true       # Logging détaillé pour analyse

# Paramètres d'optimisation automatique
AUTO_OPTIMIZATION=true      # Optimisation automatique des paramètres
OPTIMIZATION_INTERVAL=3600  # Intervalle d'optimisation en secondes (1h)
PERFORMANCE_THRESHOLD=0.1   # Seuil de performance pour ajustement

# Mode de test avancé
TEST_DURATION_HOURS=168     # Durée de test (1 semaine = 168h)
PERFORMANCE_TRACKING=true   # Suivi de performance en temps réel
STRATEGY_VALIDATION=true    # Validation automatique des stratégies"""
    
    return config

def configure_conservative_test():
    """Configure le mode test conservateur (sécurisé)"""
    print("🔧 Configuration du mode TEST CONSERVATEUR...")
    
    config = """# Configuration optimisée pour rentabilité maximale
GRID_SIZE=15                 # Augmentation pour plus d'opportunités
GRID_SPACING=50             # Réduction pour des trades plus fréquents
ORDER_SIZE=0.002            # Augmentation du volume par trade
CAPITAL_MAX=1000           # Inchangé pour la gestion du risque
STOP_LOSS_PERCENT=2        # Plus serré pour limiter les pertes
TAKE_PROFIT_PERCENT=3      # Plus petit mais plus fréquent

# Nouvelles métriques de trading avancées (MODE CONSERVATEUR)
MIN_VOLUME_24H=500000       # Volume minimum modéré
MIN_PRICE_CHANGE=0.2        # Variation minimum modérée
MAX_SPREAD=0.5              # Spread maximum modéré
MIN_PROFIT_THRESHOLD=0.1    # Profit minimum modéré

# Configuration de logging optimisée
LOG_ROTATION_SIZE=10        # Taille max des logs en MB
LOG_RETENTION_DAYS=7        # Rétention des logs en jours
DETAILED_LOGGING=true       # Logging détaillé pour analyse

# Paramètres d'optimisation automatique
AUTO_OPTIMIZATION=true      # Optimisation automatique des paramètres
OPTIMIZATION_INTERVAL=3600  # Intervalle d'optimisation en secondes (1h)
PERFORMANCE_THRESHOLD=0.1   # Seuil de performance pour ajustement

# Mode de test avancé
TEST_DURATION_HOURS=168     # Durée de test (1 semaine = 168h)
PERFORMANCE_TRACKING=true   # Suivi de performance en temps réel
STRATEGY_VALIDATION=true    # Validation automatique des stratégies"""
    
    return config

def configure_production():
    """Configure le mode production (très sécurisé)"""
    print("🔧 Configuration du mode PRODUCTION...")
    
    config = """# Configuration optimisée pour rentabilité maximale
GRID_SIZE=15                 # Augmentation pour plus d'opportunités
GRID_SPACING=50             # Réduction pour des trades plus fréquents
ORDER_SIZE=0.002            # Augmentation du volume par trade
CAPITAL_MAX=1000           # Inchangé pour la gestion du risque
STOP_LOSS_PERCENT=2        # Plus serré pour limiter les pertes
TAKE_PROFIT_PERCENT=3      # Plus petit mais plus fréquent

# Nouvelles métriques de trading avancées (MODE PRODUCTION)
MIN_VOLUME_24H=1000000      # Volume minimum élevé pour sécurité
MIN_PRICE_CHANGE=0.5        # Variation minimum élevée
MAX_SPREAD=0.1              # Spread maximum très strict
MIN_PROFIT_THRESHOLD=0.2    # Profit minimum élevé

# Configuration de logging optimisée
LOG_ROTATION_SIZE=10        # Taille max des logs en MB
LOG_RETENTION_DAYS=7        # Rétention des logs en jours
DETAILED_LOGGING=true       # Logging détaillé pour analyse

# Paramètres d'optimisation automatique
AUTO_OPTIMIZATION=true      # Optimisation automatique des paramètres
OPTIMIZATION_INTERVAL=3600  # Intervalle d'optimisation en secondes (1h)
PERFORMANCE_THRESHOLD=0.1   # Seuil de performance pour ajustement

# Mode de test avancé
TEST_DURATION_HOURS=168     # Durée de test (1 semaine = 168h)
PERFORMANCE_TRACKING=true   # Suivi de performance en temps réel
STRATEGY_VALIDATION=true    # Validation automatique des stratégies"""
    
    return config

def update_env_file(new_config):
    """Met à jour le fichier .env.local avec la nouvelle configuration"""
    env_file = Path(".env.local")
    
    # Lire le fichier existant
    with open(env_file, 'r') as f:
        lines = f.readlines()
    
    # Trouver où commencent les paramètres de trading
    start_index = -1
    for i, line in enumerate(lines):
        if "Configuration optimisée pour rentabilité maximale" in line:
            start_index = i
            break
    
    if start_index == -1:
        print("❌ Section de configuration non trouvée")
        return False
    
    # Garder la partie avant (clés API, etc.)
    header = lines[:start_index]
    
    # Écrire le nouveau fichier
    with open(env_file, 'w') as f:
        f.writelines(header)
        f.write(new_config)
    
    print("✅ Configuration mise à jour")
    return True

def main():
    """Fonction principale"""
    print("🔧 CONFIGURATEUR DE MODE TEST")
    print("=" * 40)
    print("1. 🚀 Mode Test Agressif (plus de trades)")
    print("2. 🛡️ Mode Test Conservateur (sécurisé)")
    print("3. 🏭 Mode Production (très sécurisé)")
    print("4. 📊 Voir la configuration actuelle")
    print("0. 🚪 Quitter")
    
    choice = input("\nVotre choix: ").strip()
    
    if choice == '1':
        config = configure_aggressive_test()
        if update_env_file(config):
            print("\n✅ Mode TEST AGRESSIF configuré!")
            print("💡 Le bot va maintenant trader plus fréquemment")
            print("🧪 Relancez votre test: python quick_test.py --test-1h")
    
    elif choice == '2':
        config = configure_conservative_test()
        if update_env_file(config):
            print("\n✅ Mode TEST CONSERVATEUR configuré!")
            print("🛡️ Le bot va trader de manière plus sécurisée")
            print("🧪 Relancez votre test: python quick_test.py --test-1h")
    
    elif choice == '3':
        config = configure_production()
        if update_env_file(config):
            print("\n✅ Mode PRODUCTION configuré!")
            print("🏭 Configuration très sécurisée pour l'argent réel")
            print("⚠️ À utiliser uniquement après validation en testnet")
    
    elif choice == '4':
        print("\n📊 Configuration actuelle:")
        env_file = Path(".env.local")
        if env_file.exists():
            with open(env_file, 'r') as f:
                lines = f.readlines()
            
            # Afficher les paramètres de trading
            in_trading_section = False
            for line in lines:
                if "Configuration optimisée" in line:
                    in_trading_section = True
                
                if in_trading_section and line.strip():
                    if line.startswith('#') or '=' in line:
                        print(f"   {line.strip()}")
        else:
            print("❌ Fichier .env.local non trouvé")
    
    elif choice == '0':
        print("👋 Au revoir!")
    
    else:
        print("❌ Choix invalide")

if __name__ == "__main__":
    main()
