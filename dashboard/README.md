# 📊 Dashboard de Monitoring Avancé

## 🎯 Vue d'ensemble

Dashboard web moderne pour surveiller les bots de trading en temps réel. Interface intuitive avec métriques détaillées, alertes intelligentes et analytics de performance.

## 🏗️ Architecture

```
dashboard/
├── __init__.py                  # Module principal
├── metrics_collector.py         # Collecte de métriques
├── alert_system.py             # Système d'alertes intelligent
├── web_server.py               # Serveur web et API REST
├── dashboard_manager.py        # Gestionnaire principal
└── README.md                   # Cette documentation
```

## 🚀 Fonctionnalités

### Interface Web Moderne
- **Dashboard temps réel** avec graphiques interactifs
- **WebSocket** pour mises à jour automatiques
- **Design responsive** adaptatif mobile/desktop
- **Thème moderne** avec animations fluides

### Collecte de Métriques
- **Monitoring multi-bots** en temps réel
- **Métriques système** (CPU, mémoire, disque)
- **Métriques de trading** (P&L, trades, win rate)
- **Historique 24h** avec séries temporelles

### Système d'Alertes
- **Détection automatique** d'anomalies
- **Règles configurables** par type et seuil
- **Notifications multi-canal** (Telegram, Email)
- **Niveaux d'urgence** (Info, Warning, Critical, Emergency)

### API REST Complète
- **Endpoints RESTful** pour toutes les données
- **Documentation automatique** avec FastAPI
- **Authentification** et sécurité
- **Rate limiting** et cache intelligent

## 📊 Métriques Collectées

### Métriques Système

| Métrique | Description | Fréquence |
|----------|-------------|-----------|
| **Portfolio Value** | Valeur totale du portefeuille | 30s |
| **Daily P&L** | Profit/Perte quotidien | 30s |
| **System Health** | Score de santé global | 30s |
| **CPU Usage** | Utilisation processeur | 30s |
| **Memory Usage** | Utilisation mémoire | 30s |
| **Disk Usage** | Utilisation disque | 30s |

### Métriques par Bot

| Métrique | Description | Utilisation |
|----------|-------------|-------------|
| **Total Trades** | Nombre total de trades | Performance |
| **Win Rate** | Pourcentage de trades gagnants | Qualité |
| **Total P&L** | Profit/Perte total | Rentabilité |
| **Volume** | Volume total tradé | Activité |
| **Execution Time** | Temps d'exécution moyen | Efficacité |
| **Error Rate** | Taux d'erreur | Fiabilité |
| **Uptime** | Temps de fonctionnement | Disponibilité |

## 🛠️ Installation et Configuration

### 1. Dépendances

```bash
# Installer les dépendances
pip install fastapi uvicorn websockets psutil

# Optionnel pour les graphiques avancés
pip install plotly pandas numpy
```

### 2. Démarrage Rapide

```python
from dashboard.dashboard_manager import DashboardManager

# Créer le dashboard
dashboard = DashboardManager(host="0.0.0.0", port=8080)

# Enregistrer vos bots
dashboard.register_bot("my_bot_1", "dex_scalping", bot_instance)
dashboard.register_bot("my_bot_2", "arbitrage", bot_instance)

# Démarrer le dashboard
await dashboard.start()
```

### 3. Configuration Avancée

```python
# Configuration personnalisée
dashboard = DashboardManager(host="127.0.0.1", port=8080)

# Ajouter des règles d'alerte personnalisées
from dashboard.alert_system import AlertRule, AlertLevel, AlertType

custom_rule = AlertRule(
    id="custom_profit_alert",
    name="Profit élevé détecté",
    description="Profit supérieur à $1000",
    alert_type=AlertType.PERFORMANCE,
    level=AlertLevel.INFO,
    condition=lambda bot: bot.total_pnl > 1000,
    threshold=1000,
    notification_channels=["telegram"]
)

dashboard.alert_system.add_rule(custom_rule)
```

## 🌐 Interface Web

### Page d'Accueil

L'interface principale affiche :

- **Barre de statut** : Portfolio, P&L, bots actifs, santé système
- **Liste des bots** : Statut, performances, métriques clés
- **Alertes actives** : Notifications importantes
- **Graphiques** : Performance portefeuille et P&L par bot

### Fonctionnalités Interactives

- **Actualisation automatique** toutes les 10 secondes
- **Graphiques temps réel** avec Chart.js
- **Filtrage des alertes** par niveau et type
- **Détails des bots** au clic
- **Acquittement d'alertes** en un clic

## 📡 API REST

### Endpoints Principaux

```bash
# Santé de l'API
GET /api/health

# Résumé global
GET /api/summary

# Liste des bots
GET /api/bots

# Détails d'un bot
GET /api/bots/{bot_id}

# Métriques système
GET /api/system

# Historique d'une métrique
GET /api/metrics/{metric_name}?hours=24

# Analytics de performance
GET /api/analytics?hours=24

# Alertes actives
GET /api/alerts

# Acquitter une alerte
POST /api/alerts/{alert_id}/acknowledge
```

### WebSocket

```javascript
// Connexion WebSocket pour mises à jour temps réel
const ws = new WebSocket('ws://localhost:8080/ws');

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    updateDashboard(data);
};
```

## 🚨 Système d'Alertes

### Règles par Défaut

| Règle | Condition | Niveau | Cooldown |
|-------|-----------|--------|----------|
| **Santé système critique** | Score < 30% | Critical | 15 min |
| **CPU élevé** | Usage > 90% | Warning | 10 min |
| **Mémoire élevée** | Usage > 85% | Warning | 10 min |
| **Bot arrêté** | Status != running | Warning | 5 min |
| **Taux d'erreur élevé** | Error rate > 20% | Warning | 20 min |
| **Performance dégradée** | Win rate < 40% | Warning | 60 min |
| **Perte importante** | Daily P&L < -$1000 | Warning | 15 min |

### Configuration des Notifications

```python
# Configuration Telegram
from utils.notifications import notifier

await notifier.configure_telegram(
    bot_token="YOUR_BOT_TOKEN",
    chat_id="YOUR_CHAT_ID"
)

# Configuration Email
await notifier.configure_email(
    smtp_server="smtp.gmail.com",
    smtp_port=587,
    username="<EMAIL>",
    password="your_password",
    to_email="<EMAIL>"
)
```

### Alertes Personnalisées

```python
# Créer une règle personnalisée
custom_rule = AlertRule(
    id="high_volume_alert",
    name="Volume élevé détecté",
    description="Volume de trading supérieur à $100k",
    alert_type=AlertType.PERFORMANCE,
    level=AlertLevel.INFO,
    condition=lambda bot: bot.total_volume > 100000,
    threshold=100000,
    cooldown_minutes=30,
    notification_channels=["telegram", "email"]
)

dashboard.alert_system.add_rule(custom_rule)
```

## 📈 Analytics et Reporting

### Métriques de Performance

```python
# Récupérer les analytics
analytics = dashboard.metrics_collector.get_performance_analytics(hours=24)

print(f"Portfolio Performance:")
print(f"  Current Value: ${analytics['portfolio']['current_value']:,.2f}")
print(f"  Change: {analytics['portfolio']['change_percent']:+.2f}%")

print(f"Bot Performance:")
for bot_id, bot_data in analytics['bots'].items():
    print(f"  {bot_id}: ${bot_data['performance']['total_pnl']:,.2f}")
```

### Exportation des Données

```python
# Exporter les métriques
import json
from datetime import datetime

# Récupérer toutes les métriques
summary = dashboard.get_performance_summary()

# Sauvegarder en JSON
with open(f"metrics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json", 'w') as f:
    json.dump(summary, f, indent=2)
```

## 🔧 Configuration Avancée

### Personnalisation des Métriques

```python
# Ajouter des métriques personnalisées
def custom_metric_calculator(bot_metrics):
    # Calculer une métrique personnalisée
    return sum(bot.total_pnl for bot in bot_metrics.values())

# Enregistrer la métrique
dashboard.metrics_collector.custom_metrics['total_portfolio_pnl'] = custom_metric_calculator
```

### Thème Personnalisé

```css
/* Personnaliser l'apparence */
:root {
    --primary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --background-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
```

### Sécurité

```python
# Ajouter l'authentification
from fastapi.security import HTTPBearer
from fastapi import Depends

security = HTTPBearer()

@app.get("/api/protected")
async def protected_endpoint(token: str = Depends(security)):
    # Vérifier le token
    if not verify_token(token):
        raise HTTPException(status_code=401, detail="Token invalide")
    
    return {"message": "Accès autorisé"}
```

## 🧪 Tests et Validation

### Tests Unitaires

```bash
# Tester les composants
python -m pytest tests/test_metrics_collector.py
python -m pytest tests/test_alert_system.py
python -m pytest tests/test_web_server.py
```

### Simulation

```bash
# Lancer l'exemple complet
python examples/dashboard_example.py

# Démarrer le dashboard seul
python dashboard/dashboard_manager.py --host 0.0.0.0 --port 8080
```

### Load Testing

```bash
# Tester la charge avec Apache Bench
ab -n 1000 -c 10 http://localhost:8080/api/summary

# Tester les WebSockets
python tests/websocket_load_test.py
```

## 📊 Monitoring et Maintenance

### Logs du Dashboard

```bash
# Logs en temps réel
tail -f logs/dashboard/dashboard.log

# Logs des métriques
tail -f logs/dashboard/metrics.log

# Logs des alertes
tail -f logs/dashboard/alerts.log
```

### Métriques de Performance

```python
# Surveiller les performances du dashboard
dashboard_stats = dashboard.get_dashboard_status()

print(f"Uptime: {dashboard_stats['uptime_seconds']} seconds")
print(f"WebSocket connections: {dashboard_stats['websocket_connections']}")
print(f"Memory usage: {dashboard_stats['memory_usage']}%")
```

### Backup et Restauration

```python
# Sauvegarder les métriques
backup_data = {
    'timestamp': datetime.now().isoformat(),
    'bot_metrics': dashboard.metrics_collector.get_bot_metrics(),
    'system_metrics': dashboard.metrics_collector.get_system_metrics(),
    'alert_history': list(dashboard.alert_system.alert_history)
}

with open('dashboard_backup.json', 'w') as f:
    json.dump(backup_data, f, default=str, indent=2)
```

## ⚠️ Limitations et Considérations

### Limitations Techniques

- **Rétention des données** : 24h par défaut (configurable)
- **Nombre de bots** : Optimisé pour 50 bots maximum
- **Fréquence de collecte** : 30 secondes minimum
- **WebSocket connections** : 100 connexions simultanées

### Considérations de Performance

- **Mémoire** : ~100MB pour 10 bots avec historique 24h
- **CPU** : <5% d'utilisation en continu
- **Réseau** : ~1KB/s par connexion WebSocket
- **Stockage** : ~10MB/jour de logs et métriques

### Sécurité

- **Exposition publique** : Utiliser HTTPS en production
- **Authentification** : Implémenter selon vos besoins
- **Rate limiting** : Configuré par défaut
- **Validation des données** : Toutes les entrées sont validées

## 📞 Support et Dépannage

### Problèmes Courants

```bash
# Port déjà utilisé
ERROR: [Errno 98] Address already in use
# Solution: Changer le port ou arrêter le processus

# Dépendances manquantes
ModuleNotFoundError: No module named 'fastapi'
# Solution: pip install fastapi uvicorn

# Permissions insuffisantes
PermissionError: [Errno 13] Permission denied
# Solution: Vérifier les permissions du répertoire
```

### Debug Mode

```python
# Activer le mode debug
dashboard = DashboardManager(host="127.0.0.1", port=8080)
dashboard.web_server.app.debug = True

# Logs détaillés
import logging
logging.getLogger("dashboard").setLevel(logging.DEBUG)
```

---

**💡 Conseil** : Le dashboard est conçu pour être léger et performant. Commencez avec la configuration par défaut et personnalisez selon vos besoins spécifiques.
