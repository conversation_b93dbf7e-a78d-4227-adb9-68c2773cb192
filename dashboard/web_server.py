"""
🌐 Serveur web pour le dashboard de monitoring
API REST et interface web pour visualiser les métriques en temps réel
"""

import asyncio
import json
import time
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from pathlib import Path
import sys
import logging

# Imports FastAPI
try:
    from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect
    from fastapi.staticfiles import StaticFiles
    from fastapi.responses import HTMLResponse, JSONResponse
    from fastapi.middleware.cors import CORSMiddleware
    import uvicorn
except ImportError:
    print("⚠️ FastAPI non installé. Installez avec: pip install fastapi uvicorn websockets")
    sys.exit(1)

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from dashboard.metrics_collector import MetricsCollector
from utils.error_handler import error_handler, ErrorCategory, ErrorSeverity
from logging_system.central_logger import central_logger, LogCategory

class DashboardWebServer:
    """Serveur web pour le dashboard de monitoring"""
    
    def __init__(self, metrics_collector: MetricsCollector, host: str = "0.0.0.0", port: int = 8080):
        self.logger = logging.getLogger(__name__)
        self.metrics_collector = metrics_collector
        self.host = host
        self.port = port
        
        # Créer l'application FastAPI
        self.app = FastAPI(
            title="Bot Trading Dashboard",
            description="Dashboard de monitoring pour les bots de trading",
            version="1.0.0"
        )
        
        # Configuration CORS
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # Connexions WebSocket actives
        self.websocket_connections: List[WebSocket] = []
        
        # Configurer les routes
        self._setup_routes()
        
        central_logger.log(
            level="INFO",
            message="Serveur web dashboard initialisé",
            category=LogCategory.SYSTEM,
            host=host,
            port=port
        )
    
    def _setup_routes(self):
        """Configure les routes de l'API"""
        
        @self.app.get("/", response_class=HTMLResponse)
        async def dashboard_home():
            """Page d'accueil du dashboard"""
            return self._get_dashboard_html()
        
        @self.app.get("/api/health")
        async def health_check():
            """Vérification de santé de l'API"""
            return {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "version": "1.0.0"
            }
        
        @self.app.get("/api/summary")
        async def get_summary():
            """Résumé global du dashboard"""
            try:
                summary = self.metrics_collector.get_dashboard_summary()
                return JSONResponse(content=summary)
            except Exception as e:
                error_handler.handle_error(e, ErrorCategory.API, ErrorSeverity.MEDIUM, {
                    'endpoint': '/api/summary'
                })
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/api/bots")
        async def get_bots():
            """Liste de tous les bots et leurs métriques"""
            try:
                bot_metrics = self.metrics_collector.get_bot_metrics()
                
                # Convertir en format JSON sérialisable
                bots_data = {}
                for bot_id, metrics in bot_metrics.items():
                    bots_data[bot_id] = {
                        'bot_id': metrics.bot_id,
                        'bot_type': metrics.bot_type,
                        'status': metrics.status,
                        'uptime_seconds': metrics.uptime_seconds,
                        'total_trades': metrics.total_trades,
                        'successful_trades': metrics.successful_trades,
                        'win_rate': metrics.win_rate,
                        'total_pnl': metrics.total_pnl,
                        'daily_pnl': metrics.daily_pnl,
                        'total_volume': metrics.total_volume,
                        'current_drawdown': metrics.current_drawdown,
                        'max_drawdown': metrics.max_drawdown,
                        'avg_execution_time': metrics.avg_execution_time,
                        'error_rate': metrics.error_rate,
                        'last_updated': metrics.last_updated.isoformat()
                    }
                
                return JSONResponse(content=bots_data)
                
            except Exception as e:
                error_handler.handle_error(e, ErrorCategory.API, ErrorSeverity.MEDIUM, {
                    'endpoint': '/api/bots'
                })
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/api/bots/{bot_id}")
        async def get_bot_details(bot_id: str):
            """Détails d'un bot spécifique"""
            try:
                bot_metrics = self.metrics_collector.get_bot_metrics(bot_id)
                
                if not bot_metrics:
                    raise HTTPException(status_code=404, detail=f"Bot {bot_id} non trouvé")
                
                # Ajouter l'historique des métriques
                pnl_history = self.metrics_collector.get_metric_history(f"{bot_id}_pnl", hours=24)
                trades_history = self.metrics_collector.get_metric_history(f"{bot_id}_trades", hours=24)
                
                bot_data = {
                    'metrics': {
                        'bot_id': bot_metrics.bot_id,
                        'bot_type': bot_metrics.bot_type,
                        'status': bot_metrics.status,
                        'uptime_seconds': bot_metrics.uptime_seconds,
                        'total_trades': bot_metrics.total_trades,
                        'successful_trades': bot_metrics.successful_trades,
                        'win_rate': bot_metrics.win_rate,
                        'total_pnl': bot_metrics.total_pnl,
                        'daily_pnl': bot_metrics.daily_pnl,
                        'total_volume': bot_metrics.total_volume,
                        'current_drawdown': bot_metrics.current_drawdown,
                        'max_drawdown': bot_metrics.max_drawdown,
                        'sharpe_ratio': bot_metrics.sharpe_ratio,
                        'avg_execution_time': bot_metrics.avg_execution_time,
                        'error_rate': bot_metrics.error_rate,
                        'last_error': bot_metrics.last_error,
                        'last_updated': bot_metrics.last_updated.isoformat()
                    },
                    'history': {
                        'pnl': [{'timestamp': p.timestamp.isoformat(), 'value': p.value} for p in pnl_history],
                        'trades': [{'timestamp': p.timestamp.isoformat(), 'value': p.value} for p in trades_history]
                    }
                }
                
                return JSONResponse(content=bot_data)
                
            except HTTPException:
                raise
            except Exception as e:
                error_handler.handle_error(e, ErrorCategory.API, ErrorSeverity.MEDIUM, {
                    'endpoint': f'/api/bots/{bot_id}'
                })
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/api/system")
        async def get_system_metrics():
            """Métriques système globales"""
            try:
                system_metrics = self.metrics_collector.get_system_metrics()
                
                if not system_metrics:
                    return JSONResponse(content={'error': 'Aucune métrique système disponible'})
                
                system_data = {
                    'timestamp': system_metrics.timestamp.isoformat(),
                    'total_portfolio_value': system_metrics.total_portfolio_value,
                    'total_daily_pnl': system_metrics.total_daily_pnl,
                    'total_unrealized_pnl': system_metrics.total_unrealized_pnl,
                    'active_bots': system_metrics.active_bots,
                    'total_active_positions': system_metrics.total_active_positions,
                    'total_daily_trades': system_metrics.total_daily_trades,
                    'system_health_score': system_metrics.system_health_score,
                    'avg_response_time': system_metrics.avg_response_time,
                    'error_rate': system_metrics.error_rate,
                    'cpu_usage': system_metrics.cpu_usage,
                    'memory_usage': system_metrics.memory_usage,
                    'disk_usage': system_metrics.disk_usage
                }
                
                return JSONResponse(content=system_data)
                
            except Exception as e:
                error_handler.handle_error(e, ErrorCategory.API, ErrorSeverity.MEDIUM, {
                    'endpoint': '/api/system'
                })
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/api/metrics/{metric_name}")
        async def get_metric_history(metric_name: str, hours: int = 1):
            """Historique d'une métrique spécifique"""
            try:
                history = self.metrics_collector.get_metric_history(metric_name, hours)
                
                metric_data = {
                    'metric_name': metric_name,
                    'period_hours': hours,
                    'data_points': len(history),
                    'data': [
                        {
                            'timestamp': point.timestamp.isoformat(),
                            'value': point.value,
                            'metadata': point.metadata
                        }
                        for point in history
                    ]
                }
                
                return JSONResponse(content=metric_data)
                
            except Exception as e:
                error_handler.handle_error(e, ErrorCategory.API, ErrorSeverity.MEDIUM, {
                    'endpoint': f'/api/metrics/{metric_name}'
                })
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/api/analytics")
        async def get_analytics(hours: int = 24):
            """Analytics de performance"""
            try:
                analytics = self.metrics_collector.get_performance_analytics(hours)
                return JSONResponse(content=analytics)

            except Exception as e:
                error_handler.handle_error(e, ErrorCategory.API, ErrorSeverity.MEDIUM, {
                    'endpoint': '/api/analytics'
                })
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.get("/api/alerts")
        async def get_alerts():
            """Récupère les alertes actives"""
            try:
                if hasattr(self, 'alert_system'):
                    alerts = self.alert_system.get_active_alerts()
                    alerts_data = [
                        {
                            'id': alert.id,
                            'rule_id': alert.rule_id,
                            'type': alert.alert_type.value,
                            'level': alert.level.value,
                            'title': alert.title,
                            'message': alert.message,
                            'value': alert.value,
                            'threshold': alert.threshold,
                            'bot_id': alert.bot_id,
                            'timestamp': alert.timestamp.isoformat(),
                            'acknowledged': alert.acknowledged
                        }
                        for alert in alerts
                    ]
                    return JSONResponse(content=alerts_data)
                else:
                    return JSONResponse(content=[])

            except Exception as e:
                error_handler.handle_error(e, ErrorCategory.API, ErrorSeverity.MEDIUM, {
                    'endpoint': '/api/alerts'
                })
                raise HTTPException(status_code=500, detail=str(e))

        @self.app.post("/api/alerts/{alert_id}/acknowledge")
        async def acknowledge_alert(alert_id: str):
            """Acquitte une alerte"""
            try:
                if hasattr(self, 'alert_system'):
                    success = self.alert_system.acknowledge_alert(alert_id)
                    return JSONResponse(content={'success': success})
                else:
                    raise HTTPException(status_code=404, detail="Système d'alertes non disponible")

            except Exception as e:
                error_handler.handle_error(e, ErrorCategory.API, ErrorSeverity.MEDIUM, {
                    'endpoint': f'/api/alerts/{alert_id}/acknowledge'
                })
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.websocket("/ws")
        async def websocket_endpoint(websocket: WebSocket):
            """WebSocket pour les mises à jour en temps réel"""
            await websocket.accept()
            self.websocket_connections.append(websocket)
            
            try:
                while True:
                    # Envoyer les métriques toutes les 5 secondes
                    summary = self.metrics_collector.get_dashboard_summary()
                    await websocket.send_json(summary)
                    await asyncio.sleep(5)
                    
            except WebSocketDisconnect:
                self.websocket_connections.remove(websocket)
            except Exception as e:
                error_handler.handle_error(e, ErrorCategory.API, ErrorSeverity.LOW, {
                    'endpoint': '/ws'
                })
                if websocket in self.websocket_connections:
                    self.websocket_connections.remove(websocket)
    
    def _get_dashboard_html(self) -> str:
        """Génère le HTML du dashboard"""
        return """
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bot Trading Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        .container { max-width: 1400px; margin: 0 auto; padding: 20px; }
        .header { 
            background: rgba(255,255,255,0.95);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .header h1 { 
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-align: center;
        }
        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-top: 15px;
        }
        .status-item {
            text-align: center;
            flex: 1;
        }
        .status-value {
            font-size: 1.8em;
            font-weight: bold;
            color: #27ae60;
        }
        .status-label {
            font-size: 0.9em;
            color: #7f8c8d;
            margin-top: 5px;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .card {
            background: rgba(255,255,255,0.95);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        .card h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.3em;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .bot-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            margin: 10px 0;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #3498db;
        }
        .bot-info h4 {
            color: #2c3e50;
            margin-bottom: 5px;
        }
        .bot-stats {
            display: flex;
            gap: 15px;
            font-size: 0.9em;
        }
        .stat {
            text-align: center;
        }
        .stat-value {
            font-weight: bold;
            color: #27ae60;
        }
        .stat-label {
            color: #7f8c8d;
            font-size: 0.8em;
        }
        .status-running { color: #27ae60; }
        .status-stopped { color: #e74c3c; }
        .chart-container {
            position: relative;
            height: 300px;
            margin-top: 20px;
        }
        .alert {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 12px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .alert.critical {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }
        .refresh-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9em;
        }
        .refresh-btn:hover {
            background: #2980b9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Bot Trading Dashboard</h1>
            <div class="status-bar">
                <div class="status-item">
                    <div class="status-value" id="portfolio-value">$0</div>
                    <div class="status-label">Valeur Portefeuille</div>
                </div>
                <div class="status-item">
                    <div class="status-value" id="daily-pnl">$0</div>
                    <div class="status-label">P&L Quotidien</div>
                </div>
                <div class="status-item">
                    <div class="status-value" id="active-bots">0</div>
                    <div class="status-label">Bots Actifs</div>
                </div>
                <div class="status-item">
                    <div class="status-value" id="health-score">0%</div>
                    <div class="status-label">Santé Système</div>
                </div>
                <div class="status-item">
                    <button class="refresh-btn" onclick="refreshData()">🔄 Actualiser</button>
                </div>
            </div>
        </div>

        <div class="grid">
            <div class="card">
                <h3>📊 Bots de Trading</h3>
                <div id="bots-list" class="loading">Chargement des bots...</div>
            </div>

            <div class="card">
                <h3>⚠️ Alertes</h3>
                <div id="alerts-list" class="loading">Chargement des alertes...</div>
            </div>
        </div>

        <div class="grid">
            <div class="card">
                <h3>📈 Performance Portefeuille</h3>
                <div class="chart-container">
                    <canvas id="portfolio-chart"></canvas>
                </div>
            </div>

            <div class="card">
                <h3>🎯 P&L par Bot</h3>
                <div class="chart-container">
                    <canvas id="pnl-chart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <script>
        let portfolioChart, pnlChart;
        let ws;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            initCharts();
            connectWebSocket();
            refreshData();
        });

        function initCharts() {
            // Graphique du portefeuille
            const portfolioCtx = document.getElementById('portfolio-chart').getContext('2d');
            portfolioChart = new Chart(portfolioCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Valeur Portefeuille',
                        data: [],
                        borderColor: '#3498db',
                        backgroundColor: 'rgba(52, 152, 219, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: false,
                            ticks: {
                                callback: function(value) {
                                    return '$' + value.toLocaleString();
                                }
                            }
                        }
                    }
                }
            });

            // Graphique P&L
            const pnlCtx = document.getElementById('pnl-chart').getContext('2d');
            pnlChart = new Chart(pnlCtx, {
                type: 'bar',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'P&L Total',
                        data: [],
                        backgroundColor: function(context) {
                            const value = context.parsed.y;
                            return value >= 0 ? '#27ae60' : '#e74c3c';
                        }
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            ticks: {
                                callback: function(value) {
                                    return '$' + value.toLocaleString();
                                }
                            }
                        }
                    }
                }
            });
        }

        function connectWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            ws = new WebSocket(`${protocol}//${window.location.host}/ws`);
            
            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                updateDashboard(data);
            };

            ws.onclose = function() {
                setTimeout(connectWebSocket, 5000); // Reconnexion automatique
            };
        }

        async function refreshData() {
            try {
                const response = await fetch('/api/summary');
                const data = await response.json();
                updateDashboard(data);
            } catch (error) {
                console.error('Erreur lors du rafraîchissement:', error);
            }
        }

        function updateDashboard(data) {
            // Mettre à jour la barre de statut
            if (data.system) {
                document.getElementById('portfolio-value').textContent = 
                    '$' + (data.system.portfolio_value || 0).toLocaleString();
                document.getElementById('daily-pnl').textContent = 
                    '$' + (data.system.daily_pnl || 0).toLocaleString();
                document.getElementById('active-bots').textContent = 
                    data.system.active_bots || 0;
                document.getElementById('health-score').textContent = 
                    Math.round((data.system.health_score || 0) * 100) + '%';
            }

            // Mettre à jour la liste des bots
            updateBotsList(data.bots || {});

            // Mettre à jour les alertes
            updateAlerts(data.alerts || []);
        }

        function updateBotsList(bots) {
            const container = document.getElementById('bots-list');
            
            if (Object.keys(bots).length === 0) {
                container.innerHTML = '<div class="loading">Aucun bot configuré</div>';
                return;
            }

            let html = '';
            for (const [botId, bot] of Object.entries(bots)) {
                const statusClass = bot.status === 'running' ? 'status-running' : 'status-stopped';
                const uptimeHours = Math.floor((bot.uptime || 0) / 3600);
                
                html += `
                    <div class="bot-item">
                        <div class="bot-info">
                            <h4>${botId} <span class="${statusClass}">●</span></h4>
                            <div>Type: ${bot.type} | Uptime: ${uptimeHours}h</div>
                        </div>
                        <div class="bot-stats">
                            <div class="stat">
                                <div class="stat-value">${bot.total_trades || 0}</div>
                                <div class="stat-label">Trades</div>
                            </div>
                            <div class="stat">
                                <div class="stat-value">${Math.round(bot.win_rate || 0)}%</div>
                                <div class="stat-label">Win Rate</div>
                            </div>
                            <div class="stat">
                                <div class="stat-value">$${(bot.total_pnl || 0).toLocaleString()}</div>
                                <div class="stat-label">P&L Total</div>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            container.innerHTML = html;

            // Mettre à jour le graphique P&L
            updatePnLChart(bots);
        }

        function updateAlerts(alerts) {
            const container = document.getElementById('alerts-list');
            
            if (alerts.length === 0) {
                container.innerHTML = '<div style="color: #27ae60;">✅ Aucune alerte active</div>';
                return;
            }

            let html = '';
            for (const alert of alerts) {
                const alertClass = alert.level === 'critical' ? 'alert critical' : 'alert';
                html += `
                    <div class="${alertClass}">
                        <strong>${alert.type.toUpperCase()}:</strong> ${alert.message}
                        ${alert.value ? ` (${alert.value})` : ''}
                    </div>
                `;
            }
            
            container.innerHTML = html;
        }

        function updatePnLChart(bots) {
            const labels = [];
            const data = [];
            
            for (const [botId, bot] of Object.entries(bots)) {
                labels.push(botId);
                data.push(bot.total_pnl || 0);
            }
            
            pnlChart.data.labels = labels;
            pnlChart.data.datasets[0].data = data;
            pnlChart.update();
        }

        // Actualisation automatique toutes les 30 secondes
        setInterval(refreshData, 30000);
    </script>
</body>
</html>
        """
    
    async def start_server(self):
        """Démarre le serveur web"""
        try:
            central_logger.log(
                level="INFO",
                message="Démarrage du serveur web dashboard",
                category=LogCategory.SYSTEM,
                host=self.host,
                port=self.port
            )
            
            config = uvicorn.Config(
                app=self.app,
                host=self.host,
                port=self.port,
                log_level="info"
            )
            
            server = uvicorn.Server(config)
            await server.serve()
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.CRITICAL, {
                'function': 'start_server'
            })
    
    async def broadcast_update(self, data: Dict[str, Any]):
        """Diffuse une mise à jour à tous les clients WebSocket"""
        if not self.websocket_connections:
            return
        
        disconnected = []
        for websocket in self.websocket_connections:
            try:
                await websocket.send_json(data)
            except Exception:
                disconnected.append(websocket)
        
        # Nettoyer les connexions fermées
        for websocket in disconnected:
            self.websocket_connections.remove(websocket)
