"""
📊 Gestionnaire principal du dashboard
Coordonne tous les composants du dashboard de monitoring
"""

import asyncio
import time
from typing import Dict, List, Optional, Any
from datetime import datetime
import logging
from pathlib import Path
import sys

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from dashboard.metrics_collector import MetricsCollector
from dashboard.web_server import DashboardWebServer
from dashboard.alert_system import AlertSystem
from utils.error_handler import error_handler, ErrorCategory, ErrorSeverity
from logging_system.central_logger import central_logger, LogCategory

class DashboardManager:
    """Gestionnaire principal du dashboard de monitoring"""
    
    def __init__(self, host: str = "0.0.0.0", port: int = 8080):
        self.logger = logging.getLogger(__name__)
        self.host = host
        self.port = port
        
        # Composants du dashboard
        self.metrics_collector = MetricsCollector()
        self.alert_system = AlertSystem(self.metrics_collector)
        self.web_server = DashboardWebServer(self.metrics_collector, host, port)
        
        # Ajouter le système d'alertes au serveur web
        self.web_server.alert_system = self.alert_system
        
        # État du dashboard
        self.is_running = False
        self.start_time = None
        
        # Tâches asyncio
        self.tasks: List[asyncio.Task] = []
        
        central_logger.log(
            level="INFO",
            message="Gestionnaire dashboard initialisé",
            category=LogCategory.SYSTEM,
            host=host,
            port=port
        )
    
    def register_bot(self, bot_id: str, bot_type: str, bot_instance: Any = None):
        """Enregistre un bot pour le monitoring"""
        try:
            self.metrics_collector.register_bot(bot_id, bot_type, bot_instance)
            
            central_logger.log(
                level="INFO",
                message=f"Bot enregistré dans le dashboard: {bot_id}",
                category=LogCategory.SYSTEM,
                bot_type=bot_type
            )
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.MEDIUM, {
                'function': 'register_bot',
                'bot_id': bot_id
            })
    
    def unregister_bot(self, bot_id: str):
        """Désenregistre un bot"""
        try:
            self.metrics_collector.unregister_bot(bot_id)
            
            central_logger.log(
                level="INFO",
                message=f"Bot désenregistré du dashboard: {bot_id}",
                category=LogCategory.SYSTEM
            )
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.LOW, {
                'function': 'unregister_bot',
                'bot_id': bot_id
            })
    
    async def start(self):
        """Démarre le dashboard complet"""
        if self.is_running:
            self.logger.warning("Le dashboard est déjà en cours d'exécution")
            return
        
        try:
            self.is_running = True
            self.start_time = datetime.now()
            
            central_logger.log(
                level="INFO",
                message="Démarrage du dashboard de monitoring",
                category=LogCategory.SYSTEM,
                host=self.host,
                port=self.port
            )
            
            # Créer les tâches de monitoring
            self.tasks = [
                asyncio.create_task(self.metrics_collector.start_collection()),
                asyncio.create_task(self.alert_system.start_monitoring()),
                asyncio.create_task(self._periodic_broadcast()),
                asyncio.create_task(self.web_server.start_server())
            ]
            
            # Attendre que toutes les tâches se terminent
            await asyncio.gather(*self.tasks)
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.CRITICAL, {
                'function': 'start'
            })
            await self.stop()
    
    async def stop(self):
        """Arrête le dashboard"""
        if not self.is_running:
            return
        
        try:
            self.is_running = False
            
            # Annuler toutes les tâches
            for task in self.tasks:
                if not task.done():
                    task.cancel()
            
            # Attendre que les tâches se terminent
            if self.tasks:
                await asyncio.gather(*self.tasks, return_exceptions=True)
            
            central_logger.log(
                level="INFO",
                message="Dashboard arrêté",
                category=LogCategory.SYSTEM
            )
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.MEDIUM, {
                'function': 'stop'
            })
    
    async def _periodic_broadcast(self):
        """Diffuse périodiquement les mises à jour via WebSocket"""
        while self.is_running:
            try:
                # Récupérer le résumé du dashboard
                summary = self.metrics_collector.get_dashboard_summary()
                
                # Ajouter les alertes actives
                if hasattr(self.alert_system, 'get_active_alerts'):
                    active_alerts = self.alert_system.get_active_alerts()
                    summary['alerts'] = [
                        {
                            'id': alert.id,
                            'type': alert.alert_type.value,
                            'level': alert.level.value,
                            'title': alert.title,
                            'message': alert.message,
                            'bot_id': alert.bot_id,
                            'timestamp': alert.timestamp.isoformat()
                        }
                        for alert in active_alerts[:10]  # Limiter à 10 alertes
                    ]
                
                # Diffuser aux clients WebSocket
                await self.web_server.broadcast_update(summary)
                
                # Attendre 10 secondes avant la prochaine diffusion
                await asyncio.sleep(10)
                
            except Exception as e:
                error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.LOW, {
                    'function': '_periodic_broadcast'
                })
                await asyncio.sleep(30)
    
    def get_dashboard_status(self) -> Dict[str, Any]:
        """Retourne le statut du dashboard"""
        try:
            uptime_seconds = 0
            if self.start_time:
                uptime_seconds = (datetime.now() - self.start_time).total_seconds()
            
            # Statistiques des composants
            metrics_stats = {
                'registered_bots': len(self.metrics_collector.registered_bots),
                'active_metrics': len(self.metrics_collector.bot_metrics)
            }
            
            alert_stats = self.alert_system.get_alert_statistics()
            
            return {
                'is_running': self.is_running,
                'start_time': self.start_time.isoformat() if self.start_time else None,
                'uptime_seconds': uptime_seconds,
                'host': self.host,
                'port': self.port,
                'websocket_connections': len(self.web_server.websocket_connections),
                'metrics_collector': metrics_stats,
                'alert_system': alert_stats,
                'tasks_running': len([t for t in self.tasks if not t.done()])
            }
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.LOW, {
                'function': 'get_dashboard_status'
            })
            return {'error': str(e)}
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Génère un résumé de performance"""
        try:
            # Métriques système
            system_metrics = self.metrics_collector.get_system_metrics()
            
            # Métriques des bots
            bot_metrics = self.metrics_collector.get_bot_metrics()
            
            # Alertes actives
            active_alerts = self.alert_system.get_active_alerts()
            
            # Calculer les totaux
            total_trades = sum(bot.total_trades for bot in bot_metrics.values())
            total_pnl = sum(bot.total_pnl for bot in bot_metrics.values())
            avg_win_rate = (
                sum(bot.win_rate for bot in bot_metrics.values()) / len(bot_metrics)
                if bot_metrics else 0
            )
            
            # Bots par statut
            running_bots = len([bot for bot in bot_metrics.values() if bot.status == "running"])
            stopped_bots = len([bot for bot in bot_metrics.values() if bot.status != "running"])
            
            # Alertes par niveau
            critical_alerts = len([a for a in active_alerts if a.level.value == "critical"])
            warning_alerts = len([a for a in active_alerts if a.level.value == "warning"])
            
            return {
                'timestamp': datetime.now().isoformat(),
                'system': {
                    'portfolio_value': system_metrics.total_portfolio_value if system_metrics else 0,
                    'daily_pnl': system_metrics.total_daily_pnl if system_metrics else 0,
                    'health_score': system_metrics.system_health_score if system_metrics else 0,
                    'cpu_usage': system_metrics.cpu_usage if system_metrics else 0,
                    'memory_usage': system_metrics.memory_usage if system_metrics else 0
                },
                'trading': {
                    'total_bots': len(bot_metrics),
                    'running_bots': running_bots,
                    'stopped_bots': stopped_bots,
                    'total_trades': total_trades,
                    'total_pnl': total_pnl,
                    'average_win_rate': avg_win_rate
                },
                'alerts': {
                    'total_active': len(active_alerts),
                    'critical': critical_alerts,
                    'warning': warning_alerts,
                    'info': len(active_alerts) - critical_alerts - warning_alerts
                },
                'dashboard': {
                    'uptime_hours': (datetime.now() - self.start_time).total_seconds() / 3600 if self.start_time else 0,
                    'websocket_clients': len(self.web_server.websocket_connections),
                    'is_healthy': self.is_running and len(self.tasks) > 0
                }
            }
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.MEDIUM, {
                'function': 'get_performance_summary'
            })
            return {'error': str(e)}
    
    async def send_test_alert(self, level: str = "info", message: str = "Test d'alerte"):
        """Envoie une alerte de test"""
        try:
            from dashboard.alert_system import Alert, AlertLevel, AlertType
            
            # Créer une alerte de test
            alert = Alert(
                id=f"test_{int(time.time())}",
                rule_id="test_rule",
                alert_type=AlertType.SYSTEM,
                level=AlertLevel(level),
                title="Test d'alerte",
                message=message,
                value=None,
                threshold=None,
                bot_id=None,
                timestamp=datetime.now()
            )
            
            # Ajouter aux alertes actives
            self.alert_system.active_alerts[alert.id] = alert
            
            central_logger.log(
                level="INFO",
                message=f"Alerte de test envoyée: {message}",
                category=LogCategory.ALERT,
                level=level
            )
            
            return True
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.LOW, {
                'function': 'send_test_alert'
            })
            return False

# Instance globale du dashboard
dashboard_manager = DashboardManager()

# Fonctions utilitaires
async def start_dashboard(host: str = "0.0.0.0", port: int = 8080):
    """Démarre le dashboard avec la configuration donnée"""
    global dashboard_manager
    
    if dashboard_manager.is_running:
        dashboard_manager.logger.warning("Dashboard déjà en cours d'exécution")
        return dashboard_manager
    
    # Créer une nouvelle instance si nécessaire
    if dashboard_manager.host != host or dashboard_manager.port != port:
        dashboard_manager = DashboardManager(host, port)
    
    await dashboard_manager.start()
    return dashboard_manager

async def stop_dashboard():
    """Arrête le dashboard"""
    global dashboard_manager
    await dashboard_manager.stop()

def register_bot(bot_id: str, bot_type: str, bot_instance: Any = None):
    """Enregistre un bot dans le dashboard"""
    global dashboard_manager
    dashboard_manager.register_bot(bot_id, bot_type, bot_instance)

def unregister_bot(bot_id: str):
    """Désenregistre un bot du dashboard"""
    global dashboard_manager
    dashboard_manager.unregister_bot(bot_id)

def get_dashboard_status() -> Dict[str, Any]:
    """Récupère le statut du dashboard"""
    global dashboard_manager
    return dashboard_manager.get_dashboard_status()

def get_performance_summary() -> Dict[str, Any]:
    """Récupère un résumé de performance"""
    global dashboard_manager
    return dashboard_manager.get_performance_summary()

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Dashboard de monitoring des bots de trading")
    parser.add_argument("--host", default="0.0.0.0", help="Adresse IP d'écoute")
    parser.add_argument("--port", type=int, default=8080, help="Port d'écoute")
    
    args = parser.parse_args()
    
    try:
        asyncio.run(start_dashboard(args.host, args.port))
    except KeyboardInterrupt:
        print("\n🛑 Arrêt du dashboard demandé par l'utilisateur")
        asyncio.run(stop_dashboard())
    except Exception as e:
        print(f"❌ Erreur lors du démarrage du dashboard: {e}")
        sys.exit(1)
