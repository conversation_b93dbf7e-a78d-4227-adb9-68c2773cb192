"""
⚖️ Moteur de gestion des risques intégré
Système central de gestion des risques combinant tous les composants
"""

import logging
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
from pathlib import Path
import sys

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from risk_management.portfolio_manager import portfolio_manager, PositionType, RiskLevel
from risk_management.stop_loss_manager import stop_loss_manager, StopLossConfig, TakeProfitConfig, StopLossType, TakeProfitType
from risk_management.correlation_manager import correlation_manager
from utils.notifications import notifier
from utils.error_handler import error_handler, ErrorCategory, ErrorSeverity

class RiskAction(Enum):
    """Actions de gestion des risques"""
    ALLOW = "ALLOW"                    # Autoriser l'opération
    REDUCE_SIZE = "REDUCE_SIZE"        # Réduire la taille
    REJECT = "REJECT"                  # Rejeter l'opération
    CLOSE_POSITION = "CLOSE_POSITION"  # Fermer une position
    EMERGENCY_STOP = "EMERGENCY_STOP"  # Arrêt d'urgence

@dataclass
class RiskAssessment:
    """Évaluation des risques"""
    action: RiskAction
    risk_score: float
    reasons: List[str]
    recommendations: List[str]
    max_position_size: Optional[float] = None
    suggested_stop_loss: Optional[float] = None
    suggested_take_profit: Optional[List[float]] = None

@dataclass
class RiskAlert:
    """Alerte de risque"""
    alert_type: str
    severity: str
    message: str
    timestamp: datetime
    symbol: Optional[str] = None
    action_required: bool = False

class RiskEngine:
    """Moteur de gestion des risques intégré"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # État du moteur
        self.is_monitoring = False
        self.monitor_thread = None
        
        # Alertes de risque
        self.risk_alerts: List[RiskAlert] = []
        
        # Configuration des seuils d'alerte
        self.alert_thresholds = {
            'portfolio_risk_score': 80,      # Score de risque portfolio
            'daily_loss_percent': 5,         # Perte quotidienne en %
            'drawdown_percent': 15,          # Drawdown en %
            'correlation_threshold': 0.85,   # Corrélation élevée
            'concentration_percent': 30      # Concentration par actif
        }
        
        # Historique des évaluations
        self.assessment_history: List[Tuple[datetime, RiskAssessment]] = []
        
        # Configuration par défaut des stop-loss
        self._setup_default_stop_configs()
        
        self.logger.info("⚖️ Moteur de gestion des risques initialisé")
    
    def _setup_default_stop_configs(self):
        """Configure les stop-loss par défaut pour différents types d'actifs"""
        # Configuration conservatrice pour BTC
        btc_config = StopLossConfig(
            stop_type=StopLossType.TRAILING,
            initial_distance=0.05,
            trailing_distance=0.03,
            min_distance=0.02,
            max_distance=0.1
        )
        stop_loss_manager.set_stop_loss_config('BTC/USDT', btc_config)
        
        # Configuration modérée pour ETH
        eth_config = StopLossConfig(
            stop_type=StopLossType.ATR_BASED,
            initial_distance=0.06,
            atr_multiplier=2.0,
            min_distance=0.03,
            max_distance=0.12
        )
        stop_loss_manager.set_stop_loss_config('ETH/USDT', eth_config)
        
        # Configuration pour altcoins (plus agressive)
        altcoin_config = StopLossConfig(
            stop_type=StopLossType.VOLATILITY_BASED,
            initial_distance=0.08,
            volatility_multiplier=1.5,
            min_distance=0.04,
            max_distance=0.15
        )
        
        # Appliquer aux altcoins principaux
        altcoins = ['ADA/USDT', 'SOL/USDT', 'DOT/USDT', 'LINK/USDT', 'UNI/USDT']
        for symbol in altcoins:
            stop_loss_manager.set_stop_loss_config(symbol, altcoin_config)
    
    def assess_trade_risk(self, symbol: str, side: str, quantity: float, 
                         price: float, position_type: PositionType = PositionType.LONG) -> RiskAssessment:
        """
        Évalue les risques d'un trade proposé
        
        Args:
            symbol: Symbole à trader
            side: 'buy' ou 'sell'
            quantity: Quantité proposée
            price: Prix proposé
            position_type: Type de position
            
        Returns:
            Évaluation des risques
        """
        try:
            reasons = []
            recommendations = []
            risk_score = 0
            action = RiskAction.ALLOW
            
            # 1. Vérifier les limites du portefeuille
            can_open, portfolio_reason = portfolio_manager.can_open_position(symbol, quantity, price)
            if not can_open:
                reasons.append(f"Portfolio: {portfolio_reason}")
                risk_score += 30
                action = RiskAction.REJECT
            
            # 2. Calculer la taille de position optimale
            optimal_size = portfolio_manager.calculate_position_size(symbol, price)
            if quantity > optimal_size * 1.5:  # 50% de tolérance
                reasons.append(f"Taille excessive: {quantity:.6f} > {optimal_size:.6f} (optimal)")
                risk_score += 25
                if action == RiskAction.ALLOW:
                    action = RiskAction.REDUCE_SIZE
            
            # 3. Vérifier les corrélations
            portfolio_positions = self._get_portfolio_weights()
            if portfolio_positions:
                correlation_manager.check_correlation_alerts(portfolio_positions)
                
                # Vérifier si l'ajout de cette position augmente les corrélations
                correlation_risk = self._assess_correlation_risk(symbol, quantity, price, portfolio_positions)
                if correlation_risk > 0.8:
                    reasons.append(f"Corrélation élevée avec positions existantes ({correlation_risk:.2f})")
                    risk_score += 20
            
            # 4. Calculer les niveaux de stop-loss et take-profit
            suggested_stop = stop_loss_manager.calculate_stop_loss(symbol, price, position_type.value)
            suggested_tp = stop_loss_manager.calculate_take_profit(symbol, price, position_type.value, suggested_stop)
            
            # 5. Évaluer le ratio risque/récompense
            risk_distance = abs(price - suggested_stop) / price
            reward_distance = abs(suggested_tp[0] - price) / price if suggested_tp else 0
            
            if reward_distance > 0:
                risk_reward_ratio = reward_distance / risk_distance
                if risk_reward_ratio < 1.5:
                    reasons.append(f"Ratio risque/récompense faible: {risk_reward_ratio:.2f}")
                    risk_score += 15
                    recommendations.append("Considérer un meilleur point d'entrée")
            
            # 6. Vérifier les métriques de risque du portefeuille
            risk_metrics = portfolio_manager.get_risk_metrics()
            
            if risk_metrics.risk_score > self.alert_thresholds['portfolio_risk_score']:
                reasons.append(f"Score de risque portfolio élevé: {risk_metrics.risk_score:.1f}")
                risk_score += 20
            
            if risk_metrics.current_drawdown > self.alert_thresholds['drawdown_percent']:
                reasons.append(f"Drawdown élevé: {risk_metrics.current_drawdown:.1f}%")
                risk_score += 25
                if action == RiskAction.ALLOW:
                    action = RiskAction.REDUCE_SIZE
            
            # 7. Vérifier les pertes quotidiennes
            if abs(risk_metrics.daily_pnl) > portfolio_manager.get_portfolio_value() * 0.05:
                reasons.append("Limite de perte quotidienne approchée")
                risk_score += 30
                if action == RiskAction.ALLOW:
                    action = RiskAction.REDUCE_SIZE
            
            # 8. Déterminer l'action finale
            if risk_score >= 80:
                action = RiskAction.REJECT
                recommendations.append("Attendre de meilleures conditions de marché")
            elif risk_score >= 50:
                action = RiskAction.REDUCE_SIZE
                recommendations.append("Réduire la taille de position de 50%")
            
            # 9. Ajouter des recommandations générales
            if not reasons:
                recommendations.append("Trade dans les paramètres de risque acceptables")
            else:
                recommendations.append("Surveiller étroitement cette position")
            
            assessment = RiskAssessment(
                action=action,
                risk_score=risk_score,
                reasons=reasons,
                recommendations=recommendations,
                max_position_size=optimal_size,
                suggested_stop_loss=suggested_stop,
                suggested_take_profit=suggested_tp
            )
            
            # Enregistrer dans l'historique
            self.assessment_history.append((datetime.now(), assessment))
            
            # Limiter l'historique
            if len(self.assessment_history) > 100:
                self.assessment_history = self.assessment_history[-50:]
            
            self.logger.info(f"📊 Évaluation risque {symbol}: {action.value} (score: {risk_score:.1f})")
            
            return assessment
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.HIGH, {
                'function': 'assess_trade_risk',
                'symbol': symbol,
                'side': side
            })
            
            # Retourner une évaluation conservatrice en cas d'erreur
            return RiskAssessment(
                action=RiskAction.REJECT,
                risk_score=100,
                reasons=["Erreur dans l'évaluation des risques"],
                recommendations=["Réessayer plus tard"]
            )
    
    def _get_portfolio_weights(self) -> Dict[str, float]:
        """Récupère les poids du portefeuille"""
        portfolio_value = portfolio_manager.get_portfolio_value()
        if portfolio_value == 0:
            return {}
        
        weights = {}
        for symbol, position in portfolio_manager.positions.items():
            weight = position.market_value / portfolio_value
            weights[symbol] = weight
        
        return weights
    
    def _assess_correlation_risk(self, new_symbol: str, quantity: float, price: float,
                               existing_positions: Dict[str, float]) -> float:
        """Évalue le risque de corrélation d'une nouvelle position"""
        if not existing_positions or correlation_manager.correlation_matrix is None:
            return 0.0
        
        max_correlation = 0.0
        
        for existing_symbol in existing_positions.keys():
            if (new_symbol in correlation_manager.correlation_matrix.index and 
                existing_symbol in correlation_manager.correlation_matrix.columns):
                
                correlation = abs(correlation_manager.correlation_matrix.loc[new_symbol, existing_symbol])
                max_correlation = max(max_correlation, correlation)
        
        return max_correlation
    
    def start_monitoring(self, interval: int = 60):
        """Démarre le monitoring des risques en continu"""
        if self.is_monitoring:
            self.logger.warning("⚠️ Monitoring déjà en cours")
            return
        
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitoring_loop,
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
        
        self.logger.info(f"🚀 Monitoring des risques démarré (intervalle: {interval}s)")
    
    def stop_monitoring(self):
        """Arrête le monitoring des risques"""
        if not self.is_monitoring:
            self.logger.warning("⚠️ Aucun monitoring en cours")
            return
        
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        
        self.logger.info("🛑 Monitoring des risques arrêté")
    
    def _monitoring_loop(self, interval: int):
        """Boucle principale de monitoring"""
        while self.is_monitoring:
            try:
                # Mettre à jour les prix des positions
                self._update_position_prices()
                
                # Vérifier les stop-loss
                self._check_stop_losses()
                
                # Vérifier les métriques de risque
                self._check_risk_metrics()
                
                # Vérifier les corrélations
                self._check_correlations()
                
                # Nettoyer les anciennes alertes
                self._cleanup_old_alerts()
                
                time.sleep(interval)
                
            except Exception as e:
                error_handler.handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.MEDIUM, {
                    'function': '_monitoring_loop'
                })
                time.sleep(interval)
    
    def _update_position_prices(self):
        """Met à jour les prix des positions (simulation)"""
        # Dans un vrai système, ceci récupérerait les prix réels
        import random
        
        price_updates = {}
        for symbol in portfolio_manager.positions.keys():
            # Simulation de variation de prix
            current_pos = portfolio_manager.positions[symbol]
            if current_pos.current_price:
                # Variation aléatoire de ±2%
                variation = random.uniform(-0.02, 0.02)
                new_price = current_pos.current_price * (1 + variation)
                price_updates[symbol] = new_price
                
                # Mettre à jour l'historique pour les corrélations
                correlation_manager.add_price_data(symbol, new_price)
                stop_loss_manager.update_price_history(symbol, new_price)
        
        if price_updates:
            portfolio_manager.update_position_prices(price_updates)
            stop_loss_manager.update_trailing_stops(price_updates)
    
    def _check_stop_losses(self):
        """Vérifie les déclenchements de stop-loss"""
        # Récupérer les prix actuels
        price_updates = {}
        for symbol, position in portfolio_manager.positions.items():
            if position.current_price:
                price_updates[symbol] = position.current_price
        
        # Vérifier les déclenchements
        triggered_symbols = stop_loss_manager.check_stop_triggers(price_updates)
        
        for symbol in triggered_symbols:
            self._create_risk_alert(
                alert_type="STOP_LOSS_TRIGGERED",
                severity="HIGH",
                message=f"Stop-loss déclenché pour {symbol}",
                symbol=symbol,
                action_required=True
            )
    
    def _check_risk_metrics(self):
        """Vérifie les métriques de risque du portefeuille"""
        risk_metrics = portfolio_manager.get_risk_metrics()
        
        # Vérifier le score de risque
        if risk_metrics.risk_score > self.alert_thresholds['portfolio_risk_score']:
            self._create_risk_alert(
                alert_type="HIGH_RISK_SCORE",
                severity="HIGH",
                message=f"Score de risque élevé: {risk_metrics.risk_score:.1f}/100",
                action_required=True
            )
        
        # Vérifier le drawdown
        if risk_metrics.current_drawdown > self.alert_thresholds['drawdown_percent']:
            self._create_risk_alert(
                alert_type="HIGH_DRAWDOWN",
                severity="CRITICAL",
                message=f"Drawdown élevé: {risk_metrics.current_drawdown:.1f}%",
                action_required=True
            )
        
        # Vérifier les pertes quotidiennes
        daily_loss_percent = abs(risk_metrics.daily_pnl) / risk_metrics.total_value * 100
        if daily_loss_percent > self.alert_thresholds['daily_loss_percent']:
            self._create_risk_alert(
                alert_type="HIGH_DAILY_LOSS",
                severity="HIGH",
                message=f"Perte quotidienne élevée: {daily_loss_percent:.1f}%",
                action_required=True
            )
    
    def _check_correlations(self):
        """Vérifie les corrélations du portefeuille"""
        portfolio_positions = self._get_portfolio_weights()
        if portfolio_positions:
            correlation_manager.check_correlation_alerts(portfolio_positions)
    
    def _create_risk_alert(self, alert_type: str, severity: str, message: str,
                          symbol: Optional[str] = None, action_required: bool = False):
        """Crée une alerte de risque"""
        alert = RiskAlert(
            alert_type=alert_type,
            severity=severity,
            message=message,
            timestamp=datetime.now(),
            symbol=symbol,
            action_required=action_required
        )
        
        self.risk_alerts.append(alert)
        
        # Notification selon la sévérité
        if severity in ['HIGH', 'CRITICAL']:
            severity_emoji = "🚨" if severity == "CRITICAL" else "⚠️"
            notifier.send_telegram(
                f"{severity_emoji} <b>ALERTE RISQUE</b>\n"
                f"📊 Type: {alert_type}\n"
                f"💬 {message}\n"
                f"🕒 {datetime.now().strftime('%H:%M:%S')}"
            )
        
        self.logger.warning(f"⚠️ Alerte risque: {alert_type} - {message}")
    
    def _cleanup_old_alerts(self):
        """Nettoie les anciennes alertes"""
        cutoff_time = datetime.now() - timedelta(hours=24)
        
        old_count = len(self.risk_alerts)
        self.risk_alerts = [
            alert for alert in self.risk_alerts 
            if alert.timestamp >= cutoff_time
        ]
        
        # Nettoyer aussi les alertes de corrélation
        correlation_manager.cleanup_old_alerts()
    
    def get_risk_dashboard(self) -> Dict[str, Any]:
        """Génère un dashboard complet des risques"""
        portfolio_summary = portfolio_manager.get_portfolio_summary()
        risk_metrics = portfolio_manager.get_risk_metrics()
        correlation_report = correlation_manager.get_correlation_report()
        stop_stats = stop_loss_manager.get_stop_loss_stats()
        
        # Alertes récentes
        recent_alerts = [
            {
                'type': alert.alert_type,
                'severity': alert.severity,
                'message': alert.message,
                'timestamp': alert.timestamp.isoformat(),
                'symbol': alert.symbol,
                'action_required': alert.action_required
            }
            for alert in self.risk_alerts[-10:]  # 10 dernières alertes
        ]
        
        # Score de santé global
        health_score = 100 - min(100, risk_metrics.risk_score)
        
        return {
            'timestamp': datetime.now().isoformat(),
            'health_score': health_score,
            'monitoring_active': self.is_monitoring,
            'portfolio': {
                'total_value': portfolio_summary['portfolio_value'],
                'total_return': portfolio_summary['total_return'],
                'positions_count': portfolio_summary['positions_count'],
                'risk_score': portfolio_summary['risk_score'],
                'daily_pnl': portfolio_summary['daily_pnl'],
                'max_drawdown': portfolio_summary['max_drawdown'],
                'current_drawdown': portfolio_summary['current_drawdown']
            },
            'correlations': correlation_report,
            'stop_losses': stop_stats,
            'recent_alerts': recent_alerts,
            'alert_counts': {
                'total': len(self.risk_alerts),
                'critical': len([a for a in self.risk_alerts if a.severity == 'CRITICAL']),
                'high': len([a for a in self.risk_alerts if a.severity == 'HIGH']),
                'action_required': len([a for a in self.risk_alerts if a.action_required])
            }
        }

# Instance globale
risk_engine = RiskEngine()
