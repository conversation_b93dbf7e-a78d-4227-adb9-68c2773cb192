"""
🛑 Gestionnaire de stop-loss adaptatifs
Système intelligent de gestion des stop-loss et take-profit
"""

import logging
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from enum import Enum
from dataclasses import dataclass
from pathlib import Path
import sys

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from utils.notifications import notifier
from utils.error_handler import error_handler, ErrorCategory, ErrorSeverity

class StopLossType(Enum):
    """Types de stop-loss"""
    FIXED = "FIXED"                    # Stop-loss fixe
    TRAILING = "TRAILING"              # Stop-loss suiveur
    ATR_BASED = "ATR_BASED"           # Basé sur l'ATR
    VOLATILITY_BASED = "VOLATILITY_BASED"  # Basé sur la volatilité
    SUPPORT_RESISTANCE = "SUPPORT_RESISTANCE"  # Basé sur supports/résistances

class TakeProfitType(Enum):
    """Types de take-profit"""
    FIXED = "FIXED"                    # Take-profit fixe
    SCALED = "SCALED"                  # Take-profit échelonné
    RISK_REWARD = "RISK_REWARD"       # Basé sur le ratio risque/récompense
    FIBONACCI = "FIBONACCI"            # Basé sur les retracements Fibonacci

@dataclass
class StopLossConfig:
    """Configuration d'un stop-loss"""
    stop_type: StopLossType
    initial_distance: float           # Distance initiale en %
    trailing_distance: Optional[float] = None  # Distance de trailing en %
    atr_multiplier: Optional[float] = None     # Multiplicateur ATR
    volatility_multiplier: Optional[float] = None  # Multiplicateur volatilité
    min_distance: float = 0.01        # Distance minimum 1%
    max_distance: float = 0.2         # Distance maximum 20%

@dataclass
class TakeProfitConfig:
    """Configuration d'un take-profit"""
    tp_type: TakeProfitType
    target_distance: float            # Distance cible en %
    risk_reward_ratio: Optional[float] = None  # Ratio risque/récompense
    scale_levels: Optional[List[float]] = None  # Niveaux d'échelonnement
    fibonacci_levels: Optional[List[float]] = None  # Niveaux Fibonacci

@dataclass
class StopLossOrder:
    """Ordre de stop-loss"""
    symbol: str
    stop_price: float
    original_stop: float
    stop_type: StopLossType
    created_at: datetime
    last_updated: datetime
    triggered: bool = False
    trigger_price: Optional[float] = None
    trigger_time: Optional[datetime] = None

class StopLossManager:
    """Gestionnaire de stop-loss adaptatifs"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Ordres de stop-loss actifs
        self.stop_orders: Dict[str, StopLossOrder] = {}
        
        # Configurations par symbole
        self.stop_configs: Dict[str, StopLossConfig] = {}
        self.tp_configs: Dict[str, TakeProfitConfig] = {}
        
        # Historique des prix pour calculs
        self.price_history: Dict[str, List[Tuple[datetime, float]]] = {}
        
        # Statistiques
        self.stops_triggered = 0
        self.stops_saved_loss = 0.0
        
        self.logger.info("🛑 Gestionnaire de stop-loss initialisé")
    
    def set_stop_loss_config(self, symbol: str, config: StopLossConfig):
        """Configure le stop-loss pour un symbole"""
        self.stop_configs[symbol] = config
        self.logger.info(f"⚙️ Configuration stop-loss définie pour {symbol}: {config.stop_type.value}")
    
    def set_take_profit_config(self, symbol: str, config: TakeProfitConfig):
        """Configure le take-profit pour un symbole"""
        self.tp_configs[symbol] = config
        self.logger.info(f"⚙️ Configuration take-profit définie pour {symbol}: {config.tp_type.value}")
    
    def calculate_stop_loss(self, symbol: str, entry_price: float, 
                          position_type: str, current_price: Optional[float] = None) -> float:
        """
        Calcule le niveau de stop-loss optimal
        
        Args:
            symbol: Symbole tradé
            entry_price: Prix d'entrée
            position_type: 'LONG' ou 'SHORT'
            current_price: Prix actuel (optionnel)
            
        Returns:
            Prix de stop-loss calculé
        """
        try:
            config = self.stop_configs.get(symbol)
            if not config:
                # Configuration par défaut
                config = StopLossConfig(
                    stop_type=StopLossType.FIXED,
                    initial_distance=0.05  # 5%
                )
            
            if config.stop_type == StopLossType.FIXED:
                return self._calculate_fixed_stop(entry_price, position_type, config.initial_distance)
            
            elif config.stop_type == StopLossType.ATR_BASED:
                return self._calculate_atr_stop(symbol, entry_price, position_type, config)
            
            elif config.stop_type == StopLossType.VOLATILITY_BASED:
                return self._calculate_volatility_stop(symbol, entry_price, position_type, config)
            
            elif config.stop_type == StopLossType.SUPPORT_RESISTANCE:
                return self._calculate_support_resistance_stop(symbol, entry_price, position_type)
            
            else:
                # Par défaut, stop fixe
                return self._calculate_fixed_stop(entry_price, position_type, config.initial_distance)
                
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.MEDIUM, {
                'function': 'calculate_stop_loss',
                'symbol': symbol,
                'entry_price': entry_price
            })
            # Stop-loss de sécurité
            return self._calculate_fixed_stop(entry_price, position_type, 0.05)
    
    def _calculate_fixed_stop(self, entry_price: float, position_type: str, distance: float) -> float:
        """Calcule un stop-loss fixe"""
        if position_type.upper() == 'LONG':
            return entry_price * (1 - distance)
        else:
            return entry_price * (1 + distance)
    
    def _calculate_atr_stop(self, symbol: str, entry_price: float, 
                          position_type: str, config: StopLossConfig) -> float:
        """Calcule un stop-loss basé sur l'ATR"""
        atr = self._calculate_atr(symbol, period=14)
        if atr == 0:
            return self._calculate_fixed_stop(entry_price, position_type, config.initial_distance)
        
        multiplier = config.atr_multiplier or 2.0
        atr_distance = (atr * multiplier) / entry_price
        
        # Limiter la distance
        atr_distance = max(config.min_distance, min(config.max_distance, atr_distance))
        
        return self._calculate_fixed_stop(entry_price, position_type, atr_distance)
    
    def _calculate_volatility_stop(self, symbol: str, entry_price: float,
                                 position_type: str, config: StopLossConfig) -> float:
        """Calcule un stop-loss basé sur la volatilité"""
        volatility = self._calculate_volatility(symbol, period=20)
        if volatility == 0:
            return self._calculate_fixed_stop(entry_price, position_type, config.initial_distance)
        
        multiplier = config.volatility_multiplier or 1.5
        vol_distance = volatility * multiplier
        
        # Limiter la distance
        vol_distance = max(config.min_distance, min(config.max_distance, vol_distance))
        
        return self._calculate_fixed_stop(entry_price, position_type, vol_distance)
    
    def _calculate_support_resistance_stop(self, symbol: str, entry_price: float, 
                                         position_type: str) -> float:
        """Calcule un stop-loss basé sur les supports/résistances"""
        # Simplification: utiliser les niveaux de prix récents
        if symbol not in self.price_history or len(self.price_history[symbol]) < 20:
            return self._calculate_fixed_stop(entry_price, position_type, 0.05)
        
        recent_prices = [price for _, price in self.price_history[symbol][-50:]]
        
        if position_type.upper() == 'LONG':
            # Trouver le support le plus proche en dessous du prix d'entrée
            supports = [p for p in recent_prices if p < entry_price]
            if supports:
                support_level = max(supports)
                # Stop légèrement en dessous du support
                return support_level * 0.995
        else:
            # Trouver la résistance la plus proche au-dessus du prix d'entrée
            resistances = [p for p in recent_prices if p > entry_price]
            if resistances:
                resistance_level = min(resistances)
                # Stop légèrement au-dessus de la résistance
                return resistance_level * 1.005
        
        # Fallback
        return self._calculate_fixed_stop(entry_price, position_type, 0.05)
    
    def calculate_take_profit(self, symbol: str, entry_price: float, 
                            position_type: str, stop_loss: float) -> List[float]:
        """
        Calcule les niveaux de take-profit
        
        Returns:
            Liste des niveaux de take-profit
        """
        try:
            config = self.tp_configs.get(symbol)
            if not config:
                # Configuration par défaut
                config = TakeProfitConfig(
                    tp_type=TakeProfitType.RISK_REWARD,
                    target_distance=0.1,  # 10%
                    risk_reward_ratio=2.0
                )
            
            if config.tp_type == TakeProfitType.FIXED:
                return [self._calculate_fixed_tp(entry_price, position_type, config.target_distance)]
            
            elif config.tp_type == TakeProfitType.RISK_REWARD:
                return self._calculate_risk_reward_tp(entry_price, position_type, stop_loss, config)
            
            elif config.tp_type == TakeProfitType.SCALED:
                return self._calculate_scaled_tp(entry_price, position_type, config)
            
            elif config.tp_type == TakeProfitType.FIBONACCI:
                return self._calculate_fibonacci_tp(symbol, entry_price, position_type)
            
            else:
                return [self._calculate_fixed_tp(entry_price, position_type, config.target_distance)]
                
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.MEDIUM, {
                'function': 'calculate_take_profit',
                'symbol': symbol
            })
            # Take-profit de sécurité
            return [self._calculate_fixed_tp(entry_price, position_type, 0.1)]
    
    def _calculate_fixed_tp(self, entry_price: float, position_type: str, distance: float) -> float:
        """Calcule un take-profit fixe"""
        if position_type.upper() == 'LONG':
            return entry_price * (1 + distance)
        else:
            return entry_price * (1 - distance)
    
    def _calculate_risk_reward_tp(self, entry_price: float, position_type: str, 
                                stop_loss: float, config: TakeProfitConfig) -> List[float]:
        """Calcule le take-profit basé sur le ratio risque/récompense"""
        risk_distance = abs(entry_price - stop_loss)
        reward_distance = risk_distance * (config.risk_reward_ratio or 2.0)
        
        if position_type.upper() == 'LONG':
            tp_price = entry_price + reward_distance
        else:
            tp_price = entry_price - reward_distance
        
        return [tp_price]
    
    def _calculate_scaled_tp(self, entry_price: float, position_type: str, 
                           config: TakeProfitConfig) -> List[float]:
        """Calcule des take-profits échelonnés"""
        levels = config.scale_levels or [0.05, 0.1, 0.15]  # 5%, 10%, 15%
        tp_prices = []
        
        for level in levels:
            if position_type.upper() == 'LONG':
                tp_prices.append(entry_price * (1 + level))
            else:
                tp_prices.append(entry_price * (1 - level))
        
        return tp_prices
    
    def _calculate_fibonacci_tp(self, symbol: str, entry_price: float, 
                              position_type: str) -> List[float]:
        """Calcule des take-profits basés sur Fibonacci"""
        # Niveaux Fibonacci standards
        fib_levels = [0.236, 0.382, 0.618, 1.0]
        
        # Estimer la range récente
        if symbol in self.price_history and len(self.price_history[symbol]) >= 20:
            recent_prices = [price for _, price in self.price_history[symbol][-20:]]
            price_range = max(recent_prices) - min(recent_prices)
        else:
            price_range = entry_price * 0.1  # 10% par défaut
        
        tp_prices = []
        for level in fib_levels:
            if position_type.upper() == 'LONG':
                tp_prices.append(entry_price + (price_range * level))
            else:
                tp_prices.append(entry_price - (price_range * level))
        
        return tp_prices
    
    def create_trailing_stop(self, symbol: str, entry_price: float, 
                           position_type: str, trailing_distance: float) -> str:
        """
        Crée un stop-loss suiveur
        
        Returns:
            ID de l'ordre de stop
        """
        initial_stop = self.calculate_stop_loss(symbol, entry_price, position_type)
        
        stop_order = StopLossOrder(
            symbol=symbol,
            stop_price=initial_stop,
            original_stop=initial_stop,
            stop_type=StopLossType.TRAILING,
            created_at=datetime.now(),
            last_updated=datetime.now()
        )
        
        order_id = f"{symbol}_{int(datetime.now().timestamp())}"
        self.stop_orders[order_id] = stop_order
        
        self.logger.info(f"🔄 Stop-loss suiveur créé pour {symbol}: {initial_stop}")
        return order_id
    
    def update_trailing_stops(self, price_updates: Dict[str, float]):
        """Met à jour les stop-loss suiveurs"""
        for order_id, stop_order in self.stop_orders.items():
            if stop_order.triggered or stop_order.stop_type != StopLossType.TRAILING:
                continue
            
            symbol = stop_order.symbol
            if symbol not in price_updates:
                continue
            
            current_price = price_updates[symbol]
            config = self.stop_configs.get(symbol)
            trailing_distance = config.trailing_distance if config else 0.05
            
            # Déterminer si c'est une position longue ou courte
            # (simplification: supposer LONG si le stop est en dessous du prix)
            is_long = stop_order.stop_price < current_price
            
            if is_long:
                # Position longue: monter le stop si le prix monte
                new_stop = current_price * (1 - trailing_distance)
                if new_stop > stop_order.stop_price:
                    old_stop = stop_order.stop_price
                    stop_order.stop_price = new_stop
                    stop_order.last_updated = datetime.now()
                    self.logger.debug(f"📈 Stop suiveur mis à jour {symbol}: {old_stop:.4f} -> {new_stop:.4f}")
            else:
                # Position courte: descendre le stop si le prix descend
                new_stop = current_price * (1 + trailing_distance)
                if new_stop < stop_order.stop_price:
                    old_stop = stop_order.stop_price
                    stop_order.stop_price = new_stop
                    stop_order.last_updated = datetime.now()
                    self.logger.debug(f"📉 Stop suiveur mis à jour {symbol}: {old_stop:.4f} -> {new_stop:.4f}")
    
    def check_stop_triggers(self, price_updates: Dict[str, float]) -> List[str]:
        """
        Vérifie si des stops sont déclenchés
        
        Returns:
            Liste des symboles avec stops déclenchés
        """
        triggered_symbols = []
        
        for order_id, stop_order in self.stop_orders.items():
            if stop_order.triggered:
                continue
            
            symbol = stop_order.symbol
            if symbol not in price_updates:
                continue
            
            current_price = price_updates[symbol]
            
            # Vérifier le déclenchement
            is_long = stop_order.stop_price < stop_order.original_stop
            triggered = False
            
            if is_long and current_price <= stop_order.stop_price:
                triggered = True
            elif not is_long and current_price >= stop_order.stop_price:
                triggered = True
            
            if triggered:
                stop_order.triggered = True
                stop_order.trigger_price = current_price
                stop_order.trigger_time = datetime.now()
                
                triggered_symbols.append(symbol)
                self.stops_triggered += 1
                
                self.logger.warning(f"🛑 Stop-loss déclenché pour {symbol}: {current_price}")
                
                # Notification
                notifier.send_telegram(
                    f"🛑 <b>STOP-LOSS DÉCLENCHÉ</b>\n"
                    f"📊 {symbol}\n"
                    f"💲 Prix: {current_price:.4f}\n"
                    f"🎯 Stop: {stop_order.stop_price:.4f}\n"
                    f"📅 {datetime.now().strftime('%H:%M:%S')}"
                )
        
        return triggered_symbols
    
    def _calculate_atr(self, symbol: str, period: int = 14) -> float:
        """Calcule l'Average True Range"""
        if symbol not in self.price_history or len(self.price_history[symbol]) < period + 1:
            return 0.0
        
        # Simplification: utiliser seulement les prix de clôture
        prices = [price for _, price in self.price_history[symbol][-period-1:]]
        
        true_ranges = []
        for i in range(1, len(prices)):
            tr = abs(prices[i] - prices[i-1])
            true_ranges.append(tr)
        
        return np.mean(true_ranges) if true_ranges else 0.0
    
    def _calculate_volatility(self, symbol: str, period: int = 20) -> float:
        """Calcule la volatilité (écart-type des rendements)"""
        if symbol not in self.price_history or len(self.price_history[symbol]) < period + 1:
            return 0.0
        
        prices = [price for _, price in self.price_history[symbol][-period-1:]]
        
        returns = []
        for i in range(1, len(prices)):
            ret = (prices[i] - prices[i-1]) / prices[i-1]
            returns.append(ret)
        
        return np.std(returns) if returns else 0.0
    
    def update_price_history(self, symbol: str, price: float):
        """Met à jour l'historique des prix"""
        if symbol not in self.price_history:
            self.price_history[symbol] = []
        
        self.price_history[symbol].append((datetime.now(), price))
        
        # Limiter l'historique (garder 200 points)
        if len(self.price_history[symbol]) > 200:
            self.price_history[symbol] = self.price_history[symbol][-100:]
    
    def get_stop_loss_stats(self) -> Dict[str, Any]:
        """Retourne les statistiques des stop-loss"""
        active_stops = len([s for s in self.stop_orders.values() if not s.triggered])
        
        return {
            'active_stops': active_stops,
            'total_stops_created': len(self.stop_orders),
            'stops_triggered': self.stops_triggered,
            'stops_saved_loss': self.stops_saved_loss,
            'trigger_rate': (self.stops_triggered / len(self.stop_orders) * 100) if self.stop_orders else 0
        }

# Instance globale
stop_loss_manager = StopLossManager()
