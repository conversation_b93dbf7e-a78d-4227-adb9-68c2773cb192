"""
💼 Gestionnaire de portefeuille avancé
Gestion du capital, positions et allocation des risques
"""

import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from decimal import Decimal, ROUND_DOWN
from dataclasses import dataclass
from enum import Enum
import numpy as np
from pathlib import Path
import sys

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from utils.notifications import notifier
from utils.error_handler import error_handler, ErrorCategory, ErrorSeverity

class PositionType(Enum):
    """Types de positions"""
    LONG = "LONG"
    SHORT = "SHORT"

class RiskLevel(Enum):
    """Niveaux de risque"""
    CONSERVATIVE = "CONSERVATIVE"
    MODERATE = "MODERATE"
    AGGRESSIVE = "AGGRESSIVE"

@dataclass
class Position:
    """Représente une position de trading"""
    symbol: str
    position_type: PositionType
    entry_price: float
    quantity: float
    entry_time: datetime
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    current_price: Optional[float] = None
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0
    fees_paid: float = 0.0
    
    @property
    def market_value(self) -> float:
        """Valeur de marché actuelle"""
        if self.current_price is None:
            return self.quantity * self.entry_price
        return self.quantity * self.current_price
    
    @property
    def cost_basis(self) -> float:
        """Base de coût de la position"""
        return self.quantity * self.entry_price
    
    @property
    def pnl_percentage(self) -> float:
        """P&L en pourcentage"""
        if self.cost_basis == 0:
            return 0.0
        return (self.unrealized_pnl / self.cost_basis) * 100

@dataclass
class RiskMetrics:
    """Métriques de risque du portefeuille"""
    total_value: float
    total_exposure: float
    cash_available: float
    unrealized_pnl: float
    realized_pnl: float
    daily_pnl: float
    max_drawdown: float
    current_drawdown: float
    risk_score: float
    leverage_ratio: float
    concentration_risk: float

class PortfolioManager:
    """Gestionnaire de portefeuille avancé"""
    
    def __init__(self, initial_capital: float, risk_level: RiskLevel = RiskLevel.MODERATE):
        self.logger = logging.getLogger(__name__)
        
        # Capital et configuration
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.risk_level = risk_level
        
        # Positions et historique
        self.positions: Dict[str, Position] = {}
        self.closed_positions: List[Position] = []
        self.trade_history: List[Dict] = []
        
        # Métriques de risque
        self.peak_value = initial_capital
        self.max_drawdown = 0.0
        self.daily_pnl = 0.0
        self.last_reset_date = datetime.now().date()
        
        # Configuration des risques selon le niveau
        self._configure_risk_parameters()
        
        # Historique des valeurs pour calcul de volatilité
        self.value_history: List[Tuple[datetime, float]] = []
        
        self.logger.info(f"💼 Gestionnaire de portefeuille initialisé: {initial_capital} ({risk_level.value})")
    
    def _configure_risk_parameters(self):
        """Configure les paramètres de risque selon le niveau"""
        risk_configs = {
            RiskLevel.CONSERVATIVE: {
                'max_position_size': 0.05,      # 5% max par position
                'max_total_exposure': 0.3,      # 30% max d'exposition
                'max_daily_loss': 0.02,         # 2% de perte max par jour
                'max_drawdown_limit': 0.1,      # 10% de drawdown max
                'max_correlation': 0.7,         # Corrélation max entre positions
                'stop_loss_default': 0.03,      # 3% de stop-loss par défaut
                'take_profit_default': 0.06,    # 6% de take-profit par défaut
                'max_positions': 5               # 5 positions max
            },
            RiskLevel.MODERATE: {
                'max_position_size': 0.1,       # 10% max par position
                'max_total_exposure': 0.6,      # 60% max d'exposition
                'max_daily_loss': 0.05,         # 5% de perte max par jour
                'max_drawdown_limit': 0.2,      # 20% de drawdown max
                'max_correlation': 0.8,         # Corrélation max entre positions
                'stop_loss_default': 0.05,      # 5% de stop-loss par défaut
                'take_profit_default': 0.1,     # 10% de take-profit par défaut
                'max_positions': 8               # 8 positions max
            },
            RiskLevel.AGGRESSIVE: {
                'max_position_size': 0.2,       # 20% max par position
                'max_total_exposure': 0.9,      # 90% max d'exposition
                'max_daily_loss': 0.1,          # 10% de perte max par jour
                'max_drawdown_limit': 0.3,      # 30% de drawdown max
                'max_correlation': 0.9,         # Corrélation max entre positions
                'stop_loss_default': 0.08,      # 8% de stop-loss par défaut
                'take_profit_default': 0.15,    # 15% de take-profit par défaut
                'max_positions': 12              # 12 positions max
            }
        }
        
        self.risk_params = risk_configs[self.risk_level]
        self.logger.info(f"⚙️ Paramètres de risque configurés: {self.risk_level.value}")
    
    def calculate_position_size(self, symbol: str, entry_price: float, 
                              stop_loss: Optional[float] = None,
                              risk_amount: Optional[float] = None) -> float:
        """
        Calcule la taille de position optimale
        
        Args:
            symbol: Symbole à trader
            entry_price: Prix d'entrée
            stop_loss: Prix de stop-loss
            risk_amount: Montant à risquer (optionnel)
            
        Returns:
            Taille de position recommandée
        """
        try:
            # Montant à risquer (par défaut basé sur le capital)
            if risk_amount is None:
                risk_amount = self.get_portfolio_value() * self.risk_params['max_position_size']
            
            # Si pas de stop-loss, utiliser le stop-loss par défaut
            if stop_loss is None:
                stop_loss = entry_price * (1 - self.risk_params['stop_loss_default'])
            
            # Calculer le risque par unité
            risk_per_unit = abs(entry_price - stop_loss)
            
            if risk_per_unit == 0:
                self.logger.warning("⚠️ Risque par unité nul, utilisation de la taille max")
                return risk_amount / entry_price
            
            # Calculer la taille de position basée sur le risque
            position_size = risk_amount / risk_per_unit
            
            # Limiter selon les contraintes du portefeuille
            max_value = self.get_portfolio_value() * self.risk_params['max_position_size']
            max_size = max_value / entry_price
            
            position_size = min(position_size, max_size)
            
            # Vérifier l'exposition totale
            current_exposure = self.get_total_exposure()
            max_exposure = self.get_portfolio_value() * self.risk_params['max_total_exposure']
            
            if current_exposure + (position_size * entry_price) > max_exposure:
                available_exposure = max_exposure - current_exposure
                position_size = max(0, available_exposure / entry_price)
            
            self.logger.debug(f"📏 Taille calculée pour {symbol}: {position_size:.6f}")
            return position_size
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.HIGH, {
                'function': 'calculate_position_size',
                'symbol': symbol,
                'entry_price': entry_price
            })
            return 0.0
    
    def can_open_position(self, symbol: str, position_size: float, entry_price: float) -> Tuple[bool, str]:
        """
        Vérifie si une position peut être ouverte
        
        Returns:
            Tuple (can_open, reason)
        """
        # Vérifier si la position existe déjà
        if symbol in self.positions:
            return False, f"Position déjà ouverte pour {symbol}"
        
        # Vérifier le nombre maximum de positions
        if len(self.positions) >= self.risk_params['max_positions']:
            return False, f"Nombre maximum de positions atteint ({self.risk_params['max_positions']})"
        
        # Vérifier la taille de position
        position_value = position_size * entry_price
        max_position_value = self.get_portfolio_value() * self.risk_params['max_position_size']
        
        if position_value > max_position_value:
            return False, f"Position trop importante: {position_value:.2f} > {max_position_value:.2f}"
        
        # Vérifier l'exposition totale
        current_exposure = self.get_total_exposure()
        max_exposure = self.get_portfolio_value() * self.risk_params['max_total_exposure']
        
        if current_exposure + position_value > max_exposure:
            return False, f"Exposition totale dépassée: {current_exposure + position_value:.2f} > {max_exposure:.2f}"
        
        # Vérifier le capital disponible
        if position_value > self.current_capital:
            return False, f"Capital insuffisant: {position_value:.2f} > {self.current_capital:.2f}"
        
        # Vérifier les pertes quotidiennes
        if not self._check_daily_loss_limit():
            return False, "Limite de perte quotidienne atteinte"
        
        # Vérifier le drawdown
        if not self._check_drawdown_limit():
            return False, "Limite de drawdown atteinte"
        
        return True, "Position autorisée"
    
    def open_position(self, symbol: str, position_type: PositionType, 
                     entry_price: float, quantity: float,
                     stop_loss: Optional[float] = None,
                     take_profit: Optional[float] = None) -> bool:
        """
        Ouvre une nouvelle position
        
        Returns:
            True si la position a été ouverte avec succès
        """
        try:
            # Vérifier si la position peut être ouverte
            can_open, reason = self.can_open_position(symbol, quantity, entry_price)
            if not can_open:
                self.logger.warning(f"⚠️ Position refusée pour {symbol}: {reason}")
                return False
            
            # Calculer les niveaux par défaut si non fournis
            if stop_loss is None:
                if position_type == PositionType.LONG:
                    stop_loss = entry_price * (1 - self.risk_params['stop_loss_default'])
                else:
                    stop_loss = entry_price * (1 + self.risk_params['stop_loss_default'])
            
            if take_profit is None:
                if position_type == PositionType.LONG:
                    take_profit = entry_price * (1 + self.risk_params['take_profit_default'])
                else:
                    take_profit = entry_price * (1 - self.risk_params['take_profit_default'])
            
            # Créer la position
            position = Position(
                symbol=symbol,
                position_type=position_type,
                entry_price=entry_price,
                quantity=quantity,
                entry_time=datetime.now(),
                stop_loss=stop_loss,
                take_profit=take_profit,
                current_price=entry_price
            )
            
            # Ajouter au portefeuille
            self.positions[symbol] = position
            
            # Mettre à jour le capital
            position_value = quantity * entry_price
            self.current_capital -= position_value
            
            # Enregistrer dans l'historique
            self.trade_history.append({
                'timestamp': datetime.now(),
                'action': 'OPEN',
                'symbol': symbol,
                'type': position_type.value,
                'price': entry_price,
                'quantity': quantity,
                'value': position_value
            })
            
            self.logger.info(f"✅ Position ouverte: {symbol} {position_type.value} {quantity:.6f} @ {entry_price}")
            
            # Notification
            notifier.send_telegram(
                f"📈 <b>Position ouverte</b>\n"
                f"📊 {symbol}: {position_type.value}\n"
                f"💰 Quantité: {quantity:.6f}\n"
                f"💲 Prix: {entry_price:.4f}\n"
                f"🛑 Stop: {stop_loss:.4f}\n"
                f"🎯 Target: {take_profit:.4f}"
            )
            
            return True
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.HIGH, {
                'function': 'open_position',
                'symbol': symbol,
                'position_type': position_type.value
            })
            return False
    
    def close_position(self, symbol: str, exit_price: float, 
                      partial_quantity: Optional[float] = None) -> bool:
        """
        Ferme une position (totalement ou partiellement)
        
        Args:
            symbol: Symbole de la position
            exit_price: Prix de sortie
            partial_quantity: Quantité partielle à fermer (None = fermeture totale)
            
        Returns:
            True si la fermeture a réussi
        """
        try:
            if symbol not in self.positions:
                self.logger.warning(f"⚠️ Aucune position trouvée pour {symbol}")
                return False
            
            position = self.positions[symbol]
            
            # Déterminer la quantité à fermer
            close_quantity = partial_quantity if partial_quantity else position.quantity
            
            if close_quantity > position.quantity:
                self.logger.warning(f"⚠️ Quantité à fermer trop importante: {close_quantity} > {position.quantity}")
                return False
            
            # Calculer le P&L
            if position.position_type == PositionType.LONG:
                pnl = (exit_price - position.entry_price) * close_quantity
            else:
                pnl = (position.entry_price - exit_price) * close_quantity
            
            # Mettre à jour le capital
            exit_value = close_quantity * exit_price
            self.current_capital += exit_value
            
            # Mettre à jour les métriques
            position.realized_pnl += pnl
            self.daily_pnl += pnl
            
            # Enregistrer dans l'historique
            self.trade_history.append({
                'timestamp': datetime.now(),
                'action': 'CLOSE',
                'symbol': symbol,
                'type': position.position_type.value,
                'price': exit_price,
                'quantity': close_quantity,
                'value': exit_value,
                'pnl': pnl
            })
            
            # Fermeture partielle ou totale
            if partial_quantity and partial_quantity < position.quantity:
                # Fermeture partielle
                position.quantity -= close_quantity
                self.logger.info(f"📉 Position partiellement fermée: {symbol} {close_quantity:.6f} @ {exit_price}")
            else:
                # Fermeture totale
                self.closed_positions.append(position)
                del self.positions[symbol]
                self.logger.info(f"📉 Position fermée: {symbol} {close_quantity:.6f} @ {exit_price}")
            
            # Notification
            pnl_emoji = "💚" if pnl >= 0 else "❤️"
            notifier.send_telegram(
                f"📉 <b>Position fermée</b>\n"
                f"📊 {symbol}: {position.position_type.value}\n"
                f"💰 Quantité: {close_quantity:.6f}\n"
                f"💲 Prix sortie: {exit_price:.4f}\n"
                f"{pnl_emoji} P&L: {pnl:.2f} ({(pnl/position.cost_basis)*100:.2f}%)"
            )
            
            return True
            
        except Exception as e:
            error_handler.handle_error(e, ErrorCategory.TRADING, ErrorSeverity.HIGH, {
                'function': 'close_position',
                'symbol': symbol,
                'exit_price': exit_price
            })
            return False
    
    def update_position_prices(self, price_updates: Dict[str, float]):
        """Met à jour les prix des positions"""
        for symbol, price in price_updates.items():
            if symbol in self.positions:
                position = self.positions[symbol]
                position.current_price = price
                
                # Calculer le P&L non réalisé
                if position.position_type == PositionType.LONG:
                    position.unrealized_pnl = (price - position.entry_price) * position.quantity
                else:
                    position.unrealized_pnl = (position.entry_price - price) * position.quantity
        
        # Mettre à jour l'historique des valeurs
        current_value = self.get_portfolio_value()
        self.value_history.append((datetime.now(), current_value))
        
        # Limiter l'historique (garder 1000 points)
        if len(self.value_history) > 1000:
            self.value_history = self.value_history[-500:]
        
        # Mettre à jour le pic et le drawdown
        if current_value > self.peak_value:
            self.peak_value = current_value
        
        current_drawdown = (self.peak_value - current_value) / self.peak_value
        if current_drawdown > self.max_drawdown:
            self.max_drawdown = current_drawdown
    
    def get_portfolio_value(self) -> float:
        """Calcule la valeur totale du portefeuille"""
        total_value = self.current_capital
        
        for position in self.positions.values():
            total_value += position.market_value
        
        return total_value
    
    def get_total_exposure(self) -> float:
        """Calcule l'exposition totale du portefeuille"""
        total_exposure = 0.0
        
        for position in self.positions.values():
            total_exposure += position.market_value
        
        return total_exposure
    
    def get_risk_metrics(self) -> RiskMetrics:
        """Calcule les métriques de risque actuelles"""
        portfolio_value = self.get_portfolio_value()
        total_exposure = self.get_total_exposure()
        
        # P&L non réalisé total
        unrealized_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())
        
        # P&L réalisé total
        realized_pnl = sum(pos.realized_pnl for pos in self.closed_positions)
        realized_pnl += sum(pos.realized_pnl for pos in self.positions.values())
        
        # Drawdown actuel
        current_drawdown = (self.peak_value - portfolio_value) / self.peak_value if self.peak_value > 0 else 0
        
        # Score de risque (0-100, 100 = risque max)
        risk_score = self._calculate_risk_score()
        
        # Ratio de levier
        leverage_ratio = total_exposure / portfolio_value if portfolio_value > 0 else 0
        
        # Risque de concentration (position la plus importante en %)
        if self.positions:
            max_position_value = max(pos.market_value for pos in self.positions.values())
            concentration_risk = (max_position_value / portfolio_value) * 100 if portfolio_value > 0 else 0
        else:
            concentration_risk = 0
        
        return RiskMetrics(
            total_value=portfolio_value,
            total_exposure=total_exposure,
            cash_available=self.current_capital,
            unrealized_pnl=unrealized_pnl,
            realized_pnl=realized_pnl,
            daily_pnl=self.daily_pnl,
            max_drawdown=self.max_drawdown * 100,
            current_drawdown=current_drawdown * 100,
            risk_score=risk_score,
            leverage_ratio=leverage_ratio,
            concentration_risk=concentration_risk
        )
    
    def _calculate_risk_score(self) -> float:
        """Calcule un score de risque global (0-100)"""
        score = 0
        
        # Exposition (0-30 points)
        exposure_ratio = self.get_total_exposure() / self.get_portfolio_value()
        max_exposure = self.risk_params['max_total_exposure']
        score += min(30, (exposure_ratio / max_exposure) * 30)
        
        # Concentration (0-25 points)
        if self.positions:
            max_position = max(pos.market_value for pos in self.positions.values())
            concentration = max_position / self.get_portfolio_value()
            max_concentration = self.risk_params['max_position_size']
            score += min(25, (concentration / max_concentration) * 25)
        
        # Drawdown (0-25 points)
        current_drawdown = (self.peak_value - self.get_portfolio_value()) / self.peak_value
        max_dd_limit = self.risk_params['max_drawdown_limit']
        score += min(25, (current_drawdown / max_dd_limit) * 25)
        
        # Volatilité (0-20 points)
        volatility_score = self._calculate_volatility_score()
        score += min(20, volatility_score)
        
        return min(100, score)
    
    def _calculate_volatility_score(self) -> float:
        """Calcule le score de volatilité basé sur l'historique"""
        if len(self.value_history) < 10:
            return 0
        
        # Calculer les rendements
        values = [v[1] for v in self.value_history[-30:]]  # 30 derniers points
        returns = []
        
        for i in range(1, len(values)):
            ret = (values[i] - values[i-1]) / values[i-1]
            returns.append(ret)
        
        if not returns:
            return 0
        
        # Volatilité (écart-type des rendements)
        volatility = np.std(returns) * 100  # En pourcentage
        
        # Score basé sur la volatilité (plus de 5% = score max)
        return min(20, (volatility / 5) * 20)
    
    def _check_daily_loss_limit(self) -> bool:
        """Vérifie si la limite de perte quotidienne est respectée"""
        # Réinitialiser si nouveau jour
        today = datetime.now().date()
        if today != self.last_reset_date:
            self.daily_pnl = 0
            self.last_reset_date = today
        
        max_loss = self.get_portfolio_value() * self.risk_params['max_daily_loss']
        return self.daily_pnl > -max_loss
    
    def _check_drawdown_limit(self) -> bool:
        """Vérifie si la limite de drawdown est respectée"""
        current_drawdown = (self.peak_value - self.get_portfolio_value()) / self.peak_value
        return current_drawdown < self.risk_params['max_drawdown_limit']
    
    def get_portfolio_summary(self) -> Dict[str, Any]:
        """Retourne un résumé complet du portefeuille"""
        risk_metrics = self.get_risk_metrics()
        
        return {
            'portfolio_value': risk_metrics.total_value,
            'initial_capital': self.initial_capital,
            'total_return': ((risk_metrics.total_value / self.initial_capital) - 1) * 100,
            'cash_available': risk_metrics.cash_available,
            'total_exposure': risk_metrics.total_exposure,
            'positions_count': len(self.positions),
            'risk_level': self.risk_level.value,
            'risk_score': risk_metrics.risk_score,
            'daily_pnl': risk_metrics.daily_pnl,
            'unrealized_pnl': risk_metrics.unrealized_pnl,
            'realized_pnl': risk_metrics.realized_pnl,
            'max_drawdown': risk_metrics.max_drawdown,
            'current_drawdown': risk_metrics.current_drawdown,
            'leverage_ratio': risk_metrics.leverage_ratio,
            'concentration_risk': risk_metrics.concentration_risk,
            'positions': {
                symbol: {
                    'type': pos.position_type.value,
                    'quantity': pos.quantity,
                    'entry_price': pos.entry_price,
                    'current_price': pos.current_price,
                    'market_value': pos.market_value,
                    'unrealized_pnl': pos.unrealized_pnl,
                    'pnl_percentage': pos.pnl_percentage
                }
                for symbol, pos in self.positions.items()
            }
        }

# Instance globale
portfolio_manager = PortfolioManager(initial_capital=1000, risk_level=RiskLevel.MODERATE)
