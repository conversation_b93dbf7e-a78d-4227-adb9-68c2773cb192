# ⚖️ Système de Gestion des Risques Avancé

## 🎯 Vue d'ensemble

Le système de gestion des risques de botCrypto fournit une approche complète et sophistiquée pour protéger le capital et optimiser les performances. Il combine position sizing dynamique, stop-loss adaptatifs, analyse de corrélations et monitoring en temps réel.

## 🏗️ Architecture

```
risk_management/
├── __init__.py              # Module principal
├── portfolio_manager.py     # Gestion du portefeuille et allocation
├── stop_loss_manager.py     # Stop-loss adaptatifs et take-profit
├── correlation_manager.py   # Analyse de corrélations et diversification
├── risk_engine.py          # Moteur de risques intégré
└── README.md               # Cette documentation
```

## 🚀 Utilisation Rapide

### 1. Gestion de portefeuille basique

```python
from risk_management.portfolio_manager import portfolio_manager, PositionType

# Calculer la taille de position optimale
optimal_size = portfolio_manager.calculate_position_size('BTC/USDT', 50000)

# Ouvrir une position
success = portfolio_manager.open_position(
    symbol='BTC/USDT',
    position_type=PositionType.LONG,
    entry_price=50000,
    quantity=optimal_size,
    stop_loss=47500,
    take_profit=55000
)
```

### 2. Stop-loss adaptatifs

```python
from risk_management.stop_loss_manager import stop_loss_manager, StopLossConfig, StopLossType

# Configuration d'un stop-loss ATR
config = StopLossConfig(
    stop_type=StopLossType.ATR_BASED,
    initial_distance=0.05,
    atr_multiplier=2.0
)
stop_loss_manager.set_stop_loss_config('ETH/USDT', config)

# Calculer le stop-loss
stop_price = stop_loss_manager.calculate_stop_loss('ETH/USDT', 3000, 'LONG')
```

### 3. Évaluation des risques

```python
from risk_management.risk_engine import risk_engine

# Évaluer un trade
assessment = risk_engine.assess_trade_risk('ADA/USDT', 'buy', 100, 1.5)

if assessment.action == RiskAction.ALLOW:
    # Exécuter le trade
    pass
elif assessment.action == RiskAction.REDUCE_SIZE:
    # Réduire la taille
    pass
else:
    # Rejeter le trade
    pass
```

## 💼 Gestion de Portefeuille

### Niveaux de Risque

| Niveau | Max Position | Max Exposition | Stop-Loss | Drawdown Max |
|--------|--------------|----------------|-----------|--------------|
| **CONSERVATIVE** | 5% | 30% | 3% | 10% |
| **MODERATE** | 10% | 60% | 5% | 20% |
| **AGGRESSIVE** | 20% | 90% | 8% | 30% |

### Position Sizing

Le système calcule automatiquement la taille optimale basée sur :

- **Risque par trade** : Montant maximum à risquer
- **Distance du stop-loss** : Plus le stop est proche, plus la position peut être grande
- **Volatilité de l'actif** : Actifs plus volatiles = positions plus petites
- **Corrélations** : Réduction si corrélé avec positions existantes

```python
# Exemple de calcul de position
optimal_size = portfolio_manager.calculate_position_size(
    symbol='BTC/USDT',
    entry_price=50000,
    stop_loss=47500,  # -5%
    risk_amount=100   # Risquer 100 USDT
)
```

### Métriques de Risque

```python
risk_metrics = portfolio_manager.get_risk_metrics()

print(f"Score de risque: {risk_metrics.risk_score}/100")
print(f"Drawdown actuel: {risk_metrics.current_drawdown:.1f}%")
print(f"Exposition totale: {risk_metrics.total_exposure:.2f}")
print(f"Ratio de levier: {risk_metrics.leverage_ratio:.2f}")
```

## 🛑 Stop-Loss Adaptatifs

### Types de Stop-Loss

| Type | Description | Utilisation |
|------|-------------|-------------|
| **FIXED** | Distance fixe en % | Débutants, actifs stables |
| **TRAILING** | Suit le prix favorable | Tendances fortes |
| **ATR_BASED** | Basé sur la volatilité | Actifs volatiles |
| **VOLATILITY_BASED** | Écart-type des rendements | Adaptation dynamique |
| **SUPPORT_RESISTANCE** | Niveaux techniques | Trading technique |

### Configuration Avancée

```python
from risk_management.stop_loss_manager import StopLossConfig, StopLossType

# Stop-loss suiveur
trailing_config = StopLossConfig(
    stop_type=StopLossType.TRAILING,
    initial_distance=0.05,    # 5% initial
    trailing_distance=0.03,   # 3% de suivi
    min_distance=0.02,        # 2% minimum
    max_distance=0.1          # 10% maximum
)

# Stop-loss basé sur l'ATR
atr_config = StopLossConfig(
    stop_type=StopLossType.ATR_BASED,
    atr_multiplier=2.0,       # 2x ATR
    min_distance=0.02,
    max_distance=0.15
)
```

### Take-Profit Échelonnés

```python
from risk_management.stop_loss_manager import TakeProfitConfig, TakeProfitType

# Take-profit échelonné
tp_config = TakeProfitConfig(
    tp_type=TakeProfitType.SCALED,
    scale_levels=[0.05, 0.1, 0.15, 0.2]  # 5%, 10%, 15%, 20%
)

# Basé sur le ratio risque/récompense
rr_config = TakeProfitConfig(
    tp_type=TakeProfitType.RISK_REWARD,
    risk_reward_ratio=3.0  # 1:3 risque/récompense
)
```

## 📊 Analyse de Corrélations

### Calcul de Corrélations

```python
from risk_management.correlation_manager import correlation_manager

# Ajouter des données de prix
correlation_manager.add_price_data('BTC/USDT', 50000)
correlation_manager.add_price_data('ETH/USDT', 3000)

# Calculer la matrice de corrélations
correlation_matrix = correlation_manager.calculate_correlation_matrix()

# Identifier les corrélations élevées
high_correlations = correlation_manager.get_high_correlations(threshold=0.8)
```

### Score de Diversification

```python
portfolio_positions = {
    'BTC/USDT': 0.4,  # 40%
    'ETH/USDT': 0.3,  # 30%
    'ADA/USDT': 0.2,  # 20%
    'SOL/USDT': 0.1   # 10%
}

# Calculer le score (0-100)
score = correlation_manager.calculate_diversification_score(portfolio_positions)

# Obtenir des suggestions
suggestions = correlation_manager.suggest_diversification_improvements(portfolio_positions)
```

### Secteurs Crypto

Le système reconnaît automatiquement les secteurs :

- **Store of Value** : BTC
- **Smart Contracts** : ETH, ADA, SOL, AVAX
- **DeFi** : UNI, AAVE, SUSHI
- **Layer 2** : MATIC, ARBITRUM
- **Oracles** : LINK, BAND
- **Interoperability** : DOT, ATOM

## ⚖️ Moteur de Risques Intégré

### Évaluation Automatique

```python
from risk_management.risk_engine import risk_engine, RiskAction

# Évaluation complète d'un trade
assessment = risk_engine.assess_trade_risk(
    symbol='ETH/USDT',
    side='buy',
    quantity=1.0,
    price=3000
)

# Actions possibles
if assessment.action == RiskAction.ALLOW:
    print("✅ Trade autorisé")
elif assessment.action == RiskAction.REDUCE_SIZE:
    print(f"⚠️ Réduire à {assessment.max_position_size}")
elif assessment.action == RiskAction.REJECT:
    print("❌ Trade rejeté")
```

### Monitoring en Temps Réel

```python
# Démarrer le monitoring
risk_engine.start_monitoring(interval=60)  # Toutes les minutes

# Dashboard en temps réel
dashboard = risk_engine.get_risk_dashboard()

print(f"Score de santé: {dashboard['health_score']}/100")
print(f"Alertes actives: {dashboard['alert_counts']['total']}")

# Arrêter le monitoring
risk_engine.stop_monitoring()
```

### Alertes Automatiques

Le système génère automatiquement des alertes pour :

- **Score de risque élevé** (>80/100)
- **Drawdown excessif** (>15%)
- **Pertes quotidiennes** (>5%)
- **Corrélations élevées** (>85%)
- **Stop-loss déclenchés**
- **Concentration excessive** (>30% par actif)

## 🔧 Configuration Avancée

### Personnalisation des Seuils

```python
# Modifier les seuils d'alerte
risk_engine.alert_thresholds.update({
    'portfolio_risk_score': 70,      # Score de risque
    'daily_loss_percent': 3,         # Perte quotidienne
    'drawdown_percent': 10,          # Drawdown
    'correlation_threshold': 0.8,    # Corrélation
    'concentration_percent': 25      # Concentration
})
```

### Stratégies par Actif

```python
# Configuration spécifique pour BTC
btc_config = StopLossConfig(
    stop_type=StopLossType.TRAILING,
    initial_distance=0.05,
    trailing_distance=0.03
)

# Configuration pour altcoins
altcoin_config = StopLossConfig(
    stop_type=StopLossType.VOLATILITY_BASED,
    initial_distance=0.08,
    volatility_multiplier=1.5
)
```

## 📊 Métriques et Reporting

### Dashboard Complet

```python
dashboard = risk_engine.get_risk_dashboard()

# Informations disponibles
{
    'health_score': 85,              # Score de santé global
    'portfolio': {
        'total_value': 10500,
        'total_return': 5.0,
        'risk_score': 45,
        'current_drawdown': 2.3
    },
    'correlations': {
        'high_correlations_count': 2,
        'symbols_analyzed': 8
    },
    'stop_losses': {
        'active_stops': 5,
        'stops_triggered': 2
    },
    'recent_alerts': [...],
    'alert_counts': {
        'total': 3,
        'critical': 0,
        'high': 1
    }
}
```

### Rapports Personnalisés

```python
# Rapport de corrélations
corr_report = correlation_manager.get_correlation_report()

# Statistiques de stop-loss
stop_stats = stop_loss_manager.get_stop_loss_stats()

# Résumé du portefeuille
portfolio_summary = portfolio_manager.get_portfolio_summary()
```

## 🧪 Tests et Validation

### Exécuter les Exemples

```bash
# Exemples complets
python examples/risk_management_example.py
```

### Tests Unitaires

```bash
# Tests du système de risques
python -m pytest tests/test_risk_management.py -v
```

## 📝 Bonnes Pratiques

### 1. Position Sizing

```python
# ✅ Bon - Utiliser le calcul automatique
optimal_size = portfolio_manager.calculate_position_size(symbol, price, stop_loss)

# ❌ Mauvais - Taille fixe arbitraire
fixed_size = 0.1  # Ignore le risque
```

### 2. Stop-Loss

```python
# ✅ Bon - Stop adaptatif
stop_config = StopLossConfig(stop_type=StopLossType.ATR_BASED)

# ❌ Mauvais - Stop fixe pour tous les actifs
fixed_stop = entry_price * 0.95  # Ignore la volatilité
```

### 3. Diversification

```python
# ✅ Bon - Vérifier les corrélations
correlation_manager.check_correlation_alerts(portfolio_positions)

# ❌ Mauvais - Ignorer les corrélations
# Ouvrir des positions corrélées sans vérification
```

### 4. Monitoring

```python
# ✅ Bon - Monitoring continu
risk_engine.start_monitoring()

# ❌ Mauvais - Vérification manuelle sporadique
# Pas de surveillance automatique
```

## 🚨 Gestion des Urgences

### Arrêt d'Urgence

```python
# Déclencher l'arrêt d'urgence
portfolio_manager.emergency_stop = True

# Fermer toutes les positions
for symbol in list(portfolio_manager.positions.keys()):
    portfolio_manager.close_position(symbol, current_price)
```

### Réduction de Risque

```python
# Réduire toutes les positions de 50%
for symbol, position in portfolio_manager.positions.items():
    partial_quantity = position.quantity * 0.5
    portfolio_manager.close_position(symbol, current_price, partial_quantity)
```

---

**💡 Conseil** : Commencez avec le niveau CONSERVATIVE et augmentez progressivement selon votre expérience et tolérance au risque.
