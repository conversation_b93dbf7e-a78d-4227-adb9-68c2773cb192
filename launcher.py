#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 BotCrypto Launcher v2.0
Interface principale pour lancer tous les bots de trading
"""

import os
import sys
import subprocess
from pathlib import Path
from dotenv import load_dotenv

class BotLauncher:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.load_environment()
        
    def load_environment(self):
        """Charge les variables d'environnement"""
        env_file = self.project_root / ".env.local"
        if env_file.exists():
            load_dotenv(env_file)
            print("✅ Configuration chargée")
        else:
            print("❌ Fichier .env.local manquant")
            print("📝 Copiez .env.example vers .env.local et configurez vos clés")
            sys.exit(1)
    
    def check_configuration(self):
        """Vérifie la configuration"""
        required_keys = {
            "Production": ["safe_bot_PROD_API_KEY", "safe_bot_PROD_API_SECRET"],
            "Testnet": ["safe_bot_TEST_API_KEY", "safe_bot_TEST_API_SECRET"],
        }
        
        config_status = {}
        for env_type, keys in required_keys.items():
            config_status[env_type] = all(os.getenv(key) for key in keys)
        
        return config_status
    
    def show_menu(self):
        """Affiche le menu principal"""
        config_status = self.check_configuration()
        
        print("\n" + "="*70)
        print("🤖 BOTCRYPTO LAUNCHER v2.0")
        print("="*70)
        print("📊 Configuration:")
        for env_type, status in config_status.items():
            status_icon = "✅" if status else "❌"
            print(f"   {status_icon} {env_type}")
        
        print("\n🎯 Bots Disponibles:")
        print("1. ⚡ Tests Rapides - Interface simple pour tests express")
        print("2. 🎛️ Interface Avancée - Gestion complète du Safe Bot v2.0")
        print("3. 🎯 Sniper Bot - Achat rapide de nouveaux tokens")
        print("4. 📈 Grid Bot v2 - Trading avancé avec grille")
        print("5. 📊 Dashboard - Interface de monitoring")
        print("6. 🧪 Tests & Validation")
        print("0. ❌ Quitter")
        print("="*70)
    
    def launch_quick_tests(self):
        """Lance l'interface de tests rapides"""
        print("⚡ Lancement de l'interface de tests rapides...")
        script_path = self.project_root / "scripts" / "quick_test.py"
        subprocess.run([sys.executable, str(script_path)])
    
    def launch_advanced_interface(self):
        """Lance l'interface avancée du Safe Bot"""
        print("🎛️ Lancement de l'interface avancée...")
        script_path = self.project_root / "scripts" / "run_safe_bot.py"
        subprocess.run([sys.executable, str(script_path)])
    
    def launch_sniper_bot(self):
        """Lance le Sniper Bot"""
        print("🎯 Lancement du Sniper Bot...")
        script_path = self.project_root / "bots" / "sniper_bot_v2.py"
        subprocess.run([sys.executable, str(script_path)])
    
    def launch_grid_bot(self):
        """Lance le Grid Bot v2"""
        print("📈 Lancement du Grid Bot v2...")
        script_path = self.project_root / "bots" / "grid_bot_v2.py"
        subprocess.run([sys.executable, str(script_path)])
    
    def launch_dashboard(self):
        """Lance le dashboard de monitoring"""
        print("📊 Lancement du dashboard...")
        script_path = self.project_root / "dashboard" / "dashboard_manager.py"
        subprocess.run([sys.executable, str(script_path)])
    
    def launch_tests(self):
        """Lance les tests et validations"""
        print("🧪 Lancement des tests...")
        script_path = self.project_root / "tests" / "run_tests.py"
        subprocess.run([sys.executable, str(script_path)])
    
    def run(self):
        """Lance l'interface principale"""
        while True:
            self.show_menu()
            
            try:
                choice = input("\n🎯 Choisissez une option (0-6): ").strip()
                
                if choice == "0":
                    print("👋 Au revoir !")
                    break
                elif choice == "1":
                    self.launch_quick_tests()
                elif choice == "2":
                    self.launch_advanced_interface()
                elif choice == "3":
                    self.launch_sniper_bot()
                elif choice == "4":
                    self.launch_grid_bot()
                elif choice == "5":
                    self.launch_dashboard()
                elif choice == "6":
                    self.launch_tests()
                else:
                    print("❌ Option invalide")
                    
            except KeyboardInterrupt:
                print("\n👋 Au revoir !")
                break
            except Exception as e:
                print(f"❌ Erreur: {e}")

def main():
    """Fonction principale"""
    print("🚀 Initialisation du BotCrypto Launcher...")
    
    try:
        launcher = BotLauncher()
        launcher.run()
    except Exception as e:
        print(f"❌ Erreur fatale: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
