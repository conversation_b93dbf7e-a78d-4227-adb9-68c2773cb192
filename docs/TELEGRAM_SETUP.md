# 📱 Configuration des Notifications Telegram

Ce guide vous explique comment configurer les notifications Telegram pour recevoir des mises à jour automatiques de votre bot de trading.

## 🎯 Pourquoi utiliser Telegram ?

- ✅ **Suivi à distance** : Surveillez votre bot sans rester devant l'ordinateur
- ✅ **Tests longue durée** : Parfait pour les tests de 6h+ 
- ✅ **Notifications intelligentes** : Mises à jour toutes les 30min pour tests > 1h
- ✅ **Alertes d'erreur** : Notification immédiate en cas de problème
- ✅ **Résumé final** : Rapport complet à la fin du test

## 🚀 Configuration Rapide (5 minutes)

### Étape 1: Créer un Bot Telegram

1. **Ouvrez Telegram** et cherchez `@BotFather`
2. **Démarrez une conversation** avec BotFather
3. **Tapez** `/newbot`
4. **Choisissez un nom** pour votre bot (ex: "Mon Bot Crypto")
5. **Choisissez un username** (ex: "monbotcrypto_bot")
6. **Copiez le token** qui ressemble à : `123456789:ABCdefGHIjklMNOpqrsTUVwxyz`

### Étape 2: Obtenir votre Chat ID

1. **Démarrez une conversation** avec votre nouveau bot
2. **Envoyez un message** (ex: "Hello")
3. **Ouvrez ce lien** dans votre navigateur (remplacez TOKEN par votre token) :
   ```
   https://api.telegram.org/botTOKEN/getUpdates
   ```
4. **Cherchez "chat":{"id":** dans la réponse
5. **Copiez le numéro** qui suit (ex: 123456789)

### Étape 3: Configurer le Bot

Ajoutez ces lignes à votre fichier `.env.local` :

```bash
# Configuration Telegram
TELEGRAM_BOT_TOKEN=123456789:ABCdefGHIjklMNOpqrsTUVwxyz
TELEGRAM_CHAT_ID=123456789
```

### Étape 4: Tester la Configuration

```bash
# Test rapide des notifications
python -c "from utils.telegram_notifier import setup_telegram_notifications; setup_telegram_notifications()"
```

Si tout fonctionne, vous devriez recevoir un message de test !

## 🎮 Utilisation

### Tests Longue Durée avec Notifications

```bash
# Test de 6 heures avec notifications
python long_term_test.py --hours 6

# Test de 12 heures
python long_term_test.py --hours 12

# Test sans notifications Telegram
python long_term_test.py --hours 6 --no-telegram
```

### Fréquence des Notifications

- **Tests < 1h** : Notifications au début et à la fin
- **Tests ≥ 1h** : Notifications toutes les 30 minutes
- **Erreurs** : Notification immédiate

## 📊 Types de Notifications

### 🚀 Notification de Démarrage
```
🚀 BOT CRYPTO DÉMARRÉ

📅 Début: 31/07/2025 14:30:00
⏱️ Durée: 6h
💰 Mode: TESTNET
🎯 Stratégie: Grid Trading Optimisé

🔧 Paramètres:
• Grid Size: 15
• Grid Spacing: 50
• Order Size: 0.002
• Stop Loss: 2%

📊 Balance initiale: 128,419 USDT

🔔 Notifications toutes les 30min pour les tests > 1h
```

### 📈 Mise à Jour Périodique
```
📈 MISE À JOUR BOT (2h30m)

💰 Performance:
• Balance: 127,850.45 USDT
• P&L: -568.55 USDT (-0.44%)
• Trades: 45
• Win Rate: 88.9%

📊 Trades:
• Meilleur: +12.50 USDT
• Pire: -8.20 USDT

⏰ Prochaine update: 17:00
```

### 🎉 Notification de Fin
```
🎉 TEST TERMINÉ (6h00m)

📊 RÉSULTATS FINAUX:
• Total Trades: 89
• Trades Gagnants: 78
• Win Rate: 87.6%
• P&L Total: +245.80 USDT (+0.19%)

📈 Métriques:
• Profit Factor: 1.24
• Sharpe Ratio: 0.156
• Temps d'exécution moyen: 0.24s

🎯 Recommandation: ✅ Stratégie viable pour production
```

### 🚨 Notification d'Erreur
```
🚨 ERREUR BOT

❌ Problème détecté:
Connexion API Binance interrompue

⏰ Heure: 31/07/2025 16:45:23

🔧 Action requise: Vérifiez les logs et redémarrez si nécessaire
```

## 🔧 Configuration Avancée

### Personnaliser l'Intervalle de Notification

```python
# Dans votre script
from utils.telegram_notifier import TelegramNotifier

notifier = TelegramNotifier()
notifier.set_notification_interval(15)  # 15 minutes au lieu de 30
```

### Notifications de Trades Exceptionnels

Le système envoie automatiquement une notification pour les trades avec un profit > 1%.

### Désactiver Certaines Notifications

```python
# Modifier dans utils/telegram_notifier.py
def send_periodic_update(self, current_stats):
    # Commenter cette ligne pour désactiver les mises à jour périodiques
    # self.send_message(message)
    pass
```

## 🛠️ Dépannage

### ❌ "Configuration Telegram manquante"
- Vérifiez que `TELEGRAM_BOT_TOKEN` et `TELEGRAM_CHAT_ID` sont dans `.env.local`
- Assurez-vous qu'il n'y a pas d'espaces avant/après les valeurs

### ❌ "Erreur connexion Telegram"
- Vérifiez que le token est correct
- Testez le bot avec BotFather : `/mybots` → Votre bot → `API Token`

### ❌ "Erreur envoi message"
- Vérifiez que le Chat ID est correct
- Assurez-vous d'avoir envoyé au moins un message au bot

### ❌ Messages non reçus
- Vérifiez que vous n'avez pas bloqué le bot
- Redémarrez la conversation avec `/start`

## 📱 Exemple Complet

```bash
# 1. Configuration dans .env.local
echo "TELEGRAM_BOT_TOKEN=votre_token_ici" >> .env.local
echo "TELEGRAM_CHAT_ID=votre_chat_id_ici" >> .env.local

# 2. Test de la configuration
python -c "from utils.telegram_notifier import setup_telegram_notifications; setup_telegram_notifications()"

# 3. Lancement d'un test de 6h avec notifications
python long_term_test.py --hours 6

# 4. Surveillance à distance via Telegram !
```

## 🎯 Conseils d'Utilisation

1. **Tests de nuit** : Parfait pour lancer un test de 8h avant de dormir
2. **Tests de week-end** : Idéal pour des tests de 48h+ 
3. **Surveillance mobile** : Recevez les updates où que vous soyez
4. **Historique** : Gardez un historique de tous vos tests dans Telegram

---

**🚀 Prêt à trader en toute sérénité !** Vos notifications Telegram vous permettront de surveiller votre bot 24/7 sans stress.
