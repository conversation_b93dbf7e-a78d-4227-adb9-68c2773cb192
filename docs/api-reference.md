# 📡 API Reference

## 🎯 Vue d'ensemble

L'API REST de BotCrypto fournit un accès complet à toutes les fonctionnalités du système : monitoring, configuration, contrôle des bots et analytics.

**Base URL :** `http://localhost:8080/api`

## 🔐 Authentification

### API Key (Recommandé)

```bash
# Header d'authentification
Authorization: Bearer YOUR_API_KEY

# Exemple
curl -H "Authorization: Bearer abc123..." http://localhost:8080/api/summary
```

### Configuration

```json
{
  "api": {
    "enable_auth": true,
    "api_keys": ["your-secure-api-key"],
    "rate_limit_per_minute": 60
  }
}
```

## 📊 Endpoints Principaux

### Health Check

#### GET /api/health

Vérification de l'état de l'API.

**Réponse :**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "version": "1.0.0",
  "uptime_seconds": 3600,
  "components": {
    "database": "healthy",
    "blockchain": "healthy",
    "bots": "healthy"
  }
}
```

### Résumé Global

#### GET /api/summary

Résumé complet du système.

**Réponse :**
```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "system": {
    "portfolio_value": 25000.50,
    "daily_pnl": 150.25,
    "active_bots": 3,
    "health_score": 0.95
  },
  "bots": {
    "scalping_bot_1": {
      "type": "dex_scalping",
      "status": "running",
      "total_trades": 45,
      "win_rate": 78.5,
      "total_pnl": 1250.75
    }
  },
  "alerts": [
    {
      "id": "alert_123",
      "level": "warning",
      "title": "High CPU usage",
      "timestamp": "2024-01-15T10:25:00Z"
    }
  ]
}
```

## 🤖 Gestion des Bots

### Liste des Bots

#### GET /api/bots

Liste tous les bots enregistrés.

**Paramètres :**
- `status` (optionnel) : Filtrer par statut (`running`, `stopped`, `error`)
- `type` (optionnel) : Filtrer par type (`dex_scalping`, `copy_trading`, etc.)

**Réponse :**
```json
{
  "bots": [
    {
      "id": "scalping_bot_1",
      "type": "dex_scalping",
      "status": "running",
      "start_time": "2024-01-15T08:00:00Z",
      "total_trades": 45,
      "total_pnl": 1250.75,
      "win_rate": 78.5,
      "last_activity": "2024-01-15T10:29:30Z"
    }
  ],
  "total_count": 1,
  "running_count": 1,
  "stopped_count": 0
}
```

### Détails d'un Bot

#### GET /api/bots/{bot_id}

Informations détaillées sur un bot spécifique.

**Réponse :**
```json
{
  "id": "scalping_bot_1",
  "type": "dex_scalping",
  "status": "running",
  "start_time": "2024-01-15T08:00:00Z",
  "uptime_seconds": 9000,
  "configuration": {
    "target_pairs": ["ETH/USDC", "WBTC/USDC"],
    "min_profit_usd": 20.0,
    "max_position_size_usd": 5000.0
  },
  "performance": {
    "total_trades": 45,
    "successful_trades": 35,
    "failed_trades": 10,
    "win_rate": 77.8,
    "total_pnl": 1250.75,
    "daily_pnl": 150.25,
    "average_profit_per_trade": 35.73,
    "largest_win": 125.50,
    "largest_loss": -45.20,
    "sharpe_ratio": 1.85
  },
  "current_positions": [
    {
      "symbol": "ETH/USDC",
      "size": 1.5,
      "entry_price": 2000.0,
      "current_price": 2050.0,
      "unrealized_pnl": 75.0
    }
  ],
  "recent_trades": [
    {
      "timestamp": "2024-01-15T10:25:00Z",
      "symbol": "WBTC/USDC",
      "side": "buy",
      "size": 0.1,
      "price": 45000.0,
      "pnl": 25.50
    }
  ]
}
```

### Contrôle des Bots

#### POST /api/bots/{bot_id}/start

Démarre un bot.

**Réponse :**
```json
{
  "success": true,
  "message": "Bot started successfully",
  "bot_id": "scalping_bot_1",
  "status": "running"
}
```

#### POST /api/bots/{bot_id}/stop

Arrête un bot.

**Réponse :**
```json
{
  "success": true,
  "message": "Bot stopped successfully",
  "bot_id": "scalping_bot_1",
  "status": "stopped"
}
```

#### POST /api/bots/{bot_id}/restart

Redémarre un bot.

#### PUT /api/bots/{bot_id}/config

Met à jour la configuration d'un bot.

**Corps de la requête :**
```json
{
  "min_profit_usd": 25.0,
  "max_position_size_usd": 8000.0,
  "scan_interval_seconds": 8
}
```

## 📈 Métriques et Analytics

### Métriques Système

#### GET /api/system

Métriques système globales.

**Réponse :**
```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "portfolio": {
    "total_value": 25000.50,
    "available_balance": 15000.25,
    "total_positions": 5,
    "total_unrealized_pnl": 125.75
  },
  "performance": {
    "daily_pnl": 150.25,
    "weekly_pnl": 850.50,
    "monthly_pnl": 2500.75,
    "total_trades_today": 25,
    "average_win_rate": 75.5
  },
  "system": {
    "cpu_usage": 45.2,
    "memory_usage": 62.8,
    "disk_usage": 35.1,
    "network_latency_ms": 150,
    "blockchain_sync_status": "synced"
  }
}
```

### Historique des Métriques

#### GET /api/metrics/{metric_name}

Historique d'une métrique spécifique.

**Paramètres :**
- `hours` : Nombre d'heures d'historique (défaut: 24)
- `interval` : Intervalle entre les points (défaut: 300 secondes)

**Exemple :** `/api/metrics/portfolio_value?hours=48&interval=3600`

**Réponse :**
```json
{
  "metric_name": "portfolio_value",
  "timeframe": {
    "start": "2024-01-13T10:30:00Z",
    "end": "2024-01-15T10:30:00Z",
    "hours": 48
  },
  "data_points": [
    {
      "timestamp": "2024-01-13T10:30:00Z",
      "value": 24500.25
    },
    {
      "timestamp": "2024-01-13T11:30:00Z",
      "value": 24650.50
    }
  ],
  "statistics": {
    "min": 24200.00,
    "max": 25100.75,
    "average": 24750.25,
    "change": 500.25,
    "change_percent": 2.04
  }
}
```

### Analytics de Performance

#### GET /api/analytics

Analytics avancées de performance.

**Paramètres :**
- `period` : Période d'analyse (`1d`, `7d`, `30d`, `90d`)
- `bot_id` : Analyser un bot spécifique (optionnel)

**Réponse :**
```json
{
  "period": "30d",
  "summary": {
    "total_return": 12.5,
    "sharpe_ratio": 1.85,
    "max_drawdown": 8.2,
    "win_rate": 75.5,
    "profit_factor": 2.1,
    "total_trades": 450,
    "average_trade_duration_hours": 4.5
  },
  "daily_performance": [
    {
      "date": "2024-01-15",
      "pnl": 150.25,
      "trades": 25,
      "win_rate": 80.0
    }
  ],
  "risk_metrics": {
    "var_95": 2.5,
    "expected_shortfall": 3.8,
    "volatility": 15.2,
    "beta": 0.85,
    "correlation_with_market": 0.65
  },
  "performance_attribution": {
    "dex_scalping": {
      "contribution_pnl": 800.50,
      "contribution_percent": 65.2
    },
    "copy_trading": {
      "contribution_pnl": 425.25,
      "contribution_percent": 34.8
    }
  }
}
```

## 🚨 Système d'Alertes

### Alertes Actives

#### GET /api/alerts

Liste des alertes actives.

**Paramètres :**
- `level` : Filtrer par niveau (`info`, `warning`, `critical`, `emergency`)
- `acknowledged` : Filtrer par statut d'acquittement (`true`, `false`)

**Réponse :**
```json
{
  "alerts": [
    {
      "id": "alert_123",
      "rule_id": "high_cpu_usage",
      "type": "system",
      "level": "warning",
      "title": "High CPU usage detected",
      "message": "CPU usage is 85.2%, above threshold of 80%",
      "value": 85.2,
      "threshold": 80.0,
      "bot_id": null,
      "timestamp": "2024-01-15T10:25:00Z",
      "acknowledged": false
    }
  ],
  "total_count": 1,
  "by_level": {
    "info": 0,
    "warning": 1,
    "critical": 0,
    "emergency": 0
  }
}
```

### Acquitter une Alerte

#### POST /api/alerts/{alert_id}/acknowledge

Acquitte une alerte.

**Réponse :**
```json
{
  "success": true,
  "message": "Alert acknowledged successfully",
  "alert_id": "alert_123",
  "acknowledged_at": "2024-01-15T10:30:00Z"
}
```

## 🔧 Configuration

### Configuration Globale

#### GET /api/config

Récupère la configuration globale.

#### PUT /api/config

Met à jour la configuration globale.

**Corps de la requête :**
```json
{
  "trading": {
    "max_position_size_pct": 8.0,
    "max_daily_loss_pct": 3.0
  },
  "monitoring": {
    "enable_notifications": true,
    "alert_cooldown_minutes": 10
  }
}
```

## 📊 Backtesting

### Lancer un Backtest

#### POST /api/backtest

Lance un nouveau backtest.

**Corps de la requête :**
```json
{
  "strategy_name": "scalping_eth_usdc",
  "start_date": "2024-01-01",
  "end_date": "2024-01-31",
  "initial_capital": 10000,
  "parameters": {
    "min_profit_usd": 20.0,
    "max_position_size_usd": 5000.0
  }
}
```

**Réponse :**
```json
{
  "backtest_id": "bt_123456",
  "status": "running",
  "estimated_completion": "2024-01-15T10:45:00Z"
}
```

### Résultats de Backtest

#### GET /api/backtest/{backtest_id}

Récupère les résultats d'un backtest.

**Réponse :**
```json
{
  "backtest_id": "bt_123456",
  "status": "completed",
  "strategy_name": "scalping_eth_usdc",
  "period": {
    "start_date": "2024-01-01",
    "end_date": "2024-01-31",
    "duration_days": 31
  },
  "results": {
    "total_return": 15.2,
    "sharpe_ratio": 2.1,
    "max_drawdown": 3.8,
    "win_rate": 78.0,
    "total_trades": 156,
    "profit_factor": 2.3,
    "final_capital": 11520.0
  },
  "trades": [
    {
      "timestamp": "2024-01-01T10:00:00Z",
      "symbol": "ETH/USDC",
      "side": "buy",
      "size": 1.0,
      "price": 2000.0,
      "pnl": 25.50
    }
  ],
  "equity_curve": [
    {
      "timestamp": "2024-01-01T00:00:00Z",
      "equity": 10000.0
    }
  ]
}
```

## 📈 WebSocket Events

### Connexion WebSocket

```javascript
const ws = new WebSocket('ws://localhost:8080/ws');

ws.onopen = function(event) {
    console.log('Connected to WebSocket');
};

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('Received:', data);
};
```

### Types d'Événements

#### Dashboard Update
```json
{
  "type": "dashboard_update",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "portfolio_value": 25000.50,
    "daily_pnl": 150.25,
    "active_bots": 3
  }
}
```

#### Trade Executed
```json
{
  "type": "trade_executed",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "bot_id": "scalping_bot_1",
    "symbol": "ETH/USDC",
    "side": "buy",
    "size": 1.0,
    "price": 2050.0,
    "pnl": 25.50
  }
}
```

#### Alert Triggered
```json
{
  "type": "alert_triggered",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "alert_id": "alert_123",
    "level": "warning",
    "title": "High CPU usage",
    "message": "CPU usage is 85.2%"
  }
}
```

## 🔒 Codes d'Erreur

| Code | Message | Description |
|------|---------|-------------|
| 400 | Bad Request | Paramètres invalides |
| 401 | Unauthorized | Authentification requise |
| 403 | Forbidden | Permissions insuffisantes |
| 404 | Not Found | Ressource non trouvée |
| 429 | Too Many Requests | Limite de taux dépassée |
| 500 | Internal Server Error | Erreur serveur |
| 503 | Service Unavailable | Service temporairement indisponible |

### Format d'Erreur

```json
{
  "error": {
    "code": 400,
    "message": "Invalid parameter",
    "details": "min_profit_usd must be greater than 0",
    "timestamp": "2024-01-15T10:30:00Z",
    "request_id": "req_123456"
  }
}
```

## 📝 Exemples d'Utilisation

### Python

```python
import requests

# Configuration
BASE_URL = "http://localhost:8080/api"
API_KEY = "your-api-key"
headers = {"Authorization": f"Bearer {API_KEY}"}

# Récupérer le résumé
response = requests.get(f"{BASE_URL}/summary", headers=headers)
summary = response.json()
print(f"Portfolio: ${summary['system']['portfolio_value']}")

# Démarrer un bot
response = requests.post(f"{BASE_URL}/bots/scalping_bot_1/start", headers=headers)
print(f"Bot started: {response.json()['success']}")
```

### JavaScript

```javascript
const API_BASE = 'http://localhost:8080/api';
const API_KEY = 'your-api-key';

const headers = {
    'Authorization': `Bearer ${API_KEY}`,
    'Content-Type': 'application/json'
};

// Récupérer les métriques
fetch(`${API_BASE}/system`, { headers })
    .then(response => response.json())
    .then(data => {
        console.log('Portfolio Value:', data.portfolio.total_value);
    });

// Mettre à jour la configuration
fetch(`${API_BASE}/bots/scalping_bot_1/config`, {
    method: 'PUT',
    headers,
    body: JSON.stringify({
        min_profit_usd: 25.0,
        max_position_size_usd: 8000.0
    })
})
.then(response => response.json())
.then(data => console.log('Config updated:', data.success));
```

---

**📚 Documentation complète :** Cette API reference couvre tous les endpoints disponibles. Pour des exemples plus détaillés, consultez les [guides d'utilisation](getting-started.md).
