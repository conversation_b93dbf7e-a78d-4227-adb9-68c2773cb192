# 🚀 Guide de Démarrage Rapide - Bot Optimisé

## 1. Configuration Initiale

1. **Configurez vos clés API dans .env.local** :
   ```
   safe_bot_TEST_API_KEY=votre_cle_testnet
   safe_bot_TEST_API_SECRET=votre_secret_testnet
   ```

2. **Obtenez vos clés API Binance Testnet** :
   - <PERSON><PERSON> sur https://testnet.binance.vision/
   - Créez un compte et générez des clés API
   - Copiez-les dans .env.local

## 2. Premier Test

```bash
# Test rapide de 1 heure
python quick_test.py --test-1h

# Interface complète
python run_optimized_bot.py
```

## 3. Analyse des Résultats

```bash
# Analyser les performances
python utils/performance_analyzer.py --charts
```

## 4. Déploiement Production

```bash
# Après tests réussis en testnet
python quick_test.py --deploy
```

## ⚠️ Important

- Testez TOUJOURS en testnet d'abord
- Ne déployez en production qu'avec des stratégies rentables
- Surveillez les logs régulièrement
- Commencez avec de petits montants

## 📞 Support

Consultez docs/OPTIMIZED_BOT_GUIDE.md pour plus de détails.
