# 📖 Guide de Démarrage

## 🎯 Introduction

Ce guide vous accompagne pas à pas pour installer, configurer et lancer votre premier bot de trading automatisé. En 15 minutes, vous aurez un système fonctionnel prêt à trader.

## 📋 Prérequis

### Système
- **Python 3.8+** (recommandé: Python 3.10)
- **Git** pour cloner le repository
- **4GB RAM** minimum (8GB recommandé)
- **Connexion internet stable**

### Connaissances
- Bases de Python
- Notions de trading crypto
- Compréhension des risques financiers

### Comptes Requis
- **Wallet Ethereum** avec ETH pour les frais de gas
- **Clé API Infura/Alchemy** pour l'accès blockchain
- **Compte Telegram** (optionnel, pour les notifications)

## 🚀 Installation

### 1. C<PERSON>r le Repository

```bash
# Cloner le projet
git clone https://github.com/JeremieN6/botCrypto.git
cd botCrypto

# Vérifier la structure
ls -la
```

### 2. Environnement Virtuel

```bash
# Créer l'environnement virtuel
python -m venv venv

# Activer l'environnement
# Sur Linux/macOS:
source venv/bin/activate

# Sur Windows:
venv\Scripts\activate

# Vérifier l'activation
which python  # Doit pointer vers venv/bin/python
```

### 3. Installation des Dépendances

```bash
# Mettre à jour pip
pip install --upgrade pip

# Installer les dépendances principales
pip install -r requirements.txt

# Vérifier l'installation
python -c "import web3; print('Web3 installé:', web3.__version__)"
python -c "import fastapi; print('FastAPI installé:', fastapi.__version__)"
```

### 4. Dépendances Optionnelles

```bash
# Pour les tests
pip install pytest pytest-asyncio pytest-cov

# Pour l'analyse de code
pip install flake8 black isort

# Pour les graphiques avancés
pip install plotly pandas numpy
```

## ⚙️ Configuration

### 1. Configuration Principale

```bash
# Copier le fichier de configuration d'exemple
cp config/config.example.json config/config.json

# Éditer la configuration
nano config/config.json  # ou votre éditeur préféré
```

### 2. Variables d'Environnement

Créer un fichier `.env` :

```bash
# Créer le fichier .env
cat > .env << EOF
# Blockchain
WEB3_PROVIDER_URL=https://mainnet.infura.io/v3/YOUR_INFURA_KEY
PRIVATE_KEY=your_private_key_here

# Notifications (optionnel)
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_CHAT_ID=your_telegram_chat_id

# Base de données (optionnel)
DATABASE_URL=sqlite:///botcrypto.db

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/botcrypto.log
EOF
```

### 3. Configuration Détaillée

Éditer `config/config.json` :

```json
{
  "trading": {
    "max_position_size_pct": 5.0,
    "max_daily_loss_pct": 2.0,
    "default_slippage_tolerance": 1.0,
    "enable_paper_trading": true
  },
  "monitoring": {
    "dashboard_port": 8080,
    "enable_notifications": true,
    "log_level": "INFO",
    "metrics_retention_hours": 24
  },
  "bots": {
    "dex_scalping": {
      "enabled": false,
      "min_profit_usd": 10.0,
      "max_position_size_usd": 1000.0,
      "scan_interval_seconds": 10,
      "target_pairs": ["ETH/USDC"]
    },
    "copy_trading": {
      "enabled": false,
      "default_copy_percentage": 1.0,
      "max_concurrent_copies": 3
    },
    "cross_chain_arbitrage": {
      "enabled": false,
      "min_profit_usd": 20.0,
      "monitored_chains": ["ethereum", "bsc"]
    }
  }
}
```

## 🧪 Premier Test

### 1. Vérification de l'Installation

```bash
# Tester la configuration
python -c "
from utils.config_manager import config_manager
print('Configuration chargée:', config_manager.get('trading.max_position_size_pct'))
"

# Tester la connexion blockchain
python -c "
from utils.web3_utils import get_web3_instance
w3 = get_web3_instance()
print('Connexion blockchain:', w3.is_connected())
print('Dernier bloc:', w3.eth.block_number)
"
```

### 2. Lancer le Dashboard

```bash
# Démarrer le dashboard
python dashboard/dashboard_manager.py

# Dans un autre terminal, vérifier l'accès
curl http://localhost:8080/api/health
```

Ouvrir http://localhost:8080 dans votre navigateur.

### 3. Paper Trading

```bash
# Activer le paper trading dans config.json
# "enable_paper_trading": true

# Lancer un exemple en mode simulation
python examples/paper_trading_example.py
```

## 🤖 Premier Bot

### 1. Bot de Scalping (Simulation)

```python
# Créer first_bot.py
from bots.dex_scalping.dex_scalping_bot import DexScalpingBot, ScalpingConfig
import asyncio

async def main():
    # Configuration conservative pour débuter
    config = ScalpingConfig(
        target_pairs=['ETH/USDC'],
        min_profit_usd=5.0,
        max_position_size_usd=100.0,
        max_slippage_percentage=0.5,
        scan_interval_seconds=30
    )
    
    # Créer le bot
    bot = DexScalpingBot(config)
    
    print("🤖 Démarrage du bot de scalping...")
    print("📊 Mode: Paper Trading")
    print("💰 Capital simulé: $10,000")
    
    try:
        # Démarrer le bot
        await bot.start()
    except KeyboardInterrupt:
        print("\n🛑 Arrêt demandé par l'utilisateur")
        await bot.stop()

if __name__ == "__main__":
    asyncio.run(main())
```

```bash
# Lancer le bot
python first_bot.py
```

### 2. Monitoring

Pendant que le bot tourne :

1. **Dashboard** : http://localhost:8080
2. **Logs** : `tail -f logs/botcrypto.log`
3. **Métriques** : http://localhost:8080/api/summary

## 📊 Validation

### 1. Tests Automatiques

```bash
# Tests rapides
python tests/run_tests.py --quick

# Vérifier que tout fonctionne
echo "✅ Tests passés avec succès"
```

### 2. Backtesting

```bash
# Lancer un backtest simple
python examples/backtesting_example.py
```

### 3. Métriques de Base

Vérifier dans le dashboard :
- ✅ Bot visible et actif
- ✅ Métriques mises à jour
- ✅ Aucune erreur critique
- ✅ Paper trading fonctionnel

## 🔧 Personnalisation

### 1. Ajuster les Paramètres

```json
{
  "trading": {
    "max_position_size_pct": 2.0,  // Plus conservateur
    "max_daily_loss_pct": 1.0,     // Limite stricte
    "default_slippage_tolerance": 0.5  // Moins de slippage
  }
}
```

### 2. Ajouter des Paires

```json
{
  "bots": {
    "dex_scalping": {
      "target_pairs": [
        "ETH/USDC",
        "WBTC/USDC",
        "LINK/USDC"
      ]
    }
  }
}
```

### 3. Notifications

```bash
# Configurer Telegram
# 1. Créer un bot avec @BotFather
# 2. Obtenir le token
# 3. Ajouter dans .env:
TELEGRAM_BOT_TOKEN=123456789:ABCdefGHIjklMNOpqrsTUVwxyz
TELEGRAM_CHAT_ID=123456789
```

## 🚨 Sécurité

### 1. Clés Privées

```bash
# ⚠️ JAMAIS dans le code source
# ✅ Toujours dans .env
# ✅ Permissions restrictives
chmod 600 .env

# ✅ Vérifier que .env est dans .gitignore
echo ".env" >> .gitignore
```

### 2. Limites de Sécurité

```json
{
  "trading": {
    "max_position_size_pct": 5.0,    // Max 5% par position
    "max_daily_loss_pct": 2.0,       // Arrêt si -2% par jour
    "enable_emergency_stop": true,   // Arrêt d'urgence
    "require_confirmation": false    // Pour le paper trading
  }
}
```

## 🎯 Prochaines Étapes

### 1. Apprentissage
- 📖 Lire [Configuration des Bots](bot-configuration.md)
- 📊 Étudier [Guide de Backtesting](backtesting-guide.md)
- 🛡️ Comprendre [Gestion des Risques](risk-management.md)

### 2. Optimisation
- 🔧 Ajuster les paramètres selon vos résultats
- 📈 Analyser les métriques de performance
- 🧪 Tester différentes stratégies

### 3. Production
- 🔒 Passer du paper trading au trading réel
- 📊 Surveiller les performances
- 🚨 Configurer les alertes

## ❓ Dépannage

### Problèmes Courants

**Erreur de connexion blockchain :**
```bash
# Vérifier la clé Infura
curl "https://mainnet.infura.io/v3/YOUR_KEY" \
  -X POST \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}'
```

**Port 8080 occupé :**
```bash
# Changer le port dans config.json
"dashboard_port": 8081

# Ou tuer le processus
lsof -ti:8080 | xargs kill -9
```

**Dépendances manquantes :**
```bash
# Réinstaller proprement
pip uninstall -r requirements.txt -y
pip install -r requirements.txt
```

### Support

- 📚 [Documentation complète](../README.md)
- 🐛 [Issues GitHub](https://github.com/JeremieN6/botCrypto/issues)
- 💬 [Discord Community](https://discord.gg/botcrypto)

---

**🎉 Félicitations !** Vous avez maintenant un système de trading automatisé fonctionnel. 

**⚠️ Important :** Restez en mode paper trading jusqu'à être complètement à l'aise avec le système.
