# 🤖 Guide de Configuration des Bots

## 🎯 Vue d'ensemble

Ce guide détaille la configuration de chaque type de bot, leurs paramètres optimaux et les meilleures pratiques pour maximiser les performances tout en minimisant les risques.

## 🏃‍♂️ Bot de Scalping DEX

### Configuration de Base

```python
from bots.dex_scalping.dex_scalping_bot import DexScalpingBot, ScalpingConfig

config = ScalpingConfig(
    # Paires à trader
    target_pairs=['ETH/USDC', 'WBTC/USDC'],
    
    # Profit minimum pour déclencher un trade
    min_profit_usd=20.0,
    
    # Taille maximale de position
    max_position_size_usd=5000.0,
    
    # Slippage maximum toléré
    max_slippage_percentage=2.0,
    
    # Intervalle de scan des opportunités
    scan_interval_seconds=5,
    
    # Nombre maximum de trades simultanés
    max_concurrent_trades=3,
    
    # DEX à surveiller
    monitored_dexes=['uniswap_v3', 'sushiswap', 'curve']
)
```

### Paramètres Avancés

```python
config = ScalpingConfig(
    # Configuration de base
    target_pairs=['ETH/USDC', 'WBTC/USDC', 'LINK/USDC'],
    min_profit_usd=15.0,
    max_position_size_usd=3000.0,
    
    # Gestion des risques
    max_daily_trades=50,
    max_consecutive_losses=5,
    stop_loss_percentage=3.0,
    
    # Optimisation des performances
    gas_price_multiplier=1.1,
    priority_fee_gwei=2.0,
    confirmation_blocks=1,
    
    # Filtres de qualité
    min_liquidity_usd=100000.0,
    max_price_impact=1.0,
    min_volume_24h=1000000.0,
    
    # Timing
    scan_interval_seconds=3,
    execution_timeout_seconds=30,
    cooldown_between_trades_seconds=10
)
```

### Stratégies de Scalping

#### 1. Scalping Conservateur
```python
conservative_config = ScalpingConfig(
    target_pairs=['ETH/USDC'],  # Une seule paire stable
    min_profit_usd=25.0,        # Profit minimum élevé
    max_position_size_usd=1000.0,  # Positions petites
    max_slippage_percentage=1.0,   # Slippage strict
    scan_interval_seconds=10,      # Scan moins fréquent
    max_concurrent_trades=1        # Un trade à la fois
)
```

#### 2. Scalping Agressif
```python
aggressive_config = ScalpingConfig(
    target_pairs=['ETH/USDC', 'WBTC/USDC', 'LINK/USDC', 'UNI/USDC'],
    min_profit_usd=10.0,        # Profit minimum bas
    max_position_size_usd=10000.0,  # Positions importantes
    max_slippage_percentage=3.0,    # Slippage tolérant
    scan_interval_seconds=2,        # Scan très fréquent
    max_concurrent_trades=5         # Plusieurs trades simultanés
)
```

## 🔄 Bot d'Arbitrage Cross-Chain

### Configuration de Base

```python
from bots.cross_chain_arbitrage.cross_chain_arbitrage_bot import CrossChainArbitrageBot, BotConfig

config = BotConfig(
    # Profit minimum pour l'arbitrage
    min_profit_usd=50.0,
    
    # Chaînes à surveiller
    monitored_chains=['ethereum', 'bsc', 'polygon'],
    
    # Tokens à arbitrer
    monitored_tokens=['USDT', 'USDC', 'DAI'],
    
    # Trades simultanés maximum
    max_concurrent_trades=2,
    
    # Intervalle de scan
    scan_interval_seconds=15,
    
    # Seuils de liquidité
    min_liquidity_usd=500000.0
)
```

### Configuration Avancée

```python
config = BotConfig(
    # Arbitrage
    min_profit_usd=30.0,
    min_profit_percentage=0.5,
    max_position_size_usd=20000.0,
    
    # Chaînes et tokens
    monitored_chains=['ethereum', 'bsc', 'polygon', 'arbitrum'],
    monitored_tokens=['USDT', 'USDC', 'DAI', 'BUSD'],
    
    # Gestion des risques
    max_bridge_time_minutes=30,
    max_gas_price_gwei=100,
    slippage_tolerance=2.0,
    
    # Performance
    scan_interval_seconds=10,
    max_concurrent_trades=3,
    execution_timeout_minutes=15,
    
    # Filtres
    min_liquidity_usd=1000000.0,
    max_price_age_seconds=60,
    min_volume_24h=5000000.0
)
```

### Stratégies d'Arbitrage

#### 1. Arbitrage Stable (Stablecoins)
```python
stable_config = BotConfig(
    monitored_tokens=['USDT', 'USDC', 'DAI'],
    min_profit_usd=20.0,
    min_profit_percentage=0.2,  # 0.2% minimum
    max_position_size_usd=50000.0,
    monitored_chains=['ethereum', 'bsc'],
    scan_interval_seconds=20
)
```

#### 2. Arbitrage Rapide
```python
fast_config = BotConfig(
    monitored_tokens=['USDT', 'USDC'],
    min_profit_usd=15.0,
    max_bridge_time_minutes=10,  # Bridges rapides seulement
    monitored_chains=['ethereum', 'polygon'],  # Chaînes rapides
    scan_interval_seconds=5,
    priority_fee_multiplier=1.5  # Exécution prioritaire
)
```

## 👥 Bot de Copy Trading

### Configuration de Base

```python
from bots.copy_trading.copy_trading_bot import CopyTradingBot, CopyTradingBotConfig

config = CopyTradingBotConfig(
    # Wallets à suivre
    tracked_wallets=[
        {
            'address': '******************************************',
            'label': 'Whale Trader #1',
            'min_trade_size': 1000.0,
            'copy_percentage': 2.0,
            'max_position_size': 5000.0
        }
    ],
    
    # Configuration globale
    default_copy_percentage=1.0,
    max_concurrent_copies=5,
    analysis_interval_minutes=5,
    
    # Filtres
    min_wallet_balance=100000.0,
    min_trade_size_usd=500.0,
    max_trade_age_minutes=10
)
```

### Configuration Avancée

```python
config = CopyTradingBotConfig(
    tracked_wallets=[
        {
            'address': '0xwhale1...',
            'label': 'DeFi Whale',
            'min_trade_size': 5000.0,
            'copy_percentage': 1.5,
            'max_position_size': 10000.0,
            'stop_loss_percentage': 5.0,
            'take_profit_percentage': 15.0,
            'enabled': True
        },
        {
            'address': '0xtrader2...',
            'label': 'Scalp Master',
            'min_trade_size': 1000.0,
            'copy_percentage': 3.0,
            'max_position_size': 3000.0,
            'copy_only_tokens': ['ETH', 'WBTC', 'LINK'],
            'enabled': True
        }
    ],
    
    # Gestion des risques
    max_total_exposure_usd=50000.0,
    max_correlation_threshold=0.7,
    diversification_min_wallets=3,
    
    # Performance
    analysis_interval_minutes=2,
    execution_delay_seconds=30,
    max_concurrent_copies=8,
    
    # Filtres avancés
    min_wallet_age_days=30,
    min_success_rate=60.0,
    min_profit_factor=1.5,
    max_drawdown_percentage=20.0
)
```

### Stratégies de Copy Trading

#### 1. Copy Trading Conservateur
```python
conservative_config = CopyTradingBotConfig(
    tracked_wallets=[
        {
            'address': '0xconservative...',
            'copy_percentage': 0.5,  # Très petit pourcentage
            'min_trade_size': 10000.0,  # Gros trades seulement
            'stop_loss_percentage': 3.0,  # Stop-loss strict
        }
    ],
    max_concurrent_copies=2,
    min_success_rate=75.0,  # Wallets très performants seulement
    analysis_interval_minutes=10
)
```

#### 2. Copy Trading Diversifié
```python
diversified_config = CopyTradingBotConfig(
    tracked_wallets=[
        {'address': '0xwhale1...', 'copy_percentage': 1.0, 'label': 'Large Cap'},
        {'address': '0xwhale2...', 'copy_percentage': 1.5, 'label': 'Mid Cap'},
        {'address': '0xwhale3...', 'copy_percentage': 0.8, 'label': 'DeFi Specialist'},
        {'address': '0xwhale4...', 'copy_percentage': 1.2, 'label': 'Arbitrage Expert'},
        {'address': '0xwhale5...', 'copy_percentage': 0.6, 'label': 'NFT Trader'}
    ],
    max_correlation_threshold=0.5,  # Faible corrélation
    diversification_min_wallets=5,
    max_concurrent_copies=10
)
```

## ⚙️ Configuration Globale

### Fichier config.json

```json
{
  "trading": {
    "max_position_size_pct": 10.0,
    "max_daily_loss_pct": 5.0,
    "default_slippage_tolerance": 2.0,
    "enable_paper_trading": false,
    "require_confirmation": false,
    "emergency_stop_loss_pct": 15.0
  },
  
  "risk_management": {
    "max_correlation": 0.8,
    "max_drawdown_pct": 20.0,
    "position_sizing_method": "kelly",
    "diversification_min_assets": 3,
    "rebalance_frequency_hours": 24
  },
  
  "monitoring": {
    "dashboard_port": 8080,
    "enable_notifications": true,
    "log_level": "INFO",
    "metrics_retention_hours": 168,
    "alert_cooldown_minutes": 15
  },
  
  "bots": {
    "dex_scalping": {
      "enabled": true,
      "config_file": "configs/dex_scalping.json"
    },
    "cross_chain_arbitrage": {
      "enabled": true,
      "config_file": "configs/arbitrage.json"
    },
    "copy_trading": {
      "enabled": false,
      "config_file": "configs/copy_trading.json"
    }
  }
}
```

## 🎯 Optimisation des Performances

### 1. Paramètres de Gas

```python
# Pour Ethereum mainnet
gas_config = {
    'gas_price_strategy': 'fast',
    'max_gas_price_gwei': 150,
    'gas_price_multiplier': 1.1,
    'priority_fee_gwei': 2.0
}

# Pour BSC
bsc_gas_config = {
    'gas_price_gwei': 5,
    'gas_limit_multiplier': 1.2
}

# Pour Polygon
polygon_gas_config = {
    'gas_price_gwei': 30,
    'gas_limit_multiplier': 1.1
}
```

### 2. Optimisation de la Latence

```python
performance_config = {
    'rpc_timeout_seconds': 10,
    'max_retries': 3,
    'retry_delay_seconds': 1,
    'connection_pool_size': 10,
    'keep_alive': True,
    'use_websocket': True
}
```

### 3. Gestion de la Mémoire

```python
memory_config = {
    'max_price_history_points': 1000,
    'cleanup_interval_minutes': 30,
    'max_log_file_size_mb': 100,
    'compress_old_logs': True
}
```

## 🛡️ Sécurité et Limites

### 1. Limites de Sécurité

```python
security_limits = {
    'max_position_size_usd': 50000,
    'max_daily_volume_usd': 200000,
    'max_trades_per_hour': 20,
    'max_gas_per_transaction': 500000,
    'require_manual_approval_above_usd': 10000
}
```

### 2. Circuit Breakers

```python
circuit_breakers = {
    'max_consecutive_losses': 10,
    'max_daily_loss_pct': 5.0,
    'min_account_balance_usd': 1000,
    'max_drawdown_pct': 15.0,
    'pause_on_high_gas': True,
    'max_gas_price_gwei': 200
}
```

## 📊 Monitoring et Alertes

### Configuration des Alertes

```python
alert_config = {
    'telegram': {
        'enabled': True,
        'bot_token': 'YOUR_BOT_TOKEN',
        'chat_id': 'YOUR_CHAT_ID',
        'alert_levels': ['WARNING', 'CRITICAL', 'EMERGENCY']
    },
    'email': {
        'enabled': False,
        'smtp_server': 'smtp.gmail.com',
        'smtp_port': 587,
        'username': '<EMAIL>',
        'to_email': '<EMAIL>'
    },
    'webhook': {
        'enabled': False,
        'url': 'https://hooks.slack.com/services/...',
        'format': 'slack'
    }
}
```

## 🔧 Maintenance et Mise à Jour

### 1. Configuration Dynamique

```python
# Mise à jour en temps réel
bot.update_config({
    'min_profit_usd': 25.0,
    'max_position_size_usd': 8000.0,
    'scan_interval_seconds': 8
})

# Sauvegarde automatique
bot.save_config('configs/current_config.json')
```

### 2. Rotation des Configurations

```python
# Configuration A/B testing
config_a = ScalpingConfig(min_profit_usd=20.0)
config_b = ScalpingConfig(min_profit_usd=15.0)

# Basculer selon les performances
if performance_a > performance_b:
    bot.switch_config(config_a)
else:
    bot.switch_config(config_b)
```

---

**💡 Conseil :** Commencez toujours avec des configurations conservatrices et augmentez progressivement l'agressivité selon vos résultats et votre tolérance au risque.
